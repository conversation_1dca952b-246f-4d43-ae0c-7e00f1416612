'use client';

import React from 'react';
import { BADGES } from '@/lib/profile-types';
import { Tooltip } from '@/components/Tooltip';

interface UserBadgesProps {
  badges: string[];
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showTitle?: boolean;
}

export default function UserBadges({
  badges,
  size = 'md',
  className = '',
  showTitle = true
}: UserBadgesProps) {
  if (!badges || badges.length === 0) {
    return null;
  }

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const badgeSizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10'
  };

  return (
    <div className={`${className}`}>
      {showTitle && <h3 className="text-sm font-medium text-gray-500 mb-2">Badges & Achievements</h3>}
      <div className="flex flex-wrap gap-2">
        {badges.map(badge => {
          const badgeInfo = BADGES[badge as keyof typeof BADGES];
          if (!badgeInfo) return null;
          
          return (
            <Tooltip key={badge} content={badgeInfo.description || ''}>
              <div className="flex flex-col items-center">
                <div className={`${badgeSizeClasses[size]} flex items-center justify-center rounded-full bg-yellow-50 border border-yellow-200 hover:bg-yellow-100 transition-colors cursor-help`}>
                  <span className={`${sizeClasses[size]}`}>{badgeInfo.icon || '🏆'}</span>
                </div>
                {showTitle && (
                  <span className={`mt-1 text-center ${sizeClasses.sm} text-gray-700`}>
                    {badgeInfo.name}
                  </span>
                )}
              </div>
            </Tooltip>
          );
        })}
      </div>
    </div>
  );
}
