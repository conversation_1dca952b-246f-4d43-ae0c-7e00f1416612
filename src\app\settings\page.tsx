'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/AuthProvider';
// Theme functionality removed
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface UserPreferences {
  email_notifications: boolean;
  show_activity_feed: boolean;
  content_language: string;
}

export default function SettingsPage() {
  const [preferences, setPreferences] = useState<UserPreferences>({
    email_notifications: true,
    show_activity_feed: true,
    content_language: 'en',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect if not logged in
    if (!authLoading && !user) {
      router.push('/auth/signin?redirect=/settings');
      return;
    }

    async function fetchUserPreferences() {
      if (!user) return;

      setLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          throw error;
        }

        if (data) {
          setPreferences({
            email_notifications: data.email_notifications !== false,
            show_activity_feed: data.show_activity_feed !== false,
            content_language: data.content_language || 'en',
          });
        }
      } catch (err: any) {
        console.error('Error fetching user preferences:', err);
        setError(err.message || 'Failed to fetch preferences');
      } finally {
        setLoading(false);
      }
    }

    fetchUserPreferences();
  }, [user, authLoading, router]);

  const handleToggleChange = (field: 'email_notifications' | 'show_activity_feed') => {
    setPreferences(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setPreferences(prev => ({ ...prev, content_language: e.target.value }));
  };

  const savePreferences = async () => {
    if (!user) return;

    setSaving(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('user_id')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        // Update existing preferences
        const { error: updateError } = await supabase
          .from('user_preferences')
          .update({
            theme: preferences.theme,
            email_notifications: preferences.email_notifications,
            show_activity_feed: preferences.show_activity_feed,
            content_language: preferences.content_language,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', user.id);

        if (updateError) throw updateError;
      } else {
        // Insert new preferences
        const { error: insertError } = await supabase
          .from('user_preferences')
          .insert({
            user_id: user.id,
            email_notifications: preferences.email_notifications,
            show_activity_feed: preferences.show_activity_feed,
            content_language: preferences.content_language,
          });

        if (insertError) throw insertError;
      }

      setSuccessMessage('Preferences saved successfully');
    } catch (err: any) {
      console.error('Error saving preferences:', err);
      setError(err.message || 'Failed to save preferences');
    } finally {
      setSaving(false);
    }
  };

  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Sign In Required
        </h1>
        <p className="text-gray-600 mb-6">
          You need to be signed in to access your settings.
        </p>
        <Link
          href="/auth/signin?redirect=/settings"
          className="inline-flex items-center px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
        >
          Sign In
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Settings</h1>

      {loading ? (
        <div className="flex justify-center items-center min-h-[40vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : (
        <div className="space-y-8">
          {error && (
            <div className="bg-red-50 text-red-700 p-4 rounded-md">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 text-green-700 p-4 rounded-md">
              {successMessage}
            </div>
          )}

          {/* Language Settings */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold mb-4 text-gray-900">Language</h2>

            <div className="space-y-4">


              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content Language
                </label>
                <select
                  value={preferences.content_language}
                  onChange={handleLanguageChange}
                  className="w-full p-2 border border-gray-300 rounded-md bg-white text-gray-900"
                >
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                </select>
                <p className="mt-1 text-sm text-gray-500">
                  This will be used for content recommendations and translations when available.
                </p>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold mb-4 text-gray-900">Notifications</h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Email Notifications</h3>
                  <p className="text-sm text-gray-500">
                    Receive email notifications for important updates and activity.
                  </p>
                </div>
                <button
                  onClick={() => handleToggleChange('email_notifications')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                    preferences.email_notifications ? 'bg-nature-green' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                      preferences.email_notifications ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Activity Feed</h3>
                  <p className="text-sm text-gray-500">
                    Show your activity in the community feed.
                  </p>
                </div>
                <button
                  onClick={() => handleToggleChange('show_activity_feed')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                    preferences.show_activity_feed ? 'bg-nature-green' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                      preferences.show_activity_feed ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={savePreferences}
              disabled={saving}
              className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
