'use client';

import React from 'react';
import Link from 'next/link';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export default function Logo({
  size = 'md',
  className = ''
}: LogoProps) {
  // Size mappings for different screen sizes
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12',
    xl: 'h-16'
  };

  return (
    <Link href="/" className={`block hover:opacity-90 transition-opacity ${className}`}>
      <img
        src="/images/nature-heals-logo.png"
        alt="NatureHeals.info Logo"
        className={`${sizeClasses[size]} w-auto object-contain`}
      />
    </Link>
  );
}
