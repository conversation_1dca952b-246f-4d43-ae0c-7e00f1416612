-- Check and create policies using DO block
DO $$
BEGIN
    -- Check and create insert policy
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can insert their own profile') THEN
        EXECUTE 'CREATE POLICY "Users can insert their own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id)';
        RAISE NOTICE 'Created INSERT policy for profiles table';
    ELSE
        RAISE NOTICE 'INSERT policy already exists for profiles table';
    END IF;

    -- Check and create update policy
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can update their own profile') THEN
        EXECUTE 'CREATE POLICY "Users can update their own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id)';
        RAISE NOTICE 'Created UPDATE policy for profiles table';
    ELSE
        RAISE NOTICE 'UPDATE policy already exists for profiles table';
    END IF;

    -- Check and create select policy for own profile
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can view their own profile') THEN
        EXECUTE 'CREATE POLICY "Users can view their own profile" ON public.profiles FOR SELECT USING (auth.uid() = id)';
        RAISE NOTICE 'Created SELECT (own profile) policy for profiles table';
    ELSE
        RAISE NOTICE 'SELECT (own profile) policy already exists for profiles table';
    END IF;

    -- Check and create select policy for public profiles
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can view public profiles') THEN
        EXECUTE 'CREATE POLICY "Users can view public profiles" ON public.profiles FOR SELECT USING (visibility = ''public'' OR auth.uid() = id)';
        RAISE NOTICE 'Created SELECT (public profiles) policy for profiles table';
    ELSE
        RAISE NOTICE 'SELECT (public profiles) policy already exists for profiles table';
    END IF;
END
$$;

-- Make sure RLS is enabled
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;
