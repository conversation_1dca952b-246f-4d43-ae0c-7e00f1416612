'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  useEffect(() => {
    const handleVerification = async () => {
      try {
        const token = searchParams.get('token');
        const type = searchParams.get('type');
        
        if (!token) {
          router.push('/auth/verify/error?error=No verification token provided');
          return;
        }
        
        const supabase = createClientComponentClient();
        
        // Verify the token
        const { error } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: type === 'signup' ? 'signup' : 'email',
        });
        
        if (error) {
          router.push(`/auth/verify/error?error=${encodeURIComponent(error.message)}`);
          return;
        }
        
        // Success - redirect to success page
        router.push('/auth/verify/success');
      } catch (err: any) {
        console.error('Error in verification:', err);
        router.push(`/auth/verify/error?error=${encodeURIComponent(err.message || 'An unexpected error occurred')}`);
      }
    };
    
    handleVerification();
  }, [router, searchParams]);
  
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Verifying Your Email
        </h2>
        <div className="mt-8 bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mb-4"></div>
            <p className="text-gray-600">Please wait while we verify your email...</p>
          </div>
        </div>
      </div>
    </div>
  );
}
