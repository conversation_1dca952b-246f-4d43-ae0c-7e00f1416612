import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';

// This is a simple secret to prevent unauthorized revalidation
// In production, you should use a more secure method
const REVALIDATE_SECRET = 'natureheals-revalidate';

export async function POST(request: NextRequest) {
  try {
    // Get the path to revalidate from the query parameters
    const path = request.nextUrl.searchParams.get('path');
    
    // Get the secret from the request body
    const body = await request.json();
    const { secret } = body;
    
    // Validate the secret
    if (secret !== REVALIDATE_SECRET) {
      return NextResponse.json(
        { error: 'Invalid revalidation secret' },
        { status: 401 }
      );
    }
    
    // Validate the path
    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      );
    }
    
    // Revalidate the path
    revalidatePath(path);
    
    // Also revalidate related paths
    if (path === '/categories') {
      // Revalidate individual category pages if the categories page is revalidated
      revalidatePath('/categories/[slug]');
      
      // Revalidate the homepage which might show categories
      revalidatePath('/');
    }
    
    return NextResponse.json(
      { revalidated: true, path },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error revalidating path:', error);
    return NextResponse.json(
      { error: 'Failed to revalidate path' },
      { status: 500 }
    );
  }
}
