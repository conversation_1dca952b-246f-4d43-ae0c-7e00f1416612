-- Add missing columns to the profiles table if they don't exist
DO $$
BEGIN
    -- Check and add avatar_type column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'avatar_type') THEN
        ALTER TABLE public.profiles ADD COLUMN avatar_type TEXT;
    END IF;

    -- Check and add preset_avatar column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'preset_avatar') THEN
        ALTER TABLE public.profiles ADD COLUMN preset_avatar TEXT;
    END IF;

    -- Check and add visibility column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'visibility') THEN
        ALTER TABLE public.profiles ADD COLUMN visibility TEXT DEFAULT 'public';
    END IF;

    -- Check and add show_email column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'show_email') THEN
        ALTER TABLE public.profiles ADD COLUMN show_email BOOLEAN DEFAULT false;
    END IF;

    -- Check and add show_location column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'show_location') THEN
        ALTER TABLE public.profiles ADD COLUMN show_location BOOLEAN DEFAULT false;
    END IF;

    -- Check and add location column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'location') THEN
        ALTER TABLE public.profiles ADD COLUMN location TEXT;
    END IF;

    -- Check and add interests column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'interests') THEN
        ALTER TABLE public.profiles ADD COLUMN interests TEXT[];
    END IF;

    -- Check and add expertise_level column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'expertise_level') THEN
        ALTER TABLE public.profiles ADD COLUMN expertise_level TEXT;
    END IF;

    -- Check and add healing_philosophy column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'healing_philosophy') THEN
        ALTER TABLE public.profiles ADD COLUMN healing_philosophy TEXT;
    END IF;

    -- Check and add favorite_herbs column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'favorite_herbs') THEN
        ALTER TABLE public.profiles ADD COLUMN favorite_herbs TEXT[];
    END IF;

    -- Check and add social_links column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'social_links') THEN
        ALTER TABLE public.profiles ADD COLUMN social_links JSONB;
    END IF;

    -- Check and add spirit_plant column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'spirit_plant') THEN
        ALTER TABLE public.profiles ADD COLUMN spirit_plant TEXT;
    END IF;

    -- Check and add badges column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'badges') THEN
        ALTER TABLE public.profiles ADD COLUMN badges TEXT[];
    END IF;
END
$$;
