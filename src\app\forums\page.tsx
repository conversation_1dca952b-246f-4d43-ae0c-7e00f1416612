'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { getForumCategories } from '@/lib/forums';
import { ForumCategory } from '@/lib/forum-types';
import Link from 'next/link';
import { motion as Motion, AnimatePresence } from 'framer-motion';
import {
  FaLeaf, FaApple,
  FaYinYang, FaBookMedical, FaMicroscope, FaQuestion
} from 'react-icons/fa';
import {
  FaRegComments, FaRegLightbulb, FaRegClock,
  FaRegUser, FaRegBookmark
} from 'react-icons/fa';
import { GiMeditation } from 'react-icons/gi';
import { HiOutlinePencilSquare } from 'react-icons/hi2';

// Helper function to get icon based on category slug
const getCategoryIcon = (slug: string) => {
  const iconMap: Record<string, React.ReactNode> = {
    'general-discussion': <FaRegComments className="text-nature-green" />,
    'herbal-remedies': <FaLeaf className="text-nature-green" />,
    'nutrition-diet': <FaApple className="text-nature-green" />,
    'mind-body-practices': <GiMeditation className="text-nature-green" />,
    'traditional-medicine': <FaYinYang className="text-nature-green" />,
    'success-stories': <FaRegLightbulb className="text-nature-green" />,
    'research-science': <FaMicroscope className="text-nature-green" />,
    'questions-help': <FaQuestion className="text-nature-green" />
  };

  return iconMap[slug] || <FaRegBookmark className="text-nature-green" />;
};

// Forum statistics for the header section
const forumStats = [
  { label: 'Categories', value: 8, icon: <FaRegBookmark /> },
  { label: 'Members', value: 1240, icon: <FaRegUser /> },
  { label: 'Topics', value: 356, icon: <FaRegComments /> },
  { label: 'Latest Activity', value: 'Today', icon: <FaRegClock /> }
];

export default function ForumsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalTopics, setTotalTopics] = useState(0);
  const [totalPosts, setTotalPosts] = useState(0);

  useEffect(() => {
    async function loadCategories() {
      setIsLoading(true);
      setError(null);

      try {
        console.log('Fetching forum categories...');
        const data = await getForumCategories();
        console.log('Forum categories data:', data);
        setCategories(data || []); // Ensure we always set an array

        // Calculate totals for statistics
        if (data && data.length > 0) {
          const topics = data.reduce((sum, category) => sum + (category.topic_count || 0), 0);
          const posts = data.reduce((sum, category) => sum + (category.post_count || 0), 0);
          setTotalTopics(topics);
          setTotalPosts(posts);
        }
      } catch (err: any) {
        console.error('Error in loadCategories:', err);
        setError(err.message || 'Failed to load forum categories');
      } finally {
        setIsLoading(false);
      }
    }

    loadCategories();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-nature-green to-nature-green-dark py-12 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <Motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-8"
            >
              <h1 className="text-3xl md:text-4xl font-bold mb-4">Community Forums</h1>
              <p className="text-lg md:text-xl max-w-2xl mx-auto opacity-90">
                Connect with our community, share experiences, and learn about holistic healing approaches
              </p>
            </Motion.div>

            {/* Stats Section */}
            <Motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                <FaRegBookmark className="mx-auto text-2xl mb-2" />
                <p className="text-sm uppercase tracking-wider">Categories</p>
                <p className="text-2xl font-bold">{categories.length}</p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                <FaRegUser className="mx-auto text-2xl mb-2" />
                <p className="text-sm uppercase tracking-wider">Members</p>
                <p className="text-2xl font-bold">1,240+</p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                <FaRegComments className="mx-auto text-2xl mb-2" />
                <p className="text-sm uppercase tracking-wider">Topics</p>
                <p className="text-2xl font-bold">{totalTopics}</p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                <FaRegLightbulb className="mx-auto text-2xl mb-2" />
                <p className="text-sm uppercase tracking-wider">Posts</p>
                <p className="text-2xl font-bold">{totalPosts}</p>
              </div>
            </Motion.div>

            {/* Create Topic Button */}
            {user && (
              <Motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.4 }}
                className="text-center"
              >
                <Link
                  href="/forums/create-topic"
                  className="inline-flex items-center px-6 py-3 bg-white text-nature-green-dark rounded-full font-medium shadow-lg hover:bg-gray-100 transition-colors"
                >
                  <HiOutlinePencilSquare className="mr-2" />
                  Create New Topic
                </Link>
              </Motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          {error && (
            <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
              <p className="font-medium">Error</p>
              <p>{error}</p>
            </div>
          )}

          {/* Categories Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Forum Categories</h2>

            {isLoading ? (
              <div className="bg-white rounded-xl shadow-md p-8 text-center">
                <div className="animate-pulse flex flex-col items-center">
                  <div className="h-12 w-12 bg-gray-200 rounded-full mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
                <p className="text-gray-500 mt-4">Loading forum categories...</p>
              </div>
            ) : categories.length === 0 ? (
              <div className="bg-white rounded-xl shadow-md p-8 text-center">
                <p className="text-gray-500">No forum categories found.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {categories.map((category, index) => (
                  <Motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Link href={`/forums/${category.slug}`} className="block h-full">
                      <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 border border-gray-100 h-full">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mr-4 text-3xl bg-nature-green/10 p-3 rounded-full">
                            {getCategoryIcon(category.slug)}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-nature-green transition-colors">
                              {category.name}
                            </h3>
                            {category.description && (
                              <p className="text-gray-600 mb-3 line-clamp-2">{category.description}</p>
                            )}
                            <div className="flex items-center text-sm text-gray-500 mt-auto">
                              <span className="mr-4 bg-gray-100 px-2 py-1 rounded-md">
                                <span className="font-medium text-gray-900">{category.topic_count || 0}</span> Topics
                              </span>
                              <span className="bg-gray-100 px-2 py-1 rounded-md">
                                <span className="font-medium text-gray-900">{category.post_count || 0}</span> Posts
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </Motion.div>
                ))}
              </div>
            )}
          </div>

          {/* Guidelines Section */}
          <Motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-8 bg-white rounded-xl shadow-md p-8 border border-gray-100"
          >
            <h2 className="text-xl font-bold mb-4 text-gray-800 flex items-center">
              <FaRegLightbulb className="mr-2 text-nature-green" />
              Forum Guidelines
            </h2>
            <div className="prose prose-sm max-w-none">
              <p className="text-gray-700">
                Welcome to the NatureHeals.info community forums! This is a space for discussing holistic healing,
                sharing experiences, and connecting with others interested in natural remedies.
              </p>
              <p className="text-gray-700">Please follow these guidelines to keep our community helpful and respectful:</p>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                <li className="flex items-start">
                  <span className="text-nature-green mr-2">•</span>
                  <span>Be respectful and kind to other members</span>
                </li>
                <li className="flex items-start">
                  <span className="text-nature-green mr-2">•</span>
                  <span>Stay on topic and post in the appropriate categories</span>
                </li>
                <li className="flex items-start">
                  <span className="text-nature-green mr-2">•</span>
                  <span>Share knowledge and experiences, but avoid making medical claims</span>
                </li>
                <li className="flex items-start">
                  <span className="text-nature-green mr-2">•</span>
                  <span>Do not promote or sell products or services</span>
                </li>
                <li className="flex items-start">
                  <span className="text-nature-green mr-2">•</span>
                  <span>Respect privacy and confidentiality</span>
                </li>
                <li className="flex items-start">
                  <span className="text-nature-green mr-2">•</span>
                  <span>Report inappropriate content to moderators</span>
                </li>
              </ul>
              <p className="text-gray-700 mt-4">
                Remember that the information shared in these forums is for educational purposes only and
                should not replace professional medical advice.
              </p>
            </div>
          </Motion.div>
        </div>
      </div>
    </div>
  );
}
