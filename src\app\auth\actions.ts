'use server';

import { createActionClient } from '@/lib/auth.server';
import { redirect } from 'next/navigation';
import { verifyRecaptcha } from '@/lib/recaptcha';
import { createServerClient } from '@supabase/ssr';

// Verify reCAPTCHA token
export async function verifyRecaptchaToken(token: string) {
  try {
    const isValid = await verifyRecaptcha(token);
    return { success: isValid };
  } catch (error) {
    console.error('Error verifying reCAPTCHA:', error);
    return { success: false, error: 'Failed to verify reCAPTCHA' };
  }
}

// Sign up a new user and create a profile with role
export async function signUp(email: string, password: string, username: string, fullName: string, recaptchaToken: string) {
  // Verify reCAPTCHA first
  const { success } = await verifyRecaptchaToken(recaptchaToken);
  if (!success) {
    return { error: { message: 'reCAPTCHA verification failed. Please try again.' } };
  }
  const supabase = await createActionClient();

  // Check if username already exists
  const { data: existingUser, error: userCheckError } = await supabase
    .from('profiles')
    .select('id')
    .eq('username', username)
    .single();

  if (userCheckError && userCheckError.code !== 'PGRST116') {
    // Not found is OK, any other error is not
    return { error: userCheckError };
  }
  if (existingUser) {
    return { error: { message: 'Username already exists' } };
  }

  // Create the user in Supabase Auth
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        username,
        full_name: fullName,
        role: 'user',
      },
      // Use the site URL directly - Supabase will append its verification parameters
      emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/auth/verify/client`,
    },
  });

  if (error) {
    return { error };
  }

  // Create profile record in the profiles table using service role key to bypass RLS
  if (data?.user) {
    // Create a service role client to bypass RLS policies
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!serviceRoleKey) {
      return { error: { message: 'Server configuration error' } };
    }

    const adminSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      serviceRoleKey,
      {
        cookies: {
          get: () => undefined,
          set: () => {},
          remove: () => {},
        },
      }
    );

    const { error: profileError } = await adminSupabase.from('profiles').insert({
      id: data.user.id,
      username,
      full_name: fullName,
      role: 'user',
    });

    if (profileError) {
      console.error('Error creating profile:', profileError);
      return { error: profileError };
    }
  }

  return { data };
}

// Sign in a user
export async function signIn(email: string, password: string) {
  const supabase = await createActionClient();

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return { error };
  }

  return { data };
}

// Sign out the current user
export async function signOut() {
  const supabase = await createActionClient();
  await supabase.auth.signOut();
  redirect('/');
}

// Get the current session (for SSR) - SECURE: fetch verified user
export async function getSession() {
  const supabase = await createActionClient();
  const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
  if (sessionError || !sessionData.session) {
    return { user: null, error: sessionError };
  }
  // Secure: fetch verified user from Supabase Auth server
  const { data: userData, error: userError } = await supabase.auth.getUser();
  if (userError) {
    return { user: null, error: userError };
  }
  return { user: userData.user };
}