-- WARNING: This script completely disables <PERSON><PERSON> for the forum_topics table
-- This is a last resort solution and should be replaced with proper RLS policies later

-- Disable R<PERSON> on forum_topics table
ALTER TABLE public.forum_topics DISABLE ROW LEVEL SECURITY;

-- Grant all privileges to authenticated users
GRANT ALL PRIVILEGES ON TABLE public.forum_topics TO authenticated;
GRANT ALL PRIVILEGES ON TABLE public.forum_posts TO authenticated;

-- Verify R<PERSON> is disabled
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND tablename = 'forum_topics';
