'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';
import { useRouter } from 'next/navigation';

interface BookmarkedArticle {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  created_at: string;
  bookmark_id: string;
  bookmark_date: string;
  author: {
    username: string;
    full_name?: string;
  };
  category?: {
    name: string;
    slug: string;
  };
}

export default function BookmarksPage() {
  const [bookmarks, setBookmarks] = useState<BookmarkedArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect if not logged in
    if (!authLoading && !user) {
      router.push('/auth/signin?redirect=/bookmarks');
      return;
    }

    async function fetchBookmarks() {
      if (!user) return;

      setLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('bookmarks')
          .select(`
            id,
            created_at,
            articles:article_id (
              id,
              title,
              slug,
              excerpt,
              created_at,
              profiles:author_id (username, full_name),
              categories:category_id (name, slug)
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        // Transform the data structure
        const formattedBookmarks = data.map(bookmark => ({
          id: bookmark.articles.id,
          title: bookmark.articles.title,
          slug: bookmark.articles.slug,
          excerpt: bookmark.articles.excerpt,
          created_at: bookmark.articles.created_at,
          bookmark_id: bookmark.id,
          bookmark_date: bookmark.created_at,
          author: bookmark.articles.profiles,
          category: bookmark.articles.categories
        }));

        setBookmarks(formattedBookmarks);
      } catch (err: any) {
        console.error('Error fetching bookmarks:', err);
        setError(err.message || 'Failed to fetch bookmarks');
      } finally {
        setLoading(false);
      }
    }

    fetchBookmarks();
  }, [user, authLoading, router]);

  const removeBookmark = async (bookmarkId: string) => {
    try {
      const { error } = await supabase
        .from('bookmarks')
        .delete()
        .eq('id', bookmarkId);

      if (error) {
        throw error;
      }

      // Update the UI by removing the bookmark
      setBookmarks(prev => prev.filter(bookmark => bookmark.bookmark_id !== bookmarkId));
    } catch (err: any) {
      console.error('Error removing bookmark:', err);
      // You could show an error toast here
    }
  };

  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Your Bookmarks</h1>

      {loading ? (
        <div className="flex justify-center items-center min-h-[40vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-md">
          {error}
        </div>
      ) : bookmarks.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
          <h2 className="text-xl font-semibold mt-4">No bookmarks yet</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2 mb-6">
            Save articles to read later by clicking the bookmark icon.
          </p>
          <Link
            href="/wiki"
            className="inline-flex items-center px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
          >
            Explore Articles
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          {bookmarks.map((bookmark) => (
            <div key={bookmark.bookmark_id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start">
                <div>
                  <Link href={`/wiki/${bookmark.slug}`} className="text-xl font-semibold text-gray-900 dark:text-gray-100 hover:text-nature-green dark:hover:text-nature-green transition-colors">
                    {bookmark.title}
                  </Link>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1 space-x-2">
                    <span>By {bookmark.author.full_name || bookmark.author.username}</span>
                    <span>•</span>
                    <span>Saved on {formatDate(bookmark.bookmark_date)}</span>
                    {bookmark.category && (
                      <>
                        <span>•</span>
                        <Link href={`/categories/${bookmark.category.slug}`} className="text-nature-green hover:underline">
                          {bookmark.category.name}
                        </Link>
                      </>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => removeBookmark(bookmark.bookmark_id)}
                  className="text-gray-400 hover:text-red-500 transition-colors"
                  aria-label="Remove bookmark"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              {bookmark.excerpt && (
                <p className="mt-3 text-gray-600 dark:text-gray-300">
                  {bookmark.excerpt}
                </p>
              )}
              <div className="mt-4">
                <Link
                  href={`/wiki/${bookmark.slug}`}
                  className="inline-flex items-center text-nature-green hover:text-nature-green-dark dark:hover:text-green-400 font-medium"
                >
                  Read Article
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
