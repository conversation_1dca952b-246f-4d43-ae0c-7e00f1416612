import { NextRequest, NextResponse } from 'next/server';

export function GET(request: NextRequest) {
  // If there's a hash in the URL, redirect to the client callback page
  const url = new URL(request.url);
  const hash = url.hash;
  
  if (hash) {
    return NextResponse.redirect(new URL(`/auth/callback-client${hash}`, request.url));
  }
  
  // Otherwise, let the normal callback route handle it
  return NextResponse.redirect(new URL('/auth/callback', request.url));
}
