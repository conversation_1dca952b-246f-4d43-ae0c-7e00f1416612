'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { createClient } from '@/lib/supabase';
import { checkRateLimit, formatRateLimitMessage } from '@/lib/rate-limiting';

interface ReportContentModalProps {
  contentType: 'topic' | 'post' | 'article' | 'comment';
  contentId: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function ReportContentModal({
  contentType,
  contentId,
  isOpen,
  onClose
}: ReportContentModalProps) {
  const { user } = useAuth();
  const [reason, setReason] = useState('');
  const [selectedReason, setSelectedReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    function handleEscape(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        onClose();
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  const handleReasonSelect = (value: string) => {
    setSelectedReason(value);
    if (value !== 'other') {
      setReason(value);
    } else {
      setReason('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      setError('You must be logged in to report content');
      return;
    }

    if (!reason.trim()) {
      setError('Please provide a reason for your report');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Check rate limit
      const rateLimitCheck = await checkRateLimit(user.id, 'report_content');
      if (rateLimitCheck.isLimited) {
        throw new Error(formatRateLimitMessage('report_content', rateLimitCheck.resetTime));
      }

      const supabase = createClient();

      // Check if this content has already been reported by this user
      const { data: existingReports, error: checkError } = await supabase
        .from('reported_content')
        .select('id')
        .eq('content_type', contentType)
        .eq('content_id', contentId)
        .eq('reporter_id', user.id)
        .eq('status', 'pending');

      if (checkError) {
        throw checkError;
      }

      if (existingReports && existingReports.length > 0) {
        setError('You have already reported this content');
        setIsSubmitting(false);
        return;
      }

      // Create the report
      const { error: reportError } = await supabase
        .from('reported_content')
        .insert({
          content_type: contentType,
          content_id: contentId,
          reporter_id: user.id,
          reason: reason.trim(),
          status: 'pending'
        });

      if (reportError) {
        throw reportError;
      }

      setSuccess(true);

      // Reset form
      setReason('');
      setSelectedReason('');

      // Close modal after 2 seconds
      setTimeout(() => {
        onClose();
        setSuccess(false);
      }, 2000);
    } catch (err: any) {
      console.error('Error reporting content:', err);
      setError(err.message || 'Failed to report content');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  const reasonOptions = [
    { value: 'spam', label: 'Spam or advertising' },
    { value: 'offensive', label: 'Offensive or inappropriate content' },
    { value: 'harmful', label: 'Harmful or dangerous content' },
    { value: 'misinformation', label: 'Misinformation or unverified claims' },
    { value: 'copyright', label: 'Copyright or intellectual property violation' },
    { value: 'other', label: 'Other (please specify)' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        ref={modalRef}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Report Content</h2>
            <button
              onClick={onClose}
              className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {success ? (
            <div className="text-center py-6">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/30">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="mt-3 text-lg font-medium text-gray-900 dark:text-gray-100">Report Submitted</h3>
              <p className="mt-2 text-gray-500 dark:text-gray-400">
                Thank you for helping to keep our community safe.
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Please let us know why you're reporting this content. Our moderators will review your report.
              </p>

              {error && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-md text-sm">
                  {error}
                </div>
              )}

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Reason for reporting
                </label>
                <div className="space-y-2">
                  {reasonOptions.map((option) => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="reason"
                        value={option.value}
                        checked={selectedReason === option.value}
                        onChange={() => handleReasonSelect(option.value)}
                        className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 dark:border-gray-600"
                      />
                      <span className="ml-2 text-gray-700 dark:text-gray-300">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {selectedReason === 'other' && (
                <div className="mb-4">
                  <label htmlFor="custom-reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Please specify
                  </label>
                  <textarea
                    id="custom-reason"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-nature-green focus:border-nature-green dark:bg-gray-700 dark:text-white"
                    rows={3}
                    placeholder="Please provide details about your report"
                  />
                </div>
              )}

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-650 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-nature-green"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || !reason.trim()}
                  className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-nature-green disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Report'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
