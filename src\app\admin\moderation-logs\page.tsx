'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getModerationActions } from '@/lib/moderation';
import { format, formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import {
  FaEdit, FaTrash, FaLock, FaUnlock, FaThumbtack, FaExchangeAlt,
  FaCheck, FaExclamationTriangle, FaFilter, FaSearch, FaCalendarAlt,
  FaUser, FaComments, FaFolder, FaList, FaDownload, FaEye
} from 'react-icons/fa';

export default function ModerationLogsPage() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [moderationActions, setModerationActions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalActions, setTotalActions] = useState(0);
  const [filters, setFilters] = useState({
    contentType: '',
    actionType: '',
    moderatorId: '',
    startDate: '',
    endDate: ''
  });
  const [moderators, setModerators] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const itemsPerPage = 20;

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        // Redirect to unauthorized page if not admin/moderator
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Load moderators for filter dropdown
      const { data: moderatorsData } = await supabase
        .from('profiles')
        .select('id, username, full_name')
        .or('role.eq.admin,role.eq.moderator');

      if (moderatorsData) {
        setModerators(moderatorsData);
      }

      // Load moderation actions
      loadModerationActions();
    }

    checkAuth();
  }, [router, user, loading, page]);

  async function loadModerationActions() {
    try {
      setIsLoading(true);
      setError(null);

      const { data, count, error } = await getModerationActions({
        limit: itemsPerPage,
        offset: (page - 1) * itemsPerPage,
        contentType: filters.contentType || undefined,
        actionType: filters.actionType || undefined,
        moderatorId: filters.moderatorId || undefined,
        startDate: filters.startDate || undefined,
        endDate: filters.endDate || undefined
      });

      if (error) {
        console.error('Error from getModerationActions:', error);
        setError(`Failed to load moderation logs: ${error}`);
        setModerationActions([]);
        setTotalActions(0);
        setTotalPages(1);
        return;
      }

      setModerationActions(data || []);
      setTotalActions(count || 0);
      setTotalPages(Math.ceil((count || 0) / itemsPerPage));
    } catch (err) {
      console.error('Error loading moderation actions:', err);
      setError('Failed to load moderation logs. Please try again.');
      setModerationActions([]);
      setTotalActions(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  }

  function handleFilterChange(e) {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  }

  function applyFilters() {
    setPage(1); // Reset to first page when applying filters
    loadModerationActions();
  }

  function resetFilters() {
    setFilters({
      contentType: '',
      actionType: '',
      moderatorId: '',
      startDate: '',
      endDate: ''
    });
    setPage(1);
    loadModerationActions();
  }

  function exportToCSV() {
    // Create CSV content
    const headers = ['Date', 'Action', 'Moderator', 'Content Type', 'Content ID', 'Reason'];
    const csvRows = [headers.join(',')];

    moderationActions.forEach(action => {
      const row = [
        format(new Date(action.created_at), 'yyyy-MM-dd HH:mm:ss'),
        ((action.action_type || action.action || '') + '').replace(/_/g, ' '),
        action.moderator?.username || 'Unknown',
        action.content_type,
        action.content_id,
        `"${(action.reason || '').replace(/"/g, '""')}"`
      ];
      csvRows.push(row.join(','));
    });

    const csvContent = csvRows.join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `moderation-logs-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Helper function to get icon for action type
  const getActionIcon = (actionType) => {
    // Use action_type if available, otherwise use action
    const action = actionType || '';
    switch (action) {
      case 'edit_post':
        return <FaEdit className="text-yellow-500" />;
      case 'soft_delete_post':
      case 'hard_delete_post':
      case 'soft_delete_topic':
      case 'hard_delete_topic':
        return <FaTrash className="text-red-500" />;
      case 'lock_topic':
        return <FaLock className="text-gray-500" />;
      case 'unlock_topic':
        return <FaUnlock className="text-green-500" />;
      case 'pin_topic':
      case 'unpin_topic':
        return <FaThumbtack className="text-blue-500" />;
      case 'move_topic':
        return <FaExchangeAlt className="text-purple-500" />;
      case 'mark_solution':
        return <FaCheck className="text-green-500" />;
      case 'ban_user':
        return <FaUser className="text-red-500" />;
      case 'unban_user':
        return <FaUser className="text-green-500" />;
      case 'create_category':
      case 'update_category':
      case 'delete_category':
        return <FaFolder className="text-blue-500" />;
      case 'resolved_report':
      case 'dismissed_report':
        return <FaExclamationTriangle className="text-orange-500" />;
      default:
        return <FaList className="text-gray-500" />;
    }
  };

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading moderation logs...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Moderation Logs</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/admin')}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Back to Dashboard
          </button>
          <button
            onClick={exportToCSV}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <FaDownload className="mr-2" />
            Export CSV
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Filters</h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-nature-green hover:underline flex items-center"
          >
            <FaFilter className="mr-1" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <label htmlFor="contentType" className="block text-sm font-medium text-gray-700 mb-1">
                Content Type
              </label>
              <select
                id="contentType"
                name="contentType"
                value={filters.contentType}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="">All Content Types</option>
                <option value="forum_post">Forum Posts</option>
                <option value="forum_topic">Forum Topics</option>
                <option value="forum_category">Forum Categories</option>
                <option value="user">Users</option>
                <option value="report">Reports</option>
              </select>
            </div>

            <div>
              <label htmlFor="actionType" className="block text-sm font-medium text-gray-700 mb-1">
                Action Type
              </label>
              <select
                id="actionType"
                name="actionType"
                value={filters.actionType}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="">All Actions</option>
                <option value="edit_post">Edit Post</option>
                <option value="soft_delete_post">Soft Delete Post</option>
                <option value="hard_delete_post">Hard Delete Post</option>
                <option value="soft_delete_topic">Soft Delete Topic</option>
                <option value="hard_delete_topic">Hard Delete Topic</option>
                <option value="lock_topic">Lock Topic</option>
                <option value="unlock_topic">Unlock Topic</option>
                <option value="pin_topic">Pin Topic</option>
                <option value="unpin_topic">Unpin Topic</option>
                <option value="move_topic">Move Topic</option>
                <option value="ban_user">Ban User</option>
                <option value="unban_user">Unban User</option>
                <option value="create_category">Create Category</option>
                <option value="update_category">Update Category</option>
                <option value="delete_category">Delete Category</option>
                <option value="resolved_report">Resolve Report</option>
                <option value="dismissed_report">Dismiss Report</option>
              </select>
            </div>

            <div>
              <label htmlFor="moderatorId" className="block text-sm font-medium text-gray-700 mb-1">
                Moderator
              </label>
              <select
                id="moderatorId"
                name="moderatorId"
                value={filters.moderatorId}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="">All Moderators</option>
                {moderators.map(mod => (
                  <option key={mod.id} value={mod.id}>
                    {mod.full_name || mod.username}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <div className="relative">
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={filters.startDate}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green pl-10"
                />
                <FaCalendarAlt className="absolute left-3 top-3 text-gray-400" />
              </div>
            </div>

            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <div className="relative">
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={filters.endDate}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green pl-10"
                />
                <FaCalendarAlt className="absolute left-3 top-3 text-gray-400" />
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-2">
          <button
            onClick={resetFilters}
            className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>
          <button
            onClick={applyFilters}
            className="px-3 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <span className="text-gray-500">Total Actions:</span>
            <span className="ml-2 font-semibold">{totalActions}</span>
          </div>
          <div>
            <span className="text-gray-500">Page:</span>
            <span className="ml-2 font-semibold">{page} of {totalPages}</span>
          </div>
        </div>
      </div>

      {/* Moderation Actions Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Moderator
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Content Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {moderationActions.length > 0 ? (
                moderationActions.map((action) => (
                  <tr key={action.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="font-medium">
                        {format(new Date(action.created_at), 'MMM d, yyyy')}
                      </div>
                      <div className="text-xs">
                        {format(new Date(action.created_at), 'h:mm a')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="mr-2">
                          {getActionIcon(action.action_type || action.action)}
                        </div>
                        <div className="font-medium text-gray-900">
                          {((action.action_type || action.action || '') + '').replace(/_/g, ' ')}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {action.moderator?.full_name || action.moderator?.username || 'Unknown'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {action.content_type}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 max-w-xs truncate">
                        {action.reason || 'No reason provided'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        href={`/admin/moderation-logs/${action.id}`}
                        className="text-nature-green hover:text-green-700 flex items-center justify-end"
                      >
                        <FaEye className="mr-1" />
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                    No moderation actions found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="flex items-center space-x-2">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setPage(pageNum)}
                  className={`px-3 py-1 rounded-md ${
                    page === pageNum
                      ? 'bg-nature-green text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-3 py-1 rounded-md ${
                page === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Next
            </button>
          </nav>
        </div>
      )}
    </div>
  );
}
