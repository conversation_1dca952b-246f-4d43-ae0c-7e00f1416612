import React from 'react';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import Image from 'next/image';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

export default async function WikiPage() {
  // Create a Supabase client without cookie handling for server components
  // This avoids the cookies() warning since we're not using auth in this component
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
    }
  );

  // Fetch featured articles
  const { data: featuredArticles, error: featuredError } = await supabase
    .from('articles')
    .select(`
      id,
      title,
      slug,
      excerpt,
      created_at,
      updated_at,
      author_id,
      category_id,
      profiles:author_id(username, full_name, avatar_url, avatar_type, preset_avatar),
      categories:category_id(name, slug, icon),
      media:article_media(media(file_url))
    `)
    .eq('status', 'published')
    .eq('featured', true)
    .order('created_at', { ascending: false })
    .limit(3);

  // Fetch categories with article counts
  const { data: categories, error: categoriesError } = await supabase
    .from('categories')
    .select('id, name, slug, description, icon');

  // Get article counts for each category
  const categoryCounts = {};
  if (categories) {
    for (const category of categories) {
      const { count, error } = await supabase
        .from('articles')
        .select('id', { count: 'exact', head: true })
        .eq('category_id', category.id)
        .eq('status', 'published');

      categoryCounts[category.id] = count || 0;
    }
  }

  // Fetch recently updated articles
  const { data: recentArticles, error: recentError } = await supabase
    .from('articles')
    .select(`
      id,
      title,
      slug,
      excerpt,
      content,
      created_at,
      updated_at,
      author_id,
      profiles:author_id(username, full_name, avatar_url, avatar_type, preset_avatar),
      categories:category_id(name, slug, icon)
    `)
    .eq('status', 'published')
    .order('updated_at', { ascending: false })
    .limit(3);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-16 bg-gradient-to-br from-[#2e7d32] to-[#1b5e20] text-white p-12 rounded-2xl shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/pattern.svg')] opacity-20"></div>

        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-1/2 translate-x-1/4"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/10 rounded-full translate-y-1/2 -translate-x-1/4"></div>

        <div className="relative z-10 max-w-4xl mx-auto text-center">
          <div className="inline-flex items-center justify-center mb-6 bg-white/20 px-4 py-2 rounded-full backdrop-blur-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <span className="font-medium">Community-Driven Knowledge Base</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 drop-shadow-sm">NatureHeals Wiki</h1>
          <p className="text-xl md:text-2xl text-green-100 max-w-3xl mx-auto mb-8 leading-relaxed">
            Discover evidence-based information on holistic healing, plant-based medicines, and natural treatments curated by our community of practitioners and enthusiasts.
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative mt-10">
            <div className="absolute inset-0 bg-white/30 rounded-full blur-xl"></div>
            <div className="relative flex">
              <input
                type="text"
                placeholder="Search for herbs, remedies, conditions..."
                className="w-full px-8 py-5 border-0 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-green-300 text-gray-700 text-lg search-input"
              />
              <button className="absolute right-2 top-2 bg-gradient-to-r from-[#2e7d32] to-[#1b5e20] text-white p-3 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div>

          <div className="flex flex-wrap justify-center gap-4 mt-8">
            <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm hover:bg-white/30 transition-colors cursor-pointer">#Herbal Remedies</span>
            <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm hover:bg-white/30 transition-colors cursor-pointer">#Ayurveda</span>
            <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm hover:bg-white/30 transition-colors cursor-pointer">#Essential Oils</span>
            <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm hover:bg-white/30 transition-colors cursor-pointer">#Medicinal Mushrooms</span>
            <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm hover:bg-white/30 transition-colors cursor-pointer">#Holistic Health</span>
          </div>
        </div>
      </div>

      {/* Featured Articles */}
      <div className="mb-16">
        <div className="flex flex-col items-center justify-between mb-8">
          <div className="inline-block mb-4">
            <div className="h-1 w-24 bg-green-300 mb-1 mx-auto"></div>
            <div className="h-1 w-16 bg-green-500 mx-auto"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Featured Articles</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8 text-center">
            Explore our most popular and comprehensive guides to natural healing
          </p>
          <div className="flex w-full justify-end">
            <a href="/wiki/featured" className="text-[#2e7d32] font-medium hover:underline flex items-center">
              View All
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredArticles && featuredArticles.length > 0 ? (
            featuredArticles.map((article) => {
              // Get the first media item if available
              const mediaUrl = article.media && article.media.length > 0
                ? article.media[0]?.media?.file_url
                : null;

              return (
                <div key={article.id} className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border border-gray-100 hover:border-green-200">
                  <div className="h-48 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-green-100 group-hover:scale-105 transition-transform duration-500">
                      {mediaUrl ? (
                        <Image
                          src={mediaUrl}
                          alt={article.title}
                          width={400}
                          height={200}
                          className="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-green-50">
                          <span className="text-green-800 font-medium">{article.categories?.name || 'Article'}</span>
                        </div>
                      )}
                    </div>
                    {article.categories && (
                      <div className="absolute top-3 right-3 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-medium text-green-800 shadow-sm">
                        {article.categories.name}
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        Featured Article
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-gray-800 group-hover:text-[#2e7d32] transition-colors">{article.title}</h3>
                    <div className="h-px w-16 bg-green-200 mb-3"></div>
                    <p className="text-gray-600 mb-4 line-clamp-3">{article.excerpt}</p>
                    <Link href={`/wiki/${article.slug}`} className="text-[#2e7d32] font-medium hover:underline flex items-center transition-all duration-200 transform hover:translate-x-1">
                      Read Article
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </Link>
                  </div>
                </div>
              );
            })
          ) : (
            // Fallback content if no featured articles are found
            <div className="col-span-3 bg-white p-8 rounded-xl shadow-md text-center">
              <div className="bg-green-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-[#2e7d32]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">No Featured Articles Yet</h3>
              <p className="text-gray-600 mb-4">Our editors are currently selecting the best content to feature here. Check back soon!</p>
              <Link href="/wiki" className="text-[#2e7d32] font-medium hover:underline inline-flex items-center">
                Browse All Articles
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Categories */}
      <div className="mb-16 bg-gradient-to-br from-green-50 to-white rounded-xl border border-green-100 shadow-md py-12 px-6">
        <div className="flex flex-col items-center justify-between mb-8">
          <div className="inline-block mb-4">
            <div className="h-1 w-24 bg-green-300 mb-1 mx-auto"></div>
            <div className="h-1 w-16 bg-green-500 mx-auto"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Browse by Category</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8 text-center">
            Explore our extensive collection of natural healing knowledge organized by topic
          </p>
          <div className="flex w-full justify-end">
            <a href="/categories" className="text-[#2e7d32] font-medium hover:underline flex items-center">
              View All Categories
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {categories && categories.length > 0 ? (
            <>
              {categories.slice(0, 7).map((category) => {
                const articleCount = categoryCounts[category.id] || 0;

                // Function to get icon for a category
                const getCategoryIcon = (slug: string, iconName?: string) => {
                  // Default icon mapping
                  const iconMap: Record<string, string> = {
                    'herbal-remedies': 'leaf',
                    'mind-body-practices': 'sparkles',
                    'nutritional-healing': 'cake',
                    'medicinal-herbs': 'beaker',
                    'aromatherapy': 'fire',
                    'traditional-medicine': 'academic-cap',
                    'ayurveda': 'sun',
                    'chinese-medicine': 'moon',
                    'homeopathy': 'droplet',
                    'naturopathy': 'globe',
                    'foraging': 'map',
                    'gardening': 'home',
                    'herbalism': 'collection',
                    'nutrition': 'shopping-cart',
                    'essential-oils': 'color-swatch',
                    'holistic-health': 'heart',
                    'sustainable-living': 'lightning-bolt'
                  };

                  // Use the provided icon name or fall back to the mapping
                  return iconName || iconMap[slug] || 'bookmark';
                };

                // Get the icon name for this category
                const iconName = getCategoryIcon(category.slug, category.icon);

                // Render the appropriate icon
                const renderCategoryIcon = () => {
                  switch (iconName) {
                    case 'leaf':
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      );
                    case 'sparkles':
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>
                      );
                    case 'cake':
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.701 2.701 0 01-1.5.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zm-3-9v-2a2 2 0 00-2-2H8a2 2 0 00-2 2v2h12z" />
                        </svg>
                      );
                    case 'heart':
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      );
                    case 'fire':
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
                        </svg>
                      );
                    case 'beaker':
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                      );
                    case 'color-swatch':
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>
                      );
                    default:
                      return (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                        </svg>
                      );
                  }
                };

                return (
                  <Link
                    key={category.id}
                    href={`/categories/${category.slug}`}
                    className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-5px] border border-gray-100 hover:border-green-200 group"
                  >
                    <div className="flex items-center mb-4">
                      <div className="bg-gradient-to-br from-[#2e7d32] to-[#388e3c] p-3 rounded-full mr-3 shadow-md">
                        {renderCategoryIcon()}
                      </div>
                      <h3 className="font-bold text-lg group-hover:text-[#2e7d32] transition-colors">{category.name}</h3>
                    </div>
                    <div className="h-px w-16 bg-green-200 mb-4 ml-12"></div>
                    <p className="text-gray-600 ml-12 mb-2">{category.description || `Articles related to ${category.name}`}</p>
                    <div className="flex items-center ml-12">
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                        {articleCount} {articleCount === 1 ? 'article' : 'articles'}
                      </span>
                    </div>
                  </Link>
                );
              })}

              <Link href="/categories" className="bg-gradient-to-br from-green-50 to-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-5px] border border-green-100 flex items-center justify-center group">
                <div className="text-center">
                  <div className="bg-white/80 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm group-hover:shadow-md transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#2e7d32]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                  </div>
                  <span className="text-gray-700 font-bold group-hover:text-[#2e7d32] transition-colors flex items-center justify-center">
                    View All Categories
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </span>
                  <div className="text-gray-500 text-sm mt-2">Explore all {categories.length} categories</div>
                </div>
              </Link>
            </>
          ) : (
            <div className="col-span-4 bg-white p-8 rounded-xl shadow-md text-center">
              <div className="bg-green-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-[#2e7d32]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">No Categories Found</h3>
              <p className="text-gray-600 mb-4">We're currently organizing our content into categories. Check back soon!</p>
            </div>
          )}
        </div>
      </div>

      {/* Recent Updates */}
      <div className="mb-16 bg-gradient-to-br from-white to-green-50 rounded-xl border border-green-100 shadow-md py-12 px-6">
        <div className="flex flex-col items-center justify-between mb-8">
          <div className="inline-block mb-4">
            <div className="h-1 w-24 bg-green-300 mb-1 mx-auto"></div>
            <div className="h-1 w-16 bg-green-500 mx-auto"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Recently Updated</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8 text-center">
            Stay current with the latest additions and improvements to our knowledge base
          </p>
          <div className="flex w-full justify-end">
            <a href="/recent-updates" className="text-[#2e7d32] font-medium hover:underline flex items-center">
              View All Updates
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
        <div className="space-y-6">
          {recentArticles && recentArticles.length > 0 ? (
            recentArticles.map((article) => {
              // Get avatar URL or use default
              const avatarUrl = article.profiles?.avatar_url ||
                (article.profiles?.preset_avatar ? `/avatars/${article.profiles.preset_avatar}.png` :
                `https://www.gravatar.com/avatar/${article.profiles?.username?.substring(0, 10) || '00000000000'}?d=mp&f=y`);

              // Format the date
              const updatedDate = formatDate(article.updated_at);

              return (
                <div key={article.id} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-green-200 group">
                  <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                    <div className="flex-1">
                      <div className="flex items-center mb-3">
                        <div className="bg-gradient-to-br from-[#2e7d32] to-[#388e3c] p-2 rounded-full mr-3 shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </div>
                        <div>
                          <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full inline-block mb-1">
                            {new Date(article.updated_at).getTime() > new Date(article.created_at).getTime() + 86400000 ? 'Updated' : 'New'}
                          </div>
                          <h3 className="font-bold text-xl group-hover:text-[#2e7d32] transition-colors">{article.title}</h3>
                        </div>
                      </div>
                      <div className="h-px w-16 bg-green-200 mb-3 ml-10"></div>
                      <p className="text-gray-700 mb-4 ml-10 line-clamp-3">{article.excerpt || article.content?.substring(0, 150) + '...'}</p>
                      <div className="flex items-center ml-10">
                        <Image
                          src={avatarUrl}
                          alt={article.profiles?.username || 'User'}
                          width={32}
                          height={32}
                          className="h-8 w-8 rounded-full border-2 border-white shadow-sm mr-3"
                        />
                        <div>
                          <span className="text-sm font-medium text-gray-900">{article.profiles?.username || 'Anonymous'}</span>
                          <div className="text-xs text-gray-500">Updated {updatedDate}</div>
                        </div>
                      </div>
                    </div>
                    <Link href={`/wiki/${article.slug}`} className="bg-gradient-to-r from-[#2e7d32] to-[#388e3c] text-white px-5 py-2 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:translate-y-[-2px] flex items-center whitespace-nowrap">
                      <span>Read Article</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </Link>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="bg-white p-8 rounded-xl shadow-md text-center">
              <div className="bg-green-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-[#2e7d32]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">No Recent Updates</h3>
              <p className="text-gray-600 mb-4">Our contributors are working on new content. Check back soon for updates!</p>
              <Link href="/contribute" className="text-[#2e7d32] font-medium hover:underline inline-flex items-center">
                Contribute an Article
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
