import type { <PERSON><PERSON><PERSON>, View<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import "./heading-fix.css";
import "../styles/dropdown-fix.css";
import "../styles/search-fix.css";
import "../styles/article-content.css";
import "../styles/social-share.css";
import ClientAuthProvider from "@/components/ClientAuthProvider";
import UserHeaderInfo from "@/components/UserHeaderInfo";
import MobileNavigation from "@/components/MobileNavigation";
import SiteLogo from "@/components/SiteLogo";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "NatureHeals.info - Holistic Healing Knowledge Base",
  description: "A community-driven knowledge base for holistic healing, plant-based medicines, and natural treatments",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "NatureHeals.info",
  },
};

export const viewport: Viewport = {
  themeColor: "#2e7d32",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Client-side auth will handle user state
  const user = null;

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50 text-gray-900`}
        suppressHydrationWarning
      >
        <ClientAuthProvider initialUser={user}>
          <div className="min-h-screen flex flex-col">
            <header className="bg-gradient-to-r from-nature-green to-green-700 text-white shadow-lg sticky top-0 z-50 h-auto">
              <div className="container mx-auto py-3 px-3 sm:px-6 flex justify-between items-center">
                <div className="flex items-center">
                  <SiteLogo size="sm" className="mr-8" />
                  <nav className="hidden md:flex space-x-8 items-center">
                    <a href="/" className="font-medium hover:text-green-100 transition-all duration-200 border-b-2 border-transparent hover:border-green-100 py-1 text-lg group relative">
                      <span className="group-hover:text-green-100">Home</span>
                    </a>
                    <a href="/wiki" className="font-medium hover:text-green-100 transition-all duration-200 border-b-2 border-transparent hover:border-green-100 py-1 text-lg group relative">
                      <span className="group-hover:text-green-100">Wiki</span>
                    </a>
                    <a href="/categories" className="font-medium hover:text-green-100 transition-all duration-200 border-b-2 border-transparent hover:border-green-100 py-1 text-lg group relative">
                      <span className="group-hover:text-green-100">Categories</span>
                    </a>
                    <a href="/forums" className="font-medium hover:text-green-100 transition-all duration-200 border-b-2 border-transparent hover:border-green-100 py-1 text-lg group relative">
                      <span className="group-hover:text-green-100">Forums</span>
                    </a>
                    <a href="/contribute" className="font-medium hover:text-green-100 transition-all duration-200 border-b-2 border-transparent hover:border-green-100 py-1 text-lg group relative">
                      <span className="group-hover:text-green-100">Contribute</span>
                    </a>
                    <a href="/about" className="font-medium hover:text-green-100 transition-all duration-200 border-b-2 border-transparent hover:border-green-100 py-1 text-lg group relative">
                      <span className="group-hover:text-green-100">About</span>
                    </a>
                  </nav>
                </div>
                <div className="flex items-center space-x-2 sm:space-x-4">
                  <a href="/search" className="hidden md:flex items-center text-white hover:text-green-100 transition-all duration-200 mr-4 group">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 group-hover:scale-110 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <span className="ml-2 font-medium text-lg">Search</span>
                  </a>
                  <MobileNavigation />
                  <UserHeaderInfo />
                </div>
              </div>
            </header>
            <main className="flex-grow container mx-auto py-6 px-4 sm:py-8 sm:px-6 bg-white text-gray-900 my-4 sm:my-8 rounded-lg shadow-md border border-gray-200">
              <div className="max-w-7xl mx-auto">
                {children}
              </div>
            </main>
            <footer className="bg-gradient-to-br from-[#1b4332] to-[#2d6a4f] text-white border-t border-green-900 shadow-inner">
              <div className="container mx-auto py-12 px-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                  <div>
                    <div className="mb-4">
                      <SiteLogo size="lg" />
                    </div>
                    <p className="text-green-50">A community-driven knowledge base for holistic healing and natural remedies.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-4 text-green-100 drop-shadow-sm">Explore</h3>
                    <ul className="space-y-2">
                      <li><a href="/wiki" className="text-green-50 hover:text-white transition-colors hover:underline">Wiki Articles</a></li>
                      <li><a href="/categories" className="text-green-50 hover:text-white transition-colors hover:underline">Categories</a></li>
                      <li><a href="/forums" className="text-green-50 hover:text-white transition-colors hover:underline">Community Forums</a></li>
                      <li><a href="/popular" className="text-green-50 hover:text-white transition-colors hover:underline">Popular Remedies</a></li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-4 text-green-100 drop-shadow-sm">Contribute</h3>
                    <ul className="space-y-2">
                      <li><a href="/contribute/article" className="text-green-50 hover:text-white transition-colors hover:underline">Add Article</a></li>
                      <li><a href="/contribute/media" className="text-green-50 hover:text-white transition-colors hover:underline">Upload Media</a></li>
                      <li><a href="/guidelines" className="text-green-50 hover:text-white transition-colors hover:underline">Contribution Guidelines</a></li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-4 text-green-100 drop-shadow-sm">Legal</h3>
                    <ul className="space-y-2">
                      <li><a href="/terms" className="text-green-50 hover:text-white transition-colors hover:underline">Terms of Service</a></li>
                      <li><a href="/privacy" className="text-green-50 hover:text-white transition-colors hover:underline">Privacy Policy</a></li>
                      <li><a href="/disclaimer" className="text-green-50 hover:text-white transition-colors hover:underline">Medical Disclaimer</a></li>
                    </ul>
                  </div>
                </div>
                <div className="mt-8 pt-6 border-t border-green-800 text-center text-green-100">
                  <p className="text-green-200 font-medium">&copy; {new Date().getFullYear()} NatureHeals.info. All rights reserved.</p>
                </div>
              </div>
            </footer>
          </div>
        </ClientAuthProvider>
      </body>
    </html>
  );
}
