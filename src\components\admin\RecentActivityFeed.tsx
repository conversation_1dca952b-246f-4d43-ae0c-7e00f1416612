'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getRecentActivity } from '@/lib/admin-data';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import {
  FaUser, FaComments, FaEdit, FaTrash, FaLock, FaUnlock,
  FaThumbtack, FaExchangeAlt, FaCheck, FaExclamationTriangle
} from 'react-icons/fa';

export default function RecentActivityFeed() {
  const router = useRouter();
  const [activity, setActivity] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadActivity() {
      try {
        setIsLoading(true);
        setError(null);
        const data = await getRecentActivity(15);
        if (Array.isArray(data)) {
          setActivity(data);
        } else {
          console.error('Invalid activity data format:', data);
          setError('Failed to load recent activity: Invalid data format');
          setActivity([]);
        }
      } catch (err) {
        console.error('Error loading activity:', err);
        setError('Failed to load recent activity: ' + (err.message || 'Unknown error'));
        setActivity([]);
      } finally {
        setIsLoading(false);
      }
    }

    loadActivity();
  }, []);

  // Helper function to get icon for activity type
  const getActivityIcon = (item) => {
    if (item.activityType === 'user') {
      return <FaUser className="text-blue-500" />;
    }

    if (item.activityType === 'forum') {
      if (item.type === 'post') {
        return <FaComments className="text-green-500" />;
      }
      return <FaComments className="text-purple-500" />;
    }

    if (item.activityType === 'moderation') {
      switch (item.type) {
        case 'edit_post':
          return <FaEdit className="text-yellow-500" />;
        case 'soft_delete_post':
        case 'hard_delete_post':
        case 'soft_delete_topic':
        case 'hard_delete_topic':
          return <FaTrash className="text-red-500" />;
        case 'lock_topic':
          return <FaLock className="text-gray-500" />;
        case 'unlock_topic':
          return <FaUnlock className="text-green-500" />;
        case 'pin_topic':
        case 'unpin_topic':
          return <FaThumbtack className="text-blue-500" />;
        case 'move_topic':
          return <FaExchangeAlt className="text-purple-500" />;
        case 'mark_solution':
          return <FaCheck className="text-green-500" />;
        default:
          return <FaExclamationTriangle className="text-orange-500" />;
      }
    }

    return <FaExclamationTriangle className="text-gray-500" />;
  };

  // Helper function to get activity description
  const getActivityDescription = (item) => {
    if (item.activityType === 'user') {
      return (
        <span>
          New user <span className="font-medium">{item.username || item.full_name}</span> registered
        </span>
      );
    }

    if (item.activityType === 'forum') {
      if (item.type === 'post') {
        return (
          <span>
            <span className="font-medium">{item.author?.username || 'Anonymous'}</span> replied to{' '}
            <Link href={`/forums/${item.category?.slug}/${item.topic?.slug}`} className="text-nature-green hover:underline">
              {item.topic?.title || 'a topic'}
            </Link>
          </span>
        );
      }
      return (
        <span>
          <span className="font-medium">{item.author?.username || 'Anonymous'}</span> created a new topic{' '}
          <Link href={`/forums/${item.category?.slug}/${item.slug}`} className="text-nature-green hover:underline">
            {item.title}
          </Link>
        </span>
      );
    }

    if (item.activityType === 'moderation') {
      const moderatorName = item.moderator?.username || 'A moderator';

      switch (item.type) {
        case 'edit_post':
          return <span><span className="font-medium">{moderatorName}</span> edited a post</span>;
        case 'soft_delete_post':
          return <span><span className="font-medium">{moderatorName}</span> removed a post</span>;
        case 'hard_delete_post':
          return <span><span className="font-medium">{moderatorName}</span> permanently deleted a post</span>;
        case 'soft_delete_topic':
          return <span><span className="font-medium">{moderatorName}</span> removed a topic</span>;
        case 'hard_delete_topic':
          return <span><span className="font-medium">{moderatorName}</span> permanently deleted a topic</span>;
        case 'lock_topic':
          return <span><span className="font-medium">{moderatorName}</span> locked a topic</span>;
        case 'unlock_topic':
          return <span><span className="font-medium">{moderatorName}</span> unlocked a topic</span>;
        case 'pin_topic':
          return <span><span className="font-medium">{moderatorName}</span> pinned a topic</span>;
        case 'unpin_topic':
          return <span><span className="font-medium">{moderatorName}</span> unpinned a topic</span>;
        case 'move_topic':
          return <span><span className="font-medium">{moderatorName}</span> moved a topic to another category</span>;
        default:
          return <span><span className="font-medium">{moderatorName}</span> performed a moderation action</span>;
      }
    }

    return 'Unknown activity';
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
        <h2 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">Recent Activity</h2>
        <div className="space-y-3 sm:space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex items-start animate-pulse">
              <div className="bg-gray-200 p-1.5 sm:p-2 rounded-full mr-3 sm:mr-4 h-8 w-8 sm:h-9 sm:w-9"></div>
              <div className="flex-1">
                <div className="h-3 sm:h-4 bg-gray-200 rounded w-3/4 mb-1 sm:mb-2"></div>
                <div className="h-2 sm:h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
        <h2 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">Recent Activity</h2>
        <div className="bg-red-50 text-red-700 p-3 sm:p-4 rounded-md">
          <p className="text-sm sm:text-base">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-4 sm:mb-6">
        <h2 className="text-lg sm:text-xl font-bold mb-2 sm:mb-0">Recent Activity</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/admin/activity')}
            className="px-2 sm:px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs sm:text-sm hover:bg-gray-300"
          >
            View All
          </button>
        </div>
      </div>

      {activity.length === 0 ? (
        <div className="text-center py-6 sm:py-8 text-gray-500 text-sm sm:text-base">
          No recent activity found
        </div>
      ) : (
        <div className="space-y-3 sm:space-y-4">
          {activity.map((item) => (
            <div key={item.id} className="flex items-start border-b border-gray-100 pb-3 sm:pb-4">
              <div className="bg-gray-100 p-1.5 sm:p-2 rounded-full mr-3 sm:mr-4 flex-shrink-0">
                {getActivityIcon(item)}
              </div>
              <div className="flex-1">
                <p className="text-xs sm:text-sm">{getActivityDescription(item)}</p>
                <p className="text-xs text-gray-500 mt-0.5 sm:mt-1">
                  {formatDistanceToNow(new Date(item.created_at), { addSuffix: true })}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
