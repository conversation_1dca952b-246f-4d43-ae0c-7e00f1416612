'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { UserProfile } from '@/lib/profile-types';
import { getGravatarUrl } from '@/lib/gravatar';

export default function ProfilePage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    async function loadProfile() {
      if (loading) return;

      if (!user) {
        router.push('/auth/signin?redirect=/profile');
        return;
      }

      try {
        setIsLoading(true);
        const supabase = createClientComponentClient();

        // Get the user's profile
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          throw error;
        }

        setProfile(data as UserProfile);
      } catch (err: any) {
        console.error('Error loading profile:', err);
        setError(err.message || 'Failed to load profile');
      } finally {
        setIsLoading(false);
      }
    }

    loadProfile();
  }, [user, loading, router]);

  if (loading || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 p-4 rounded-md text-red-700 mb-4">
          {error}
        </div>
        <button
          onClick={() => router.push('/profile/edit')}
          className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Create Your Profile
        </button>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Welcome to NatureHeals.info!</h1>
          <p className="mb-6">You haven't set up your profile yet. Create one to connect with other herbal enthusiasts!</p>
          <button
            onClick={() => router.push('/profile/edit')}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Create Your Profile
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Your Profile</h1>
        <button
          onClick={() => router.push('/profile/edit')}
          className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Edit Profile
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="bg-gradient-to-r from-green-600 to-green-400 h-32"></div>
        <div className="px-6 py-4 relative">
          <div className="absolute -top-16 left-6">
            <div className="h-32 w-32 rounded-full border-4 border-white bg-gray-200 flex items-center justify-center overflow-hidden">
              {profile.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt={profile.full_name || profile.username}
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="text-4xl font-bold text-gray-400">
                  {(profile.full_name || profile.username || 'User').charAt(0).toUpperCase()}
                </span>
              )}
            </div>
          </div>

          <div className="mt-16">
            <h2 className="text-2xl font-bold">{profile.full_name || 'Unnamed User'}</h2>
            <p className="text-gray-600">@{profile.username}</p>

            {profile.bio && (
              <div className="mt-4">
                <p className="text-gray-700">{profile.bio}</p>
              </div>
            )}

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              {profile.show_location && profile.location && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Location</h3>
                  <p className="text-gray-700">{profile.location}</p>
                </div>
              )}

              {profile.show_email && profile.email && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Email</h3>
                  <p className="text-gray-700">{profile.email}</p>
                </div>
              )}

              {profile.website && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Website</h3>
                  <a href={profile.website} target="_blank" rel="noopener noreferrer" className="text-nature-green hover:underline">
                    {profile.website.replace(/^https?:\/\//, '')}
                  </a>
                </div>
              )}

              {profile.expertise_level && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Expertise Level</h3>
                  <p className="text-gray-700 capitalize">{profile.expertise_level}</p>
                </div>
              )}
            </div>

            {profile.interests && profile.interests.length > 0 && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Interests</h3>
                <div className="flex flex-wrap gap-2">
                  {profile.interests.map(interest => (
                    <span
                      key={interest}
                      className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                    >
                      {interest.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {profile.favorite_herbs && profile.favorite_herbs.length > 0 && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Favorite Herbs</h3>
                <div className="flex flex-wrap gap-2">
                  {profile.favorite_herbs.map(herb => (
                    <span
                      key={herb}
                      className="px-3 py-1 bg-green-50 text-green-700 border border-green-200 rounded-full text-sm"
                    >
                      {herb}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {profile.healing_philosophy && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Healing Philosophy</h3>
                <p className="text-gray-700">{profile.healing_philosophy}</p>
              </div>
            )}

            {profile.spirit_plant && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Spirit Plant</h3>
                <p className="text-gray-700">{profile.spirit_plant}</p>
              </div>
            )}

            {profile.social_links && Object.values(profile.social_links).some(link => !!link) && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Connect</h3>
                <div className="flex space-x-4">
                  {profile.social_links.twitter && (
                    <a href={profile.social_links.twitter} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-500">
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                      </svg>
                    </a>
                  )}

                  {profile.social_links.instagram && (
                    <a href={profile.social_links.instagram} target="_blank" rel="noopener noreferrer" className="text-pink-500 hover:text-pink-600">
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                      </svg>
                    </a>
                  )}

                  {profile.social_links.facebook && (
                    <a href={profile.social_links.facebook} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-700">
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                      </svg>
                    </a>
                  )}

                  {profile.social_links.pinterest && (
                    <a href={profile.social_links.pinterest} target="_blank" rel="noopener noreferrer" className="text-red-600 hover:text-red-700">
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.627 0-12 5.372-12 12 0 5.084 3.163 9.426 7.627 11.174-.105-.949-.2-2.405.042-3.441.218-.937 1.407-5.965 1.407-5.965s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738.***************.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.889-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.359-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146 1.124.347 2.317.535 3.554.535 6.627 0 12-5.373 12-12 0-6.628-5.373-12-12-12z" fillRule="evenodd" clipRule="evenodd" />
                      </svg>
                    </a>
                  )}

                  {profile.social_links.youtube && (
                    <a href={profile.social_links.youtube} target="_blank" rel="noopener noreferrer" className="text-red-600 hover:text-red-700">
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            )}

            {profile.badges && profile.badges.length > 0 && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Badges</h3>
                <div className="flex flex-wrap gap-2">
                  {profile.badges.map(badge => (
                    <span
                      key={badge}
                      className="px-3 py-1 bg-yellow-50 text-yellow-700 border border-yellow-200 rounded-full text-sm flex items-center"
                    >
                      <span className="mr-1">🏆</span> {badge}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Profile Visibility</h3>
              <div className="flex items-center">
                <span className={`px-3 py-1 rounded-full text-sm ${
                  profile.visibility === 'public' ? 'bg-green-100 text-green-800' :
                  profile.visibility === 'members' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {profile.visibility === 'public' ? 'Public' :
                   profile.visibility === 'members' ? 'Members Only' :
                   'Private'}
                </span>
                <span className="ml-2 text-sm text-gray-500">
                  {profile.visibility === 'public' ? 'Anyone can view your profile' :
                   profile.visibility === 'members' ? 'Only registered members can view your profile' :
                   'Only you can view your profile'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
