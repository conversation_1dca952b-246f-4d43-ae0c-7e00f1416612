'use client';

import React from 'react';

export default function ContributePage() {
  return (
    <div className="container mx-auto px-4 py-8 contribute-section">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-black">Contribute to NatureHeals.info</h1>
        <p className="text-lg text-gray-800 mb-8">
          Share your knowledge and help build the world's most comprehensive resource for holistic healing and natural remedies.
        </p>

        {/* Contribution Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border-t-4 border-nature-green">
            <h2 className="text-xl font-semibold mb-3 text-black">Create a New Article</h2>
            <p className="text-gray-800 mb-4">
              Write a new wiki article about a plant, remedy, therapy, or any topic related to holistic healing.
            </p>
            <a
              href="/contribute/article"
              className="inline-block bg-nature-green text-white px-4 py-2 rounded-md font-medium hover:bg-nature-green-dark transition-colors"
            >
              Create Article
            </a>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border-t-4 border-nature-green">
            <h2 className="text-xl font-semibold mb-3 text-black">Upload Media</h2>
            <p className="text-gray-800 mb-4">
              Share images, videos, or other media files to enhance existing articles or create new resources.
            </p>
            <a
              href="/contribute/media"
              className="inline-block bg-nature-green text-white px-4 py-2 rounded-md font-medium hover:bg-nature-green-dark transition-colors"
            >
              Upload Media
            </a>
          </div>
        </div>

        {/* Contribution Guidelines */}
        <div className="bg-gray-50 p-6 rounded-lg mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-black">Contribution Guidelines</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2 text-black">Quality Standards</h3>
              <ul className="list-disc list-inside text-gray-800 space-y-1">
                <li>Provide accurate, well-researched information</li>
                <li>Cite reputable sources when making health claims</li>
                <li>Use clear, concise language accessible to general readers</li>
                <li>Organize content with appropriate headings and sections</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2 text-black">Content Policies</h3>
              <ul className="list-disc list-inside text-gray-800 space-y-1">
                <li>Do not make exaggerated or unsubstantiated health claims</li>
                <li>Include appropriate disclaimers for medical information</li>
                <li>Respect copyright and only upload media you have rights to</li>
                <li>Be respectful of different healing traditions and approaches</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2 text-black">Review Process</h3>
              <p className="text-gray-800">
                All contributions are reviewed by our moderation team before being published. This process typically takes 1-3 days.
                You may be asked to make revisions if your content needs improvement.
              </p>
            </div>
          </div>
          <div className="mt-4">
            <a href="/guidelines" className="text-nature-green font-medium hover:underline">
              Read Full Guidelines
            </a>
          </div>
        </div>

        {/* Article Creation Form */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-black">Quick Contribution</h2>
          <form>
            <div className="mb-4">
              <label htmlFor="title" className="block text-black font-medium mb-2">
                Article Title
              </label>
              <input
                type="text"
                id="title"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
                placeholder="e.g., The Benefits of Lavender"
              />
            </div>
            <div className="mb-4">
              <label htmlFor="category" className="block text-black font-medium mb-2">
                Category
              </label>
              <select
                id="category"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
              >
                <option value="">Select a category</option>
                <option value="herbal-remedies">Herbal Remedies</option>
                <option value="alternative-therapies">Alternative Therapies</option>
                <option value="nutritional-healing">Nutritional Healing</option>
                <option value="traditional-medicine">Traditional Medicine</option>
                <option value="mind-body-practices">Mind-Body Practices</option>
                <option value="essential-oils">Essential Oils</option>
                <option value="natural-remedies">Natural Remedies</option>
              </select>
            </div>
            <div className="mb-4">
              <label htmlFor="content" className="block text-black font-medium mb-2">
                Content
              </label>
              <textarea
                id="content"
                rows={6}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
                placeholder="Write your article content here... You can use Markdown formatting."
              ></textarea>
              <p className="text-sm text-gray-800 mt-1">
                Supports Markdown formatting. You can add headings, lists, links, and more.
              </p>
            </div>
            <div className="mb-4">
              <label htmlFor="tags" className="block text-black font-medium mb-2">
                Tags (comma separated)
              </label>
              <input
                type="text"
                id="tags"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
                placeholder="e.g., lavender, sleep, anxiety, essential oil"
              />
            </div>
            <div className="mb-6">
              <label htmlFor="media" className="block text-black font-medium mb-2">
                Upload Images (optional)
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-12 w-12 mx-auto text-gray-400 mb-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                <p className="text-gray-800 mb-2">Drag and drop files here, or click to browse</p>
                <input type="file" id="media" className="hidden" multiple />
                <button
                  type="button"
                  className="text-nature-green font-medium hover:underline"
                  onClick={() => document.getElementById('media')?.click()}
                >
                  Browse Files
                </button>
                <p className="text-xs text-gray-500 mt-2">
                  Supported formats: JPG, PNG, GIF. Maximum file size: 5MB
                </p>
              </div>
            </div>
            <div className="flex items-center mb-6">
              <input type="checkbox" id="terms" className="mr-2" />
              <label htmlFor="terms" className="text-gray-600 text-sm">
                I confirm that this content is my own or I have permission to share it, and I agree to the{' '}
                <a href="/terms" className="text-nature-green hover:underline">
                  Terms of Service
                </a>
                .
              </label>
            </div>
            <div className="flex justify-end">
              <button
                type="submit"
                className="bg-nature-green text-white px-6 py-3 rounded-md font-medium hover:bg-nature-green-dark transition-colors"
              >
                Submit for Review
              </button>
            </div>
          </form>
        </div>

        {/* Become a Moderator */}
        <div className="bg-nature-green-dark text-white p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">Become a Moderator</h2>
          <p className="mb-4">
            Help maintain the quality and accuracy of our wiki by becoming a community moderator. Moderators review and approve
            contributions, ensuring they meet our guidelines.
          </p>
          <a
            href="/moderator-application"
            className="inline-block bg-white text-nature-green-dark px-4 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors"
          >
            Apply to be a Moderator
          </a>
        </div>
      </div>
    </div>
  );
}
