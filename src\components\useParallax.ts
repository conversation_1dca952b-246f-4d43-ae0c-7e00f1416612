'use client';
import { useEffect, useState } from "react";

export function useParallax(maxOffset = 30) {
  const [offset, setOffset] = useState({ x: 0, y: 0 });

  useEffect(() => {
    function handleMouseMove(e: MouseEvent) {
      const { innerWidth, innerHeight } = window;
      const x = ((e.clientX / innerWidth) - 0.5) * 2 * maxOffset;
      const y = ((e.clientY / innerHeight) - 0.5) * 2 * maxOffset;
      setOffset({ x, y });
    }
    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [maxOffset]);

  return offset;
}