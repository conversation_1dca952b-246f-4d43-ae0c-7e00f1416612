import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Get the SQL for the migration
    const sql = `
    -- Add policy to allow admins to update any user's profile

    -- Check if the policy already exists
    DO $$
    BEGIN
        -- First, drop the policy if it exists to ensure we're creating a fresh one
        DROP POLICY IF EXISTS "Ad<PERSON> can update any profile" ON public.profiles;
        
        -- Create policy for admins to update any profile
        -- This policy allows admins to update ANY profile (not just their own)
        CREATE POLICY "Admins can update any profile"
            ON public.profiles FOR UPDATE
            TO authenticated
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND role = 'admin'
                )
            )
            WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );
        
        RAISE NOTICE 'Created policy: Ad<PERSON> can update any profile';
    END
    $$;
    `;

    return NextResponse.json({ sql });
  } catch (error) {
    console.error('Error in migration route:', error);
    return NextResponse.json(
      { error: 'Failed to get migration SQL' },
      { status: 500 }
    );
  }
}