import { createClient } from './auth.server';
import { cookies } from 'next/headers';
import { supabase } from './supabase';

interface Article {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  author_id: string;
  category_id: string;
  status: string;
}

export async function getArticles({ 
  limit = 10, 
  offset = 0, 
  category = null, 
  tag = null, 
  search = null,
  featured = false,
  orderBy = 'created_at',
  orderDirection = 'desc'
}: {
  limit?: number;
  offset?: number;
  category?: string | null;
  tag?: string | null;
  search?: string | null;
  featured?: boolean;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}) {
  let query = supabase
  .from('articles')
  .select(
    `
      *,
      media: article_media (media (file_url)),
      author: profiles(*),
      category: categories(*),
      tags: article_tags(tags(*))
    `
  )
    .eq('status', 'published')
    .order(orderBy, { ascending: orderDirection === 'asc' })
    .range(offset, offset + limit - 1);

  if (category) {
    query = query.eq('categories.slug', category);
  }

  if (tag) {
    query = query.eq('article_tags.tags.slug', tag);
  }

  if (search) {
    query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`);
  }

  if (featured) {
    query = query.eq('featured', true);
  }

  let { data, error } = await query;

  if (error) {
    throw new Error('Error fetching articles: ' + error.message);
  }

  data = data?.map(article => ({
    ...article,
    media_url: article.media_url && article.media_url.length > 0 ? article.media_url[0].media.file_url : null
  }));

  return data;
}

export async function getArticleBySlug(slug: string) {
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      author:profiles(*),
      category:categories(*),
      tags:article_tags(tags(*)),
      media:article_media(media(*))
    `)
    .eq('slug', slug)
    .eq('status', 'published')
    .single();

  if (error) {
    console.error('Error fetching article:', error);
    return null;
  }

  // Increment view count
  await supabase
    .from('articles')
    .update({ view_count: (data.view_count || 0) + 1 })
    .eq('id', data.id);

  return data;
}

export async function getRelatedArticles(articleId: string, categoryId: string, limit = 3) {
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      author:profiles(username),
      category:categories(name)
    `)
    .eq('status', 'published')
    .eq('category_id', categoryId)
    .neq('id', articleId)
    .order('view_count', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching related articles:', error);
    return [];
  }

  return data;
}

export async function createArticle({
  title,
  content,
  categoryId,
  tags,
  excerpt,
  authorId,
  status = 'pending_review'
}: {
  title: string;
  content: string;
  categoryId: string;
  tags?: string[];
  excerpt?: string;
  authorId: string;
  status?: 'draft' | 'pending_review';
}) {
  const slug = createSlug(title);
  
  // Check if slug already exists
  const { data: existingArticle } = await supabase
    .from('articles')
    .select('id')
    .eq('slug', slug)
    .single();
    
  const finalSlug = existingArticle ? `${slug}-${Date.now()}` : slug;
  
  // Create article
  const { data: article, error } = await supabase
    .from('articles')
    .insert({
      title,
      slug: finalSlug,
      content,
      excerpt: excerpt || content.substring(0, 150) + '...',
      author_id: authorId,
      category_id: categoryId,
      status
    })
    .select()
    .single();
    
  if (error) {
    console.error('Error creating article:', error);
    throw error;
  }
  
  // Add tags if provided
  if (tags && tags.length > 0) {
    const tagInserts = tags.map(tagId => ({
      article_id: article.id,
      tag_id: tagId
    }));
    
    const { error: tagError } = await supabase
      .from('article_tags')
      .insert(tagInserts);
      
    if (tagError) {
      console.error('Error adding tags:', tagError);
    }
  }
  
  // Create initial revision
  await supabase
    .from('revisions')
    .insert({
      article_id: article.id,
      content,
      editor_id: authorId,
      revision_note: 'Initial version'
    });
    
  // Record contribution
  await supabase
    .from('contributions')
    .insert({
      user_id: authorId,
      content_type: 'article',
      content_id: article.id,
      contribution_type: 'create'
    });
    
  return article;
}

export async function updateArticle({
  id,
  title,
  content,
  categoryId,
  tags,
  excerpt,
  editorId,
  revisionNote,
  status
}: {
  id: string;
  title?: string;
  content?: string;
  categoryId?: string;
  tags?: string[];
  excerpt?: string;
  editorId: string;
  revisionNote?: string;
  status?: 'draft' | 'pending_review' | 'published' | 'rejected' | 'archived';
}) {
  const updates: any = {};
  
  if (title !== undefined) {
    updates.title = title;
  }
  
  if (content !== undefined) {
    updates.content = content;
  }
  
  if (categoryId !== undefined) {
    updates.category_id = categoryId;
  }
  
  if (excerpt !== undefined) {
    updates.excerpt = excerpt;
  }
  
  if (status !== undefined) {
    updates.status = status;
  }
  
  // Only update if there are changes
  if (Object.keys(updates).length === 0) {
    return null;
  }
  
  // Update article
  const { data: article, error } = await supabase
    .from('articles')
    .update(updates)
    .eq('id', id)
    .select()
    .single();
    
  if (error) {
    console.error('Error updating article:', error);
    throw error;
  }
  
  // Update tags if provided
  if (tags !== undefined) {
    // Remove existing tags
    await supabase
      .from('article_tags')
      .delete()
      .eq('article_id', id);
      
    // Add new tags
    if (tags.length > 0) {
      const tagInserts = tags.map(tagId => ({
        article_id: id,
        tag_id: tagId
      }));
      
      await supabase
        .from('article_tags')
        .insert(tagInserts);
    }
  }
  
  // Create revision if content was updated
  if (content !== undefined) {
    await supabase
      .from('revisions')
      .insert({
        article_id: id,
        content,
        editor_id: editorId,
        revision_note: revisionNote || 'Updated content'
      });
  }
  
  // Record contribution
  await supabase
    .from('contributions')
    .insert({
      user_id: editorId,
      content_type: 'article',
      content_id: id,
      contribution_type: 'edit'
    });
    
  return article;
}

// Helper function to create a URL-friendly slug from a title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
}

export async function getArticlesByUser(userId: string) {
  const { data, error } = await supabase
    .from('articles')
    .select(`
      *,
      category:categories(*),
      tags:article_tags(tags(*))
    `)
    .eq('author_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching user articles:', error);
    return [];
  }

  return data;
}

export async function deleteArticle(id: string) {
  const { error } = await supabase
    .from('articles')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting article:', error);
    throw error;
  }
}
