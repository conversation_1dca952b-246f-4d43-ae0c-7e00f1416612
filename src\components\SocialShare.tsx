import React from 'react';
import { FaFacebook, FaTwitter, FaLinkedin, FaWhatsapp, FaTelegram, FaReddit, FaEnvelope, FaLink } from 'react-icons/fa';

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
}

const SocialShare: React.FC<SocialShareProps> = ({ url, title, description = '' }) => {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description || '');
  
  // Function to handle copy link to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(url)
      .then(() => {
        alert('Link copied to clipboard!');
      })
      .catch(err => {
        console.error('Failed to copy link: ', err);
      });
  };

  return (
    <div className="social-share">
      <h3 className="text-lg font-semibold mb-3">Share this article</h3>
      <div className="flex flex-wrap gap-2">
        {/* Facebook */}
        <a 
          href={`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`}
          target="_blank"
          rel="noopener noreferrer"
          className="social-share-button bg-[#3b5998] hover:bg-[#2d4373] text-white"
          aria-label="Share on Facebook"
        >
          <FaFacebook className="w-5 h-5" />
        </a>
        
        {/* Twitter */}
        <a 
          href={`https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`}
          target="_blank"
          rel="noopener noreferrer"
          className="social-share-button bg-[#1DA1F2] hover:bg-[#0c85d0] text-white"
          aria-label="Share on Twitter"
        >
          <FaTwitter className="w-5 h-5" />
        </a>
        
        {/* LinkedIn */}
        <a 
          href={`https://www.linkedin.com/shareArticle?mini=true&url=${encodedUrl}&title=${encodedTitle}&summary=${encodedDescription}`}
          target="_blank"
          rel="noopener noreferrer"
          className="social-share-button bg-[#0077b5] hover:bg-[#005582] text-white"
          aria-label="Share on LinkedIn"
        >
          <FaLinkedin className="w-5 h-5" />
        </a>
        
        {/* WhatsApp */}
        <a 
          href={`https://wa.me/?text=${encodedTitle}%20${encodedUrl}`}
          target="_blank"
          rel="noopener noreferrer"
          className="social-share-button bg-[#25D366] hover:bg-[#1da851] text-white"
          aria-label="Share on WhatsApp"
        >
          <FaWhatsapp className="w-5 h-5" />
        </a>
        
        {/* Telegram */}
        <a 
          href={`https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`}
          target="_blank"
          rel="noopener noreferrer"
          className="social-share-button bg-[#0088cc] hover:bg-[#006699] text-white"
          aria-label="Share on Telegram"
        >
          <FaTelegram className="w-5 h-5" />
        </a>
        
        {/* Reddit */}
        <a 
          href={`https://www.reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`}
          target="_blank"
          rel="noopener noreferrer"
          className="social-share-button bg-[#FF5700] hover:bg-[#e24d00] text-white"
          aria-label="Share on Reddit"
        >
          <FaReddit className="w-5 h-5" />
        </a>
        
        {/* Email */}
        <a 
          href={`mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${encodedUrl}`}
          className="social-share-button bg-[#777] hover:bg-[#555] text-white"
          aria-label="Share via Email"
        >
          <FaEnvelope className="w-5 h-5" />
        </a>
        
        {/* Copy Link */}
        <button 
          onClick={copyToClipboard}
          className="social-share-button bg-[#333] hover:bg-[#111] text-white"
          aria-label="Copy Link"
        >
          <FaLink className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};

export default SocialShare;
