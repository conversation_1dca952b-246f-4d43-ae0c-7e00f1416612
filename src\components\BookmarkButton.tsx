'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';

interface BookmarkButtonProps {
  articleId: string;
  initialIsBookmarked?: boolean;
  className?: string;
  showText?: boolean;
}

export default function BookmarkButton({
  articleId,
  initialIsBookmarked = false,
  className = '',
  showText = true
}: BookmarkButtonProps) {
  const [isBookmarked, setIsBookmarked] = useState(initialIsBookmarked);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const router = useRouter();

  // Check if article is bookmarked on component mount
  useEffect(() => {
    async function checkBookmarkStatus() {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('bookmarks')
          .select('id')
          .eq('user_id', user.id)
          .eq('article_id', articleId)
          .single();

        if (error && error.code !== 'PGRST116') {
          console.error('Error checking bookmark status:', error);
          return;
        }

        setIsBookmarked(!!data);
      } catch (err) {
        console.error('Failed to check bookmark status:', err);
      }
    }

    checkBookmarkStatus();
  }, [articleId, user]);

  const toggleBookmark = async () => {
    if (!user) {
      // Redirect to login if not logged in
      router.push(`/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    setIsLoading(true);

    try {
      const { data, error } = await supabase.rpc('toggle_bookmark', {
        article_id: articleId
      });

      if (error) {
        console.error('Error toggling bookmark:', error);
        return;
      }

      // Update state based on the returned value
      setIsBookmarked(data);

      // Show a toast notification
      const message = data ? 'Article bookmarked' : 'Bookmark removed';
      // You can implement a toast notification system here
      
    } catch (err) {
      console.error('Failed to toggle bookmark:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={toggleBookmark}
      disabled={isLoading}
      className={`flex items-center gap-1 transition-colors ${
        isBookmarked
          ? 'text-yellow-500 hover:text-yellow-600'
          : 'text-gray-500 hover:text-nature-green dark:text-gray-400 dark:hover:text-yellow-500'
      } ${className}`}
      aria-label={isBookmarked ? 'Remove bookmark' : 'Bookmark this article'}
    >
      {isBookmarked ? (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
        </svg>
      )}
      {showText && <span>{isBookmarked ? 'Saved' : 'Save'}</span>}
    </button>
  );
}
