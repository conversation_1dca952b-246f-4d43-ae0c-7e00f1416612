import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Helper function to handle CORS
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}

// Cache duration in seconds (for Cache-Control header)
const CACHE_DURATION_SECONDS = 300; // 5 minutes

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('q');
  const limit = parseInt(searchParams.get('limit') || '10', 10);

  // Add cache control headers
  const headers = {
    ...corsHeaders(),
    'Cache-Control': `public, max-age=${CACHE_DURATION_SECONDS}`, // 5 minutes
  };

  if (!query || query.length < 2) {
    return NextResponse.json([], { headers });
  }

  try {
    // Initialize Supabase client inside the handler
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500, headers });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Use Promise.allSettled to run searches in parallel
    const [articlesResult, categoriesResult, tagsResult] = await Promise.allSettled([
      // Search articles
      supabase
        .from('articles')
        .select('id, title, slug, excerpt')
        .or(`title.ilike.%${query}%,content.ilike.%${query}%`)
        .limit(Math.ceil(limit * 0.6)), // 60% of results from articles

      // Search categories
      supabase
        .from('categories')
        .select('id, name, slug, description')
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .limit(Math.ceil(limit * 0.2)), // 20% of results from categories

      // Search tags
      supabase
        .from('tags')
        .select('id, name, slug')
        .ilike('name', `%${query}%`)
        .limit(Math.ceil(limit * 0.2)), // 20% of results from tags
    ]);

    // Process article results
    const articles = articlesResult.status === 'fulfilled' ? articlesResult.value.data || [] : [];
    if (articlesResult.status === 'rejected') {
      console.error('Error searching articles:', articlesResult.reason);
    }

    // Process category results
    const categories = categoriesResult.status === 'fulfilled' ? categoriesResult.value.data || [] : [];
    if (categoriesResult.status === 'rejected') {
      console.error('Error searching categories:', categoriesResult.reason);
    }

    // Process tag results
    const tags = tagsResult.status === 'fulfilled' ? tagsResult.value.data || [] : [];
    if (tagsResult.status === 'rejected') {
      console.error('Error searching tags:', tagsResult.reason);
    }

    // Search forum topics
    console.log('Searching for forum topics with query:', query);
    const { data: forumTopics, error: forumTopicsError } = await supabase
      .from('forum_topics')
      .select('id, title, slug, category_id')
      .ilike('title', `%${query}%`)
      .limit(5);

    console.log('Forum topics search result:', forumTopics);

    if (forumTopicsError) {
      console.error('Error searching forum topics:', forumTopicsError);
      // Continue with other searches instead of throwing an error
    }

    // Search forum posts
    console.log('Searching for forum posts with query:', query);
    const { data: forumPosts, error: forumPostsError } = await supabase
      .from('forum_posts')
      .select('id, content, topic_id, created_at')
      .ilike('content', `%${query}%`)
      .limit(5);

    console.log('Forum posts search result:', forumPosts);

    if (forumPostsError) {
      console.error('Error searching forum posts:', forumPostsError);
      // Continue with other searches instead of throwing an error
    }

    // Get topic information for forum posts
    let enrichedForumPosts = [];
    if (forumPosts && forumPosts.length > 0) {
      const topicIds = forumPosts.map(post => post.topic_id);
      const { data: topics } = await supabase
        .from('forum_topics')
        .select('id, title, slug, category_id')
        .in('id', topicIds);

      console.log('Topics for forum posts:', topics);

      // Get category information for topics
      const categoryIds = topics?.map(topic => topic.category_id).filter(Boolean) || [];
      const { data: categories } = await supabase
        .from('forum_categories')
        .select('id, slug')
        .in('id', categoryIds);

      console.log('Categories for topics:', categories);

      // Enrich forum posts with topic and category information
      enrichedForumPosts = forumPosts.map(post => {
        const topic = topics?.find(t => t.id === post.topic_id);
        const category = topic ? categories?.find(c => c.id === topic.category_id) : null;

        return {
          id: post.id,
          title: topic?.title || 'Forum Post',
          slug: category ? `${category.slug}/${topic?.slug}` : topic?.slug || '',
          excerpt: post.content?.substring(0, 150) + '...',
          type: 'forum_post'
        };
      });
    }

    // Format results
    const formattedArticles = articles.map(article => ({
      id: article.id,
      title: article.title,
      slug: article.slug,
      excerpt: article.excerpt,
      type: 'article'
    }));

    const formattedCategories = categories.map(category => ({
      id: category.id,
      title: category.name,
      slug: category.slug,
      excerpt: category.description,
      type: 'category'
    }));

    const formattedTags = tags.map(tag => ({
      id: tag.id,
      title: tag.name,
      slug: tag.slug,
      type: 'tag'
    }));

    const formattedForumTopics = forumTopics?.map(topic => ({
      id: topic.id,
      title: topic.title,
      slug: topic.slug,
      type: 'forum_topic'
    })) || [];

    // Combine and sort results, then limit to requested size
    const combinedResults = [
      ...formattedArticles,
      ...formattedCategories,
      ...formattedTags,
      ...formattedForumTopics,
      ...enrichedForumPosts
    ].slice(0, limit);

    // No in-memory caching, rely on HTTP cache headers instead

    console.log('Final combined results:', combinedResults.length);
    return NextResponse.json(combinedResults, { headers });
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json({ error: 'Search failed' }, { status: 500, headers });
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders() });
}
