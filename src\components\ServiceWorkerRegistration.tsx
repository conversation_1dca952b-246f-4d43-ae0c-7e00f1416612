'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { registerServiceWorker } from '@/lib/register-sw';
import Link from 'next/link';

export default function ServiceWorkerRegistration() {
  const [isOffline, setIsOffline] = useState(false);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if the browser is online or offline
    const handleOnlineStatusChange = () => {
      const offline = !navigator.onLine;
      setIsOffline(offline);

      if (offline) {
        // Log offline status
        console.log('You are offline. Some features may be unavailable.');
      }
    };

    // Register service worker
    registerServiceWorker();

    // Handle beforeinstallprompt event for PWA install
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Store the event for later use
      (window as any).deferredPrompt = e;

      // Show the install button
      setShowInstallPrompt(true);

      console.log('PWA install prompt available');
    };

    // Add event listeners for online/offline status
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Hide install prompt when app is installed
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      setShowInstallPrompt(false);
      (window as any).deferredPrompt = null;
    });

    // Initial check
    handleOnlineStatusChange();

    // Clean up event listeners
    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, [router]);

  // Function to handle install button click
  const handleInstallClick = () => {
    const promptEvent = (window as any).deferredPrompt;

    if (promptEvent) {
      // Show the install prompt
      promptEvent.prompt();

      // Wait for the user to respond to the prompt
      promptEvent.userChoice.then((choiceResult: { outcome: string }) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('User accepted the install prompt');
        } else {
          console.log('User dismissed the install prompt');
        }

        // Clear the stored prompt event
        (window as any).deferredPrompt = null;
        setShowInstallPrompt(false);
      });
    }
  };

  return (
    <>
      {/* Offline notification */}
      {isOffline && (
        <div className="fixed bottom-4 right-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded shadow-md z-50 max-w-sm">
          <div className="flex items-center">
            <svg className="h-6 w-6 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <div>
              <p className="font-medium">You are currently offline.</p>
              <p className="text-sm mt-1">Some features may be limited. <Link href="/offline" className="underline">Learn more</Link></p>
            </div>
          </div>
        </div>
      )}

      {/* Install prompt */}
      {showInstallPrompt && (
        <div className="fixed bottom-4 left-4 bg-green-50 border-l-4 border-nature-green text-gray-700 p-4 rounded shadow-md z-50 max-w-sm">
          <div className="flex items-start">
            <svg className="h-6 w-6 mr-2 flex-shrink-0 text-nature-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            <div>
              <p className="font-medium">Install NatureHeals.info</p>
              <p className="text-sm mt-1">Add this app to your home screen for quick access and offline use.</p>
              <button
                onClick={handleInstallClick}
                className="mt-2 px-3 py-1 bg-nature-green text-white text-sm rounded hover:bg-nature-green-dark transition-colors"
              >
                Install
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
