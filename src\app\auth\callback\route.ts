import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const error = requestUrl.searchParams.get('error');
  const error_description = requestUrl.searchParams.get('error_description');
  
  // If there's an error, redirect to the error page
  if (error) {
    return NextResponse.redirect(
      new URL(`/auth/verify/error?error=${encodeURIComponent(error_description || error)}`, requestUrl.origin)
    );
  }
  
  // If there's no code, redirect to the error page
  if (!code) {
    return NextResponse.redirect(
      new URL('/auth/verify/error?error=No verification code provided', requestUrl.origin)
    );
  }

  try {
    // Create a Supabase client
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Exchange the code for a session
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    
    if (error) {
      return NextResponse.redirect(
        new URL(`/auth/verify/error?error=${encodeURIComponent(error.message)}`, requestUrl.origin)
      );
    }
    
    // Redirect to success page
    return NextResponse.redirect(new URL('/auth/verify/success', requestUrl.origin));
  } catch (err) {
    console.error('Error in auth callback:', err);
    return NextResponse.redirect(
      new URL('/auth/verify/error?error=An unexpected error occurred', requestUrl.origin)
    );
  }
}
