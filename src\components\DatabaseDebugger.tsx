'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

interface DatabaseDebuggerProps {
  articleId: string;
}

const DatabaseDebugger: React.FC<DatabaseDebuggerProps> = ({ articleId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [articleData, setArticleData] = useState<any>(null);
  const [articleMediaData, setArticleMediaData] = useState<any[]>([]);
  const [mediaData, setMediaData] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const supabase = createClientComponentClient();

        // Fetch article data
        const { data: article, error: articleError } = await supabase
          .from('articles')
          .select('*')
          .eq('id', articleId)
          .single();

        if (articleError) {
          throw new Error(`Error fetching article: ${articleError.message}`);
        }

        setArticleData(article);

        // Fetch article_media data
        const { data: articleMedia, error: articleMediaError } = await supabase
          .from('article_media')
          .select('*')
          .eq('article_id', articleId);

        if (articleMediaError) {
          throw new Error(`Error fetching article_media: ${articleMediaError.message}`);
        }

        setArticleMediaData(articleMedia || []);

        // If we have article_media entries, fetch the media data
        if (articleMedia && articleMedia.length > 0) {
          const mediaIds = articleMedia.map(item => item.media_id);
          
          const { data: media, error: mediaError } = await supabase
            .from('media')
            .select('*')
            .in('id', mediaIds);

          if (mediaError) {
            throw new Error(`Error fetching media: ${mediaError.message}`);
          }

          setMediaData(media || []);
        }
      } catch (err: any) {
        console.error('Database debugger error:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (articleId) {
      fetchData();
    }
  }, [articleId]);

  if (loading) {
    return <div>Loading database information...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="bg-gray-100 p-4 rounded-lg border border-gray-300 my-4">
      <h2 className="text-lg font-bold mb-2">Database Debugger</h2>
      
      <div className="mb-4">
        <h3 className="font-medium">Article Data</h3>
        <pre className="bg-white p-2 rounded text-xs overflow-auto max-h-40">
          {JSON.stringify(articleData, null, 2)}
        </pre>
      </div>
      
      <div className="mb-4">
        <h3 className="font-medium">Article Media Entries ({articleMediaData.length})</h3>
        {articleMediaData.length === 0 ? (
          <p className="text-red-500">No article_media entries found for this article!</p>
        ) : (
          <pre className="bg-white p-2 rounded text-xs overflow-auto max-h-40">
            {JSON.stringify(articleMediaData, null, 2)}
          </pre>
        )}
      </div>
      
      <div>
        <h3 className="font-medium">Media Entries ({mediaData.length})</h3>
        {mediaData.length === 0 ? (
          <p className="text-red-500">No media entries found!</p>
        ) : (
          <pre className="bg-white p-2 rounded text-xs overflow-auto max-h-40">
            {JSON.stringify(mediaData, null, 2)}
          </pre>
        )}
      </div>
    </div>
  );
};

export default DatabaseDebugger;
