// <PERSON><PERSON>t to check the forum_topics table and RLS policies
const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Environment variables loaded:');
console.log('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '\u2713 Found' : '\u2717 Missing');
console.log('- SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '\u2713 Found' : '\u2717 Missing');
console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '\u2713 Found' : '\u2717 Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('\nMissing required Supabase credentials. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkForumTopics() {
  try {
    console.log('Checking forum_topics table...');
    
    // Check if the table exists
    const { data: tableData, error: tableError } = await supabase
      .from('forum_topics')
      .select('*')
      .limit(5);
    
    if (tableError) {
      console.error('Error accessing forum_topics table:', tableError);
      return;
    }
    
    console.log(`Found ${tableData.length} topics in the forum_topics table`);
    
    if (tableData.length > 0) {
      console.log('Sample topic:');
      console.log(JSON.stringify(tableData[0], null, 2));
    }
    
    // Check forum categories
    const { data: categories, error: catError } = await supabase
      .from('forum_categories')
      .select('*')
      .limit(5);
    
    if (catError) {
      console.error('Error accessing forum_categories table:', catError);
      return;
    }
    
    console.log(`Found ${categories.length} categories in the forum_categories table`);
    
    if (categories.length > 0) {
      console.log('Sample category:');
      console.log(JSON.stringify(categories[0], null, 2));
    }
    
    // Check user profiles
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, username, role')
      .limit(5);
    
    if (profileError) {
      console.error('Error accessing profiles table:', profileError);
      return;
    }
    
    console.log(`Found ${profiles.length} profiles in the profiles table`);
    
    if (profiles.length > 0) {
      console.log('Sample profiles:');
      profiles.forEach(profile => {
        console.log(`- ${profile.username || 'No username'} (${profile.id}): ${profile.role || 'No role'}`);
      });
    }
    
    console.log('\nInstructions to fix the RLS issue:');
    console.log('1. Go to the Supabase dashboard: https://app.supabase.com/');
    console.log('2. Navigate to your project');
    console.log('3. Go to the SQL Editor');
    console.log('4. Copy and paste the contents of supabase/comprehensive-forum-rls-fix.sql');
    console.log('5. Run the SQL script');
    console.log('6. Try creating a forum topic again');
    
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the check
checkForumTopics();
