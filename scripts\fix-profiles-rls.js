// <PERSON>ript to fix the RLS policies for the profiles table
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Get credentials from command line if not in env
const args = process.argv.slice(2);
if (args.length >= 2) {
  supabaseUrl = args[0];
  supabaseServiceKey = args[1];
}

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service key.');
  console.log('\nPlease run the script with your Supabase credentials:');
  console.log('node scripts/fix-profiles-rls.js <SUPABASE_URL> <SERVICE_KEY>');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixProfilesRLS() {
  try {
    console.log('Fixing RLS policies for profiles table...');
    
    // SQL to fix RLS policies
    const sql = `
      -- Check if the insert policy exists
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM pg_policies 
          WHERE tablename = 'profiles' 
          AND policyname = 'Users can insert their own profile'
        ) THEN
          -- Create the insert policy if it doesn't exist
          EXECUTE 'CREATE POLICY "Users can insert their own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id)';
          RAISE NOTICE 'Created INSERT policy for profiles table';
        ELSE
          RAISE NOTICE 'INSERT policy already exists for profiles table';
        END IF;
        
        -- Check if the update policy exists
        IF NOT EXISTS (
          SELECT 1 FROM pg_policies 
          WHERE tablename = 'profiles' 
          AND policyname = 'Users can update their own profile'
        ) THEN
          -- Create the update policy if it doesn't exist
          EXECUTE 'CREATE POLICY "Users can update their own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id)';
          RAISE NOTICE 'Created UPDATE policy for profiles table';
        ELSE
          RAISE NOTICE 'UPDATE policy already exists for profiles table';
        END IF;
        
        -- Make sure RLS is enabled
        EXECUTE 'ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY';
        RAISE NOTICE 'Enabled RLS for profiles table';
      END
      $$;
    `;
    
    // Execute the SQL
    const { error } = await supabase.rpc('execute_sql', { sql });
    
    if (error) {
      // If the execute_sql function doesn't exist, create it and try again
      if (error.message.includes('function execute_sql') || error.code === '42883') {
        console.log('Creating execute_sql function...');
        
        // Create the function directly
        await supabase.rpc('execute_sql', { 
          sql: `
            CREATE OR REPLACE FUNCTION execute_sql(sql text)
            RETURNS void AS $$
            BEGIN
              EXECUTE sql;
            END;
            $$ LANGUAGE plpgsql SECURITY DEFINER;
          `
        }).catch(() => {
          // If that fails, try a direct SQL approach
          return supabase.from('_rpc').select('*').limit(1);
        });
        
        // Try executing the original SQL again
        const { error: retryError } = await supabase.rpc('execute_sql', { sql });
        
        if (retryError) {
          console.error('Error fixing RLS policies after creating function:', retryError);
          
          // Last resort: try direct SQL for each policy
          console.log('Trying direct SQL approach...');
          
          await supabase.rpc('execute_sql', { 
            sql: "CREATE POLICY IF NOT EXISTS \"Users can insert their own profile\" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id)"
          }).catch(e => console.error('Error creating INSERT policy:', e));
          
          await supabase.rpc('execute_sql', { 
            sql: "CREATE POLICY IF NOT EXISTS \"Users can update their own profile\" ON public.profiles FOR UPDATE USING (auth.uid() = id)"
          }).catch(e => console.error('Error creating UPDATE policy:', e));
          
          await supabase.rpc('execute_sql', { 
            sql: "ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY"
          }).catch(e => console.error('Error enabling RLS:', e));
          
          console.log('Direct SQL approach completed.');
        }
      } else {
        console.error('Error fixing RLS policies:', error);
      }
    } else {
      console.log('Successfully fixed RLS policies for profiles table.');
    }
  } catch (err) {
    console.error('Error fixing RLS policies:', err);
  }
}

// Main function
async function main() {
  console.log('Starting RLS policy fix...');
  await fixProfilesRLS();
  console.log('RLS policy fix completed.');
}

main().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
