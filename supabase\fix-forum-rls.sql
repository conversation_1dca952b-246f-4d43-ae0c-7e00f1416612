-- Fix RLS policies for forum_topics table

-- First, drop the existing policy
DROP POLICY IF EXISTS "Authenticated users can create forum topics" ON public.forum_topics;

-- Create a new policy that allows any authenticated user to create topics
CREATE POLICY "Authenticated users can create forum topics"
    ON public.forum_topics FOR INSERT
    WITH CHECK (auth.uid() IS NOT NULL);

-- Update the policy for updating topics to be more permissive
DROP POLICY IF EXISTS "Users can update their own forum topics" ON public.forum_topics;
CREATE POLICY "Users can update their own forum topics"
    ON public.forum_topics FOR UPDATE
    USING (auth.uid() = author_id OR 
           EXISTS (
               SELECT 1 FROM public.profiles
               WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
           ));

-- Update the policy for deleting topics to be more permissive
DROP POLICY IF EXISTS "Users can delete their own forum topics" ON public.forum_topics;
CREATE POLICY "Users can delete their own forum topics"
    ON public.forum_topics FOR DELETE
    USING (auth.uid() = author_id OR 
           EXISTS (
               SELECT 1 FROM public.profiles
               WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
           ));

-- Drop the redundant moderator policy since we've incorporated it into the main policies
DROP POLICY IF EXISTS "Moderators can update any forum topic" ON public.forum_topics;
DROP POLICY IF EXISTS "Moderators can delete any forum topic" ON public.forum_topics;
