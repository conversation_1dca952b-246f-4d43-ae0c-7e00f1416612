import { createClient } from '@/lib/supabase';
import { ForumCategory, ForumTopic, ForumPost, ForumPostReaction } from '@/lib/forum-types';
import { createSlug } from '@/lib/utils';
import { checkRateLimit, formatRateLimitMessage } from '@/lib/rate-limiting';
import { logModerationAction } from '@/lib/moderation';

// Check if a table exists in the database
async function tableExists(tableName) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    return !error || !error.message.includes('does not exist');
  } catch (err) {
    return false;
  }
}

// Get all forum categories
export async function getForumCategories() {
  try {
    console.log('getForumCategories called');
    const supabase = createClient();

    // Check if forum_categories table exists
    const exists = await tableExists('forum_categories');
    console.log('forum_categories table exists:', exists);
    if (!exists) {
      console.error('forum_categories table does not exist');
      return []; // Return empty array instead of throwing error
    }

    const { data, error } = await supabase
      .from('forum_categories')
      .select('*')
      .order('display_order', { ascending: true });

    console.log('Raw forum categories data:', data);
    console.log('Error fetching forum categories:', error);

    if (error) {
      console.error('Error fetching forum categories:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.log('No forum categories found, returning empty array');
      return [];
    }

  // Get topic and post counts for each category
  try {
    console.log('Getting topic and post counts for categories');
    const categoriesWithCounts = await Promise.all(
      data.map(async (category) => {
        try {
          // Get topic count
          const { count: topicCount, error: topicError } = await supabase
            .from('forum_topics') // Corrected table name
            .select('*', { count: 'exact', head: true })
            .eq('category_id', category.id);

          if (topicError) {
            console.error('Error fetching topic count:', topicError);
          }

          // Get post count
          let postCount = 0;
          let postError = null;

          try {
            // First get topic IDs for this category
            const { data: topicIds, error: topicIdsError } = await supabase
              .from('forum_topics') // Corrected table name
              .select('id')
              .eq('category_id', category.id);

            if (topicIdsError) {
              console.error('Error fetching topic IDs:', topicIdsError);
              postError = topicIdsError;
            } else if (topicIds && topicIds.length > 0) {
              // Only query posts if we have topic IDs
              const ids = topicIds.map(t => t.id);
              const { count, error } = await supabase
                .from('forum_posts') // Corrected table name
                .select('*', { count: 'exact', head: true })
                .in('topic_id', ids);

              postCount = count || 0;
              postError = error;
            }
          } catch (err) {
            console.error('Error in post count query:', err);
            postError = err;
          }

          if (postError) {
            console.error('Error fetching post count:', postError);
          }

          const result = {
            ...category,
            topic_count: topicCount || 0,
            post_count: postCount || 0
          };
          console.log('Category with counts:', result);
          return result;
        } catch (err) {
          console.error('Error processing category:', category.id, err);
          // Return the category without counts rather than failing completely
          return {
            ...category,
            topic_count: 0,
            post_count: 0
          };
        }
      })
    );

    console.log('All categories with counts:', categoriesWithCounts);
    return categoriesWithCounts as ForumCategory[];
  } catch (err) {
    console.error('Error in Promise.all for categories:', err);
    // If Promise.all fails, return the raw categories without counts
    return data.map(category => ({
      ...category,
      topic_count: 0,
      post_count: 0
    })) as ForumCategory[];
  }
} catch (err) {
  console.error('Unexpected error in getForumCategories:', err);
  return []; // Return empty array on any error
}
}

// Get a single forum category by slug
export async function getForumCategoryBySlug(slug: string) {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('forum_categories')
    .select('*')
    .eq('slug', slug)
    .single();

  if (error) {
    console.error('Error fetching forum category:', error);
    throw error;
  }

  return data as ForumCategory;
}

// Get topics for a category
export async function getTopicsByCategory({
  categoryId,
  limit = 20,
  offset = 0,
  orderBy = 'last_post_at',
  orderDirection = 'desc'
}: {
  categoryId: string;
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}) {
  const supabase = createClient();

  const { data, error, count } = await supabase
    .from('forum_topics')
    .select(`
      *,
      author:author_id (
        username,
        full_name,
        avatar_url,
        is_verified_expert,
        level
      ),
      category:category_id (
        name,
        slug,
        icon
      )
    `, { count: 'exact' })
    .eq('category_id', categoryId)
    .order('is_pinned', { ascending: false })
    .order(orderBy, { ascending: orderDirection === 'asc' })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error fetching topics:', error);
    throw error;
  }

  // Get post counts for each topic
  const topicsWithCounts = await Promise.all(
    data.map(async (topic) => {
      const { count: postCount, error: postError } = await supabase // Corrected table name
        .from('forum_posts')
        .select('*', { count: 'exact', head: true })
        .eq('topic_id', topic.id);

      if (postError) {
        console.error('Error fetching post count:', postError);
      }

      // Get last post
      const { data: lastPost, error: lastPostError } = await supabase
        .from('forum_posts') // Corrected table name
        .select(`
          id,
          created_at,
          author:author_id (
            username,
            avatar_url
          )
        `)
        .eq('topic_id', topic.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (lastPostError && lastPostError.code !== 'PGRST116') {
        console.error('Error fetching last post:', lastPostError);
      }

      return {
        ...topic,
        post_count: postCount || 0,
        last_post: lastPost || null
      };
    })
  );

  return { data: topicsWithCounts as ForumTopic[], count };
}

// Get a single topic by slug
export async function getTopicBySlug(slug: string) {
  const supabase = createClient();

  console.log(`Getting topic by slug: ${slug}`);

  // Add a cache-busting parameter to ensure we get the latest data
  const timestamp = new Date().getTime();

  const { data, error } = await supabase
    .from('forum_topics')
    .select(`
      *,
      author:author_id (
        username,
        full_name,
        avatar_url,
        is_verified_expert,
        level
      ),
      category:category_id (
        name,
        slug,
        icon
      )
    `)
    .eq('slug', slug)
    .single();

  if (error) {
    console.error('Error fetching topic:', error);
    throw error;
  }

  console.log(`Topic data for ${slug}:`, data);

  // Increment view count
  await supabase
    .from('forum_topics')
    .update({ view_count: (data.view_count || 0) + 1 })
    .eq('id', data.id);

  return data as ForumTopic;
}

// Get posts for a topic
export async function getPostsByTopic({
  topicId,
  limit = 50,
  offset = 0
}: {
  topicId: string;
  limit?: number;
  offset?: number;
}) {
  const supabase = createClient();

  // First get all top-level posts
  const { data: topLevelPosts, error, count } = await supabase
    .from('forum_posts')
    .select(`
      *,
      author:author_id (
        username,
        full_name,
        avatar_url,
        is_verified_expert,
        level
      )
    `, { count: 'exact' })
    .eq('topic_id', topicId)
    .is('parent_id', null)
    .order('created_at', { ascending: true })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error fetching posts:', error);
    throw error;
  }

  // Get reactions for each post
  const postsWithReactions = await Promise.all(
    (topLevelPosts || []).map(async (post) => { // Ensure topLevelPosts is an array
      const { data: reactions, error: reactionsError } = await supabase
        .from('forum_post_reactions')
        .select(`
          *,
          user:user_id (
            username
          )
        `)
        .eq('post_id', post.id);

      if (reactionsError) {
        console.error('Error fetching reactions:', reactionsError);
      }

      // Get replies
      const { data: replies, error: repliesError } = await supabase
        .from('forum_posts') // Corrected table name
        .select(`
          *,
          author:author_id (
            username,
            full_name,
            avatar_url,
            is_verified_expert,
            level
          )
        `)
        .eq('topic_id', topicId)
        .eq('parent_id', post.id)
        .order('created_at', { ascending: true });

      if (repliesError) {
        console.error('Error fetching replies:', repliesError);
      }

      // Get reactions for each reply
      const repliesWithReactions = await Promise.all(
        (replies || []).map(async (reply) => {
          const { data: replyReactions, error: replyReactionsError } = await supabase
            .from('forum_post_reactions')
            .select(`
              *,
              user:user_id (
                username
              )
            `)
            .eq('post_id', reply.id);

          if (replyReactionsError) {
            console.error('Error fetching reply reactions:', replyReactionsError);
          }

          return {
            ...reply,
            reactions: replyReactions || []
          };
        })
      );

      return {
        ...post,
        reactions: reactions || [],
        replies: repliesWithReactions || []
      };
    })
  );

  return { data: postsWithReactions as ForumPost[], count };
}

// Create a new topic
export async function createTopic({
  title,
  content,
  categoryId,
  authorId
}: {
  title: string;
  content: string;
  categoryId: string;
  authorId: string;
}) {
  // Check rate limit
  const rateLimitCheck = await checkRateLimit(authorId, 'forum_topic_create');
  if (rateLimitCheck.isLimited) {
    throw new Error(formatRateLimitMessage('forum_topic_create', rateLimitCheck.resetTime));
  }

  // Use the regular client first to check if the slug exists
  const supabase = createClient();

  const slug = createSlug(title);

  // Check if slug already exists
  const { data: existingTopic } = await supabase
    .from('forum_topics')
    .select('id')
    .eq('slug', slug)
    .single();

  const finalSlug = existingTopic ? `${slug}-${Date.now()}` : slug;

  // Log the data we're trying to insert for debugging
  console.log('Creating topic with data:', {
    title,
    slug: finalSlug,
    category_id: categoryId,
    author_id: authorId
  });

  // Create topic
  const { data: topic, error } = await supabase
    .from('forum_topics')
    .insert({
      title,
      slug: finalSlug,
      category_id: categoryId,
      author_id: authorId
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating topic:', error);
    throw error;
  }

  // Create initial post
  const { data: post, error: postError } = await supabase
    .from('forum_posts')
    .insert({
      topic_id: topic.id,
      author_id: authorId,
      content
    })
    .select()
    .single();

  if (postError) {
    console.error('Error creating post:', postError);
    throw postError;
  }

  return { topic, post };
}

// Create a new post (reply)
export async function createPost({
  topicId,
  content,
  authorId,
  parentId
}: {
  topicId: string;
  content: string;
  authorId: string;
  parentId?: string;
}) {
  // Check rate limit
  const rateLimitCheck = await checkRateLimit(authorId, 'forum_post_create');
  if (rateLimitCheck.isLimited) {
    throw new Error(formatRateLimitMessage('forum_post_create', rateLimitCheck.resetTime));
  }

  const supabase = createClient();

  const { data, error } = await supabase
    .from('forum_posts')
    .insert({
      topic_id: topicId,
      author_id: authorId,
      content,
      parent_id: parentId
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating post:', error);
    throw error;
  }

  return data as ForumPost;
}

// Add a reaction to a post
export async function addReaction({
  postId,
  userId,
  reactionType
}: {
  postId: string;
  userId: string;
  reactionType: 'like' | 'helpful' | 'insightful';
}) {
  const supabase = createClient();

  // Check if reaction already exists
  const { data: existingReaction } = await supabase
    .from('forum_post_reactions')
    .select('id')
    .eq('post_id', postId)
    .eq('user_id', userId)
    .eq('reaction_type', reactionType)
    .single();

  if (existingReaction) {
    // Remove the reaction if it already exists
    const { error } = await supabase
      .from('forum_post_reactions')
      .delete()
      .eq('id', existingReaction.id);

    if (error) {
      console.error('Error removing reaction:', error);
      throw error;
    }

    return null;
  }

  // Add the reaction
  const { data, error } = await supabase
    .from('forum_post_reactions')
    .insert({
      post_id: postId,
      user_id: userId,
      reaction_type: reactionType
    })
    .select()
    .single();

  if (error) {
    console.error('Error adding reaction:', error);
    throw error;
  }

  return data as ForumPostReaction;
}

// Mark a post as a solution
export async function markAsSolution({
  postId,
  topicId,
  isSolution
}: {
  postId: string;
  topicId: string;
  isSolution: boolean;
}) {
  const supabase = createClient();

  // First, unmark any existing solutions for this topic
  if (isSolution) {
    const { error: resetError } = await supabase
      .from('forum_posts')
      .update({ is_solution: false })
      .eq('topic_id', topicId)
      .eq('is_solution', true);

    if (resetError) {
      console.error('Error resetting solutions:', resetError);
    }
  }

  // Update the post
  const { data, error } = await supabase
    .from('forum_posts')
    .update({ is_solution: isSolution })
    .eq('id', postId)
    .select()
    .single();

  if (error) {
    console.error('Error marking as solution:', error);
    throw error;
  }

  // If marking as solution, add achievement and badge to the post author
  if (isSolution) {
    // Get the post author
    const { data: post } = await supabase
      .from('forum_posts')
      .select('author_id')
      .eq('id', postId)
      .single();

    if (post) {
      // Count how many solutions this user has provided
      const { count: solutionCount } = await supabase
        .from('forum_posts')
        .select('*', { count: 'exact', head: true })
        .eq('author_id', post.author_id)
        .eq('is_solution', true);

      // Add solution provider badge if this is their first solution
      if (solutionCount === 1) {
        const { error: badgeError } = await supabase
          .from('profiles')
          .update({
            badges: supabase.rpc('array_append_unique', {
              arr: 'badges',
              el: 'solution_provider'
            })
          })
          .eq('id', post.author_id);

        if (badgeError) {
          console.error('Error adding badge:', badgeError);
        }
      }

      // Add achievement
      const { error: achievementError } = await supabase
        .from('user_achievements')
        .insert({
          user_id: post.author_id,
          achievement_type: 'solution_milestone',
          achievement_data: {
            count: solutionCount,
            post_id: postId,
            topic_id: topicId
          }
        });

      if (achievementError) {
        console.error('Error creating achievement:', achievementError);
      }
    }
  }

  return data as ForumPost;
}

// Moderation functions

// Edit a forum post
export async function editForumPost({
  postId,
  content,
  moderatorId,
  reason
}: {
  postId: string;
  content: string;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  // Get the original post first
  const { data: originalPost, error: fetchError } = await supabase
    .from('forum_posts') // Corrected table name
    .select('*')
    .eq('id', postId)
    .single();

  if (fetchError) {
    console.error('Error fetching post to edit:', fetchError);
    throw fetchError;
  }

  // Update the post
  const { data, error } = await supabase
    .from('forum_posts') // Corrected table name
    .update({
      content,
      updated_at: new Date().toISOString()
    })
    .eq('id', postId)
    .select()
    .single();

  if (error) {
    console.error('Error editing post:', error);
    throw error;
  }

  // Log the moderation action using the helper function
  try {
    await logModerationAction({
      actionType: 'edit_post',
      moderatorId,
      contentId: postId,
      contentType: 'forum_post',
      reason: reason || 'Content edited by moderator',
      previousContent: originalPost.content,
      newContent: content
    });
  } catch (logError) {
    console.error('Error logging moderation action:', logError);
    // Don't throw here, as the edit action was successful
  }

  return data as ForumPost;
}

// Delete a forum post
export async function deleteForumPost({
  postId,
  moderatorId,
  reason,
  hardDelete = false
}: {
  postId: string;
  moderatorId: string;
  reason?: string;
  hardDelete?: boolean;
}) {
  const supabase = createClient();

  // Get the post first to store its content
  const { data: post, error: fetchError } = await supabase
    .from('forum_posts') // Corrected table name
    .select('*')
    .eq('id', postId)
    .single();

  if (fetchError) {
    console.error('Error fetching post to delete:', fetchError);
    throw fetchError;
  }

  if (hardDelete) {
    // Permanently delete the post
    const { error } = await supabase
      .from('forum_posts') // Corrected table name
      .delete()
      .eq('id', postId);

    if (error) {
      console.error('Error deleting post:', error);
      throw error;
    }
  } else {
    // Soft delete - update the post to mark it as deleted
    const { error } = await supabase
      .from('forum_posts') // Corrected table name
      .update({
        content: '[This post has been removed by a moderator]',
        is_deleted: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', postId);

    if (error) {
      console.error('Error soft-deleting post:', error);
      throw error;
    }
  }

  // Log the moderation action using the helper function
  try {
    await logModerationAction({
      actionType: hardDelete ? 'hard_delete_post' : 'soft_delete_post',
      moderatorId,
      contentId: postId,
      contentType: 'forum_post',
      reason: reason || 'Content removed by moderator',
      previousContent: post.content
    });
  } catch (logError) {
    console.error('Error logging moderation action:', logError);
    // Don't throw here, as the delete action was successful
  }

  return { success: true, message: hardDelete ? 'Post permanently deleted' : 'Post removed' };
}

// Lock or unlock a topic
export async function setTopicLockStatus({
  topicId,
  isLocked,
  moderatorId,
  reason
}: {
  topicId: string;
  isLocked: boolean;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  try {
    console.log(`Setting topic ${topicId} lock status to ${isLocked}`);

    // First check if the topic exists and get its current state
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`,
        data: null
      };
    }

    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`,
        data: null
      };
    }

    // If the topic is already in the desired state, just return success
    if (topic.is_locked === isLocked) {
      console.log(`Topic ${topicId} is already ${isLocked ? 'locked' : 'unlocked'}`);
      return {
        success: true,
        data: topic,
        error: null
      };
    }

    // Use a different approach - update without returning data first
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_locked: isLocked,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);

    if (updateError) {
      console.error('Error updating topic lock status:', updateError);
      return {
        success: false,
        error: `Error updating topic lock status: ${updateError.message}`,
        data: null
      };
    }

    // Then fetch the updated topic separately
    const { data: updatedTopic, error: fetchError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (fetchError) {
      console.error('Error fetching updated topic:', fetchError);
      return {
        success: false,
        error: `Error fetching updated topic: ${fetchError.message}`,
        data: null
      };
    }

    if (!updatedTopic) {
      console.error(`Topic with ID ${topicId} not found after update`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found after update`,
        data: null
      };
    }

    // Log the moderation action using the helper function
    try {
      await logModerationAction({
        actionType: isLocked ? 'lock_topic' : 'unlock_topic',
        moderatorId,
        contentId: topicId,
        contentType: 'forum_topic',
        reason: reason || (isLocked ? 'Topic locked by moderator' : 'Topic unlocked by moderator')
      });
    } catch (logError) {
      console.error('Error logging moderation action:', logError);
      // Don't throw here, as the lock/unlock action was successful
    }

    return {
      success: true,
      data: updatedTopic as ForumTopic,
      error: null
    };
  } catch (err) {
    console.error('Exception in setTopicLockStatus:', err);
    return {
      success: false,
      data: null,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}

// Pin or unpin a topic
export async function setTopicPinStatus({
  topicId,
  isPinned,
  moderatorId,
  reason
}: {
  topicId: string;
  isPinned: boolean;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  try {
    console.log(`Setting topic ${topicId} pin status to ${isPinned}`);

    // First check if the topic exists and get its current state
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`,
        data: null
      };
    }

    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`,
        data: null
      };
    }

    // If the topic is already in the desired state, just return success
    if (topic.is_pinned === isPinned) {
      console.log(`Topic ${topicId} is already ${isPinned ? 'pinned' : 'unpinned'}`);
      return {
        success: true,
        data: topic,
        error: null
      };
    }

    // Use a different approach - update without returning data first
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_pinned: isPinned,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);

    if (updateError) {
      console.error('Error updating topic pin status:', updateError);
      return {
        success: false,
        error: `Error updating topic pin status: ${updateError.message}`,
        data: null
      };
    }

    // Then fetch the updated topic separately
    const { data: updatedTopic, error: fetchError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (fetchError) {
      console.error('Error fetching updated topic:', fetchError);
      return {
        success: false,
        error: `Error fetching updated topic: ${fetchError.message}`,
        data: null
      };
    }

    if (!updatedTopic) {
      console.error(`Topic with ID ${topicId} not found after update`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found after update`,
        data: null
      };
    }

    // Log the moderation action using the helper function
    try {
      await logModerationAction({
        actionType: isPinned ? 'pin_topic' : 'unpin_topic',
        moderatorId,
        contentId: topicId,
        contentType: 'forum_topic',
        reason: reason || (isPinned ? 'Topic pinned by moderator' : 'Topic unpinned by moderator')
      });
    } catch (logError) {
      console.error('Error logging moderation action:', logError);
      // Don't throw here, as the pin/unpin action was successful
    }

    return {
      success: true,
      data: updatedTopic as ForumTopic,
      error: null
    };
  } catch (err) {
    console.error('Exception in setTopicPinStatus:', err);
    return {
      success: false,
      data: null,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}

// Move a topic to a different category
export async function moveTopicToCategory({
  topicId,
  newCategoryId,
  moderatorId,
  reason
}: {
  topicId: string;
  newCategoryId: string;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  // Get the original category first
  const { data: originalTopic, error: fetchError } = await supabase
    .from('forum_topics')
    .select('category_id')
    .eq('id', topicId)
    .single();

  if (fetchError) {
    console.error('Error fetching topic to move:', fetchError);
    throw fetchError;
  }

  // Update the topic
  const { data, error } = await supabase
    .from('forum_topics')
    .update({
      category_id: newCategoryId,
      updated_at: new Date().toISOString()
    })
    .eq('id', topicId)
    .select()
    .single();

  if (error) {
    console.error('Error moving topic:', error);
    throw error;
  }

  // Log the moderation action using the helper function
  try {
    await logModerationAction({
      actionType: 'move_topic',
      moderatorId,
      contentId: topicId,
      contentType: 'forum_topic',
      reason: reason || 'Topic moved to another category',
      additionalData: {
        from_category_id: originalTopic.category_id,
        to_category_id: newCategoryId
      }
    });
  } catch (logError) {
    console.error('Error logging moderation action:', logError);
    // Don't throw here, as the move action was successful
  }

  return data as ForumTopic;
}

// Delete a topic
export async function deleteForumTopic({
  topicId,
  moderatorId,
  reason,
  hardDelete = false
}: {
  topicId: string;
  moderatorId: string;
  reason?: string;
  hardDelete?: boolean;
}) {
  const supabase = createClient();

  // Get the topic first to store its data
  const { data: topic, error: fetchError } = await supabase
    .from('forum_topics')
    .select('*')
    .eq('id', topicId)
    .single();

  if (fetchError) {
    console.error('Error fetching topic to delete:', fetchError);
    throw fetchError;
  }

  if (hardDelete) {
    // First delete all posts in the topic
    const { error: postsDeleteError } = await supabase
      .from('forum_posts') // Corrected table name
      .delete()
      .eq('topic_id', topicId);

    if (postsDeleteError) {
      console.error('Error deleting topic posts:', postsDeleteError);
      throw postsDeleteError;
    }

    // Then delete the topic
    const { error } = await supabase
      .from('forum_topics')
      .delete()
      .eq('id', topicId);

    if (error) {
      console.error('Error deleting topic:', error);
      throw error;
    }
  } else {
    // Soft delete - update the topic to mark it as deleted
    const { error } = await supabase
      .from('forum_topics')
      .update({
        is_deleted: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);

    if (error) {
      console.error('Error soft-deleting topic:', error);
      throw error;
    }
  }

  // Log the moderation action using the helper function
  try {
    await logModerationAction({
      actionType: hardDelete ? 'hard_delete_topic' : 'soft_delete_topic',
      moderatorId,
      contentId: topicId,
      contentType: 'forum_topic',
      reason: reason || 'Topic removed by moderator',
      additionalData: {
        topic_title: topic.title,
        category_id: topic.category_id
      }
    });
  } catch (logError) {
    console.error('Error logging moderation action:', logError);
    // Don't throw here, as the delete action was successful
  }

  return { success: true, message: hardDelete ? 'Topic permanently deleted' : 'Topic removed' };
}

// Get moderation actions
export async function getModerationActions({
  limit = 20,
  offset = 0,
  contentType,
  actionType,
  moderatorId
}: {
  limit?: number;
  offset?: number;
  contentType?: string;
  actionType?: string;
  moderatorId?: string;
}) {
  // Use the function from moderation.ts instead
  try {
    const { data, count, error } = await import('@/lib/moderation').then(mod => {
      return mod.getModerationActions({
        limit,
        offset,
        contentType,
        actionType,
        moderatorId
      });
    });

    if (error) {
      console.error('Error from moderation.getModerationActions:', error);
      return { data: [], count: 0 };
    }

    return { data, count };
  } catch (err) {
    console.error('Error calling moderation.getModerationActions:', err);
    return { data: [], count: 0 };
  }
}

// User Banning/Suspension System

// Ban or suspend a user
export async function banUser({
  userId,
  moderatorId,
  reason,
  expiresAt = null, // If null, it's a permanent ban
  banType = 'full' // 'full', 'post_only', 'read_only'
}: {
  userId: string;
  moderatorId: string;
  reason: string;
  expiresAt?: string | null;
  banType?: 'full' | 'post_only' | 'read_only';
}) {
  const supabase = createClient();

  // Check if user is already banned
  const { data: existingBan, error: checkError } = await supabase
    .from('user_bans')
    .select('*')
    .eq('user_id', userId)
    .is('revoked_at', null)
    .maybeSingle();

  if (checkError) {
    console.error('Error checking existing ban:', checkError);
    throw checkError;
  }

  // If user is already banned, update the ban
  if (existingBan) {
    const { error: updateError } = await supabase
      .from('user_bans')
      .update({
        ban_type: banType,
        reason,
        expires_at: expiresAt,
        updated_at: new Date().toISOString(),
        updated_by: moderatorId
      })
      .eq('id', existingBan.id);

    if (updateError) {
      console.error('Error updating ban:', updateError);
      throw updateError;
    }

    // Log the moderation action
    await logModerationAction({
      actionType: 'update_ban',
      moderatorId,
      contentId: userId,
      contentType: 'user',
      reason,
      additionalData: {
        ban_type: banType,
        expires_at: expiresAt
      }
    });

    return { success: true, message: 'User ban updated', banId: existingBan.id };
  }

  // Create a new ban
  const { data: ban, error } = await supabase
    .from('user_bans')
    .insert({
      user_id: userId,
      moderator_id: moderatorId,
      ban_type: banType,
      reason,
      expires_at: expiresAt,
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error('Error banning user:', error);
    throw error;
  }

  // Update the user's status in the profiles table
  const { error: profileError } = await supabase
    .from('profiles')
    .update({
      is_banned: true,
      ban_expires_at: expiresAt,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId);

  if (profileError) {
    console.error('Error updating user profile:', profileError);
    throw profileError;
  }

  // Log the moderation action
  await logModerationAction({
    actionType: 'ban_user',
    moderatorId,
    contentId: userId,
    contentType: 'user',
    reason,
    additionalData: {
      ban_type: banType,
      expires_at: expiresAt
    }
  });

  return { success: true, message: 'User banned successfully', banId: ban.id };
}

// Unban a user
export async function unbanUser({
  userId,
  moderatorId,
  reason
}: {
  userId: string;
  moderatorId: string;
  reason: string;
}) {
  const supabase = createClient();

  // Find the active ban
  const { data: activeBan, error: findError } = await supabase
    .from('user_bans')
    .select('id')
    .eq('user_id', userId)
    .is('revoked_at', null)
    .maybeSingle();

  if (findError) {
    console.error('Error finding active ban:', findError);
    throw findError;
  }

  if (!activeBan) {
    return { success: false, message: 'No active ban found for this user' };
  }

  // Revoke the ban
  const { error: updateError } = await supabase
    .from('user_bans')
    .update({
      revoked_at: new Date().toISOString(),
      revoked_by: moderatorId,
      revoke_reason: reason
    })
    .eq('id', activeBan.id);

  if (updateError) {
    console.error('Error revoking ban:', updateError);
    throw updateError;
  }

  // Update the user's status in the profiles table
  const { error: profileError } = await supabase
    .from('profiles')
    .update({
      is_banned: false,
      ban_expires_at: null,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId);

  if (profileError) {
    console.error('Error updating user profile:', profileError);
    throw profileError;
  }

  // Log the moderation action
  await logModerationAction({
    actionType: 'unban_user',
    moderatorId,
    contentId: userId,
    contentType: 'user',
    reason
  });

  return { success: true, message: 'User unbanned successfully' };
}

// Get banned users
export async function getBannedUsers({
  limit = 20,
  offset = 0,
  includeExpired = false
}: {
  limit?: number;
  offset?: number;
  includeExpired?: boolean;
}) {
  const supabase = createClient();
  const now = new Date().toISOString();

  let query = supabase
    .from('user_bans')
    .select(`
      *,
      user:user_id (id, username, full_name, email, avatar_url),
      moderator:moderator_id (id, username, full_name)
    `, { count: 'exact' })
    .is('revoked_at', null);

  if (!includeExpired) {
    query = query.or(`expires_at.is.null,expires_at.gt.${now}`);
  }

  const { data, error, count } = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error fetching banned users:', error);
    throw error;
  }

  return { data, count };
}

// Check if a user is banned
export async function checkUserBanStatus(userId: string) {
  const supabase = createClient();
  const now = new Date().toISOString();

  // Check for active bans
  const { data, error } = await supabase
    .from('user_bans')
    .select('*')
    .eq('user_id', userId)
    .is('revoked_at', null)
    .or(`expires_at.is.null,expires_at.gt.${now}`)
    .maybeSingle();

  if (error) {
    console.error('Error checking user ban status:', error);
    throw error;
  }

  if (!data) {
    return { isBanned: false };
  }

  return {
    isBanned: true,
    banType: data.ban_type,
    reason: data.reason,
    expiresAt: data.expires_at,
    banId: data.id
  };
}

// Using the imported logModerationAction function from moderation.ts

// Content Filtering System

// List of banned words/phrases for automatic filtering
const BANNED_WORDS = [
  'badword1',
  'badword2',
  'offensive1',
  'offensive2',
  // Add more banned words as needed
];

// Check content for banned words
export function checkContentForBannedWords(content: string) {
  if (!content) return { hasBannedWords: false, matches: [] };

  const contentLower = content.toLowerCase();
  const matches = BANNED_WORDS.filter(word => contentLower.includes(word.toLowerCase()));

  return {
    hasBannedWords: matches.length > 0,
    matches
  };
}

// Filter content by replacing banned words with asterisks
export function filterContent(content: string) {
  if (!content) return content;

  let filteredContent = content;
  BANNED_WORDS.forEach(word => {
    const regex = new RegExp(word, 'gi');
    filteredContent = filteredContent.replace(regex, '*'.repeat(word.length));
  });

  return filteredContent;
}

// Automatically moderate content before posting
export async function autoModerateContent({
  content,
  userId,
  contentType
}: {
  content: string;
  userId: string;
  contentType: 'post' | 'topic';
}) {
  // Check for banned words
  const { hasBannedWords, matches } = checkContentForBannedWords(content);

  if (hasBannedWords) {
    // Filter the content
    const filteredContent = filterContent(content);

    // Log the automatic moderation
    const supabase = createClient();
    await supabase
      .from('auto_moderation_logs')
      .insert({
        user_id: userId,
        content_type: contentType,
        original_content: content,
        filtered_content: filteredContent,
        matched_words: matches,
        created_at: new Date().toISOString()
      });

    return {
      isModerated: true,
      filteredContent,
      reason: `Content contained banned words: ${matches.join(', ')}`
    };
  }

  return {
    isModerated: false,
    filteredContent: content
  };
}

// Get recent forum activity
export async function getRecentForumActivity(limit = 10) {
  const supabase = createClient();
  let recentPosts = [];

  // Get recent posts
  try {
    const { data, error: postsError } = await supabase
      .from('forum_posts')
      .select(`
        id,
        content,
        created_at,
        topic_id,
        author:author_id (username, full_name, avatar_url, is_verified_expert),
        topic:topic_id (title, slug, category_id),
        topic.category:category_id (name, slug)
      `)
      .is('is_deleted', null)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (postsError) {
      console.error('Error fetching recent posts:', postsError);
      // Log error but don't throw, continue with empty array
    } else {
      recentPosts = data || [];
    }
  } catch (err) {
    console.error('Exception fetching recent posts:', err);
    // Continue with empty array
  }

  // Get recent topics
  let recentTopics = [];
  try {
    const { data, error: topicsError } = await supabase
      .from('forum_topics')
      .select(`
        id,
        title,
        slug,
        created_at,
        author:author_id (username, full_name, avatar_url, is_verified_expert),
        category:category_id (name, slug)
      `)
      .is('is_deleted', null)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (topicsError) {
      console.error('Error fetching recent topics:', topicsError);
      // Log error but don't throw, continue with empty array
    } else {
      recentTopics = data || [];
    }
  } catch (err) {
    console.error('Exception fetching recent topics:', err);
    // Continue with empty array
  }

  // Combine and sort by date
  try {
    const activity = [
      ...recentPosts.map(post => ({
        type: 'post',
        id: post.id,
        content: post.content,
        created_at: post.created_at,
        author: post.author,
        topic: {
          id: post.topic_id,
          title: post.topic?.title || 'Unknown Topic',
          slug: post.topic?.slug || ''
        },
        category: post.topic?.category || { name: 'Unknown', slug: '' }
      })),
      ...recentTopics.map(topic => ({
        type: 'topic',
        id: topic.id,
        title: topic.title || 'Unknown Topic',
        slug: topic.slug || '',
        created_at: topic.created_at,
        author: topic.author,
        category: topic.category || { name: 'Unknown', slug: '' }
      }))
    ]
    .filter(item => item && item.created_at) // Filter out any items with missing created_at
    .sort((a, b) => {
      try {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      } catch (err) {
        console.error('Error sorting activity items:', err);
        return 0;
      }
    })
    .slice(0, limit);

    return activity;
  } catch (err) {
    console.error('Error combining forum activity:', err);
    return []; // Return empty array instead of throwing
  }
}

// Bulk Moderation System

// Bulk delete posts
export async function bulkDeletePosts({
  postIds,
  moderatorId,
  reason,
  hardDelete = false
}: {
  postIds: string[];
  moderatorId: string;
  reason: string;
  hardDelete?: boolean;
}) {
  const supabase = createClient();
  const results = { success: [], failed: [] };

  // Process each post
  for (const postId of postIds) {
    try {
      // Get the post first to store its content
      const { data: post, error: fetchError } = await supabase
        .from('forum_posts') // Corrected table name
        .select('*')
        .eq('id', postId)
        .single();

      if (fetchError) {
        console.error(`Error fetching post ${postId}:`, fetchError);
        results.failed.push({ id: postId, error: fetchError.message });
        continue;
      }

      if (hardDelete) {
        // Permanently delete the post
        const { error } = await supabase
          .from('forum_posts') // Corrected table name
          .delete()
          .eq('id', postId);

        if (error) {
          console.error(`Error deleting post ${postId}:`, error);
          results.failed.push({ id: postId, error: error.message });
          continue;
        }
      } else {
        // Soft delete - update the post to mark it as deleted
        const { error } = await supabase
          .from('forum_posts') // Corrected table name
          .update({
            content: '[This post has been removed by a moderator]',
            is_deleted: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', postId);

        if (error) {
          console.error(`Error soft-deleting post ${postId}:`, error);
          results.failed.push({ id: postId, error: error.message });
          continue;
        }
      }

      // Log the moderation action
      await logModerationAction({
        actionType: hardDelete ? 'hard_delete_post' : 'soft_delete_post',
        moderatorId,
        contentId: postId,
        contentType: 'forum_post',
        reason: reason || 'Content removed by moderator in bulk action',
        previousContent: post.content
      });

      results.success.push(postId);
    } catch (err: any) {
      console.error(`Error processing post ${postId}:`, err);
      results.failed.push({ id: postId, error: err.message });
    }
  }

  return results;
}

// Bulk delete topics
export async function bulkDeleteTopics({
  topicIds,
  moderatorId,
  reason,
  hardDelete = false
}: {
  topicIds: string[];
  moderatorId: string;
  reason: string;
  hardDelete?: boolean;
}) {
  const supabase = createClient();
  const results = { success: [], failed: [] };

  // Process each topic
  for (const topicId of topicIds) {
    try {
      // Get the topic first to store its data
      const { data: topic, error: fetchError } = await supabase
        .from('forum_topics')
        .select('*')
        .eq('id', topicId)
        .single();

      if (fetchError) {
        console.error(`Error fetching topic ${topicId}:`, fetchError);
        results.failed.push({ id: topicId, error: fetchError.message });
        continue;
      }

      if (hardDelete) {
        // First delete all posts in the topic
        const { error: postsDeleteError } = await supabase
          .from('forum_posts') // Corrected table name
          .delete()
          .eq('topic_id', topicId);

        if (postsDeleteError) {
          console.error(`Error deleting posts for topic ${topicId}:`, postsDeleteError);
          results.failed.push({ id: topicId, error: postsDeleteError.message });
          continue;
        }

        // Then delete the topic
        const { error } = await supabase
          .from('forum_topics')
          .delete()
          .eq('id', topicId);

        if (error) {
          console.error(`Error deleting topic ${topicId}:`, error);
          results.failed.push({ id: topicId, error: error.message });
          continue;
        }
      } else {
        // Soft delete - update the topic to mark it as deleted
        const { error } = await supabase
          .from('forum_topics')
          .update({
            is_deleted: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', topicId);

        if (error) {
          console.error(`Error soft-deleting topic ${topicId}:`, error);
          results.failed.push({ id: topicId, error: error.message });
          continue;
        }
      }

      // Log the moderation action
      await logModerationAction({
        actionType: hardDelete ? 'hard_delete_topic' : 'soft_delete_topic',
        moderatorId,
        contentId: topicId,
        contentType: 'forum_topic',
        reason: reason || 'Topic removed by moderator in bulk action',
        additionalData: {
          topic_title: topic.title,
          category_id: topic.category_id
        }
      });

      results.success.push(topicId);
    } catch (err: any) {
      console.error(`Error processing topic ${topicId}:`, err);
      results.failed.push({ id: topicId, error: err.message });
    }
  }

  return results;
}

// Bulk lock/unlock topics
export async function bulkSetTopicLockStatus({
  topicIds,
  isLocked,
  moderatorId,
  reason
}: {
  topicIds: string[];
  isLocked: boolean;
  moderatorId: string;
  reason: string;
}) {
  const supabase = createClient();
  const results = { success: [], failed: [] };

  // Process each topic
  for (const topicId of topicIds) {
    try {
      // Update the topic
      const { error } = await supabase
        .from('forum_topics')
        .update({
          is_locked: isLocked,
          updated_at: new Date().toISOString()
        })
        .eq('id', topicId);

      if (error) {
        console.error(`Error updating lock status for topic ${topicId}:`, error);
        results.failed.push({ id: topicId, error: error.message });
        continue;
      }

      // Log the moderation action
      await logModerationAction({
        actionType: isLocked ? 'lock_topic' : 'unlock_topic',
        moderatorId,
        contentId: topicId,
        contentType: 'forum_topic',
        reason: reason || (isLocked ? 'Topic locked by moderator in bulk action' : 'Topic unlocked by moderator in bulk action')
      });

      results.success.push(topicId);
    } catch (err: any) {
      console.error(`Error processing topic ${topicId}:`, err);
      results.failed.push({ id: topicId, error: err.message });
    }
  }

  return results;
}

// Pin a topic
export async function pinTopic({
  topicId,
  moderatorId,
  reason
}: {
  topicId: string;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  try {
    console.log(`Pinning topic ${topicId}`);

    // First check if the topic exists
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`,
        data: null
      };
    }

    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`,
        data: null
      };
    }

    // If already pinned, just return success
    if (topic.is_pinned) {
      console.log(`Topic ${topicId} is already pinned`);
      return {
        success: true,
        data: topic,
        error: null
      };
    }

    // Update without returning data
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_pinned: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);

    if (updateError) {
      console.error('Error pinning topic:', updateError);
      return {
        success: false,
        error: `Error pinning topic: ${updateError.message}`,
        data: null
      };
    }

    // Log the action
    await logModerationAction({
      actionType: 'pin_topic',
      moderatorId,
      contentId: topicId,
      contentType: 'forum_topic',
      reason: reason || 'Topic pinned by moderator'
    });

    return {
      success: true,
      data: { ...topic, is_pinned: true },
      error: null
    };
  } catch (err) {
    console.error('Exception in pinTopic:', err);
    return {
      success: false,
      data: null,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}

// Unpin a topic
export async function unpinTopic({
  topicId,
  moderatorId,
  reason
}: {
  topicId: string;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  try {
    console.log(`Unpinning topic ${topicId}`);

    // First check if the topic exists
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`,
        data: null
      };
    }

    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`,
        data: null
      };
    }

    // If already unpinned, just return success
    if (!topic.is_pinned) {
      console.log(`Topic ${topicId} is already unpinned`);
      return {
        success: true,
        data: topic,
        error: null
      };
    }

    // Update without returning data
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_pinned: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);

    if (updateError) {
      console.error('Error unpinning topic:', updateError);
      return {
        success: false,
        error: `Error unpinning topic: ${updateError.message}`,
        data: null
      };
    }

    // Log the action
    await logModerationAction({
      actionType: 'unpin_topic',
      moderatorId,
      contentId: topicId,
      contentType: 'forum_topic',
      reason: reason || 'Topic unpinned by moderator'
    });

    return {
      success: true,
      data: { ...topic, is_pinned: false },
      error: null
    };
  } catch (err) {
    console.error('Exception in unpinTopic:', err);
    return {
      success: false,
      data: null,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}

// Lock a topic
export async function lockTopic({
  topicId,
  moderatorId,
  reason
}: {
  topicId: string;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  try {
    console.log(`Locking topic ${topicId}`);

    // First check if the topic exists
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`,
        data: null
      };
    }

    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`,
        data: null
      };
    }

    // If already locked, just return success
    if (topic.is_locked) {
      console.log(`Topic ${topicId} is already locked`);
      return {
        success: true,
        data: topic,
        error: null
      };
    }

    // Update without returning data
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_locked: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);

    if (updateError) {
      console.error('Error locking topic:', updateError);
      return {
        success: false,
        error: `Error locking topic: ${updateError.message}`,
        data: null
      };
    }

    // Log the action
    await logModerationAction({
      actionType: 'lock_topic',
      moderatorId,
      contentId: topicId,
      contentType: 'forum_topic',
      reason: reason || 'Topic locked by moderator'
    });

    return {
      success: true,
      data: { ...topic, is_locked: true },
      error: null
    };
  } catch (err) {
    console.error('Exception in lockTopic:', err);
    return {
      success: false,
      data: null,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}

// Unlock a topic
export async function unlockTopic({
  topicId,
  moderatorId,
  reason
}: {
  topicId: string;
  moderatorId: string;
  reason?: string;
}) {
  const supabase = createClient();

  try {
    console.log(`Unlocking topic ${topicId}`);

    // First check if the topic exists
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('*')
      .eq('id', topicId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`,
        data: null
      };
    }

    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`,
        data: null
      };
    }

    // If already unlocked, just return success
    if (!topic.is_locked) {
      console.log(`Topic ${topicId} is already unlocked`);
      return {
        success: true,
        data: topic,
        error: null
      };
    }

    // Update without returning data
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_locked: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);

    if (updateError) {
      console.error('Error unlocking topic:', updateError);
      return {
        success: false,
        error: `Error unlocking topic: ${updateError.message}`,
        data: null
      };
    }

    // Log the action
    await logModerationAction({
      actionType: 'unlock_topic',
      moderatorId,
      contentId: topicId,
      contentType: 'forum_topic',
      reason: reason || 'Topic unlocked by moderator'
    });

    return {
      success: true,
      data: { ...topic, is_locked: false },
      error: null
    };
  } catch (err) {
    console.error('Exception in unlockTopic:', err);
    return {
      success: false,
      data: null,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}

// Bulk pin/unpin topics
export async function bulkSetTopicPinStatus({
  topicIds,
  isPinned,
  moderatorId,
  reason
}: {
  topicIds: string[];
  isPinned: boolean;
  moderatorId: string;
  reason: string;
}) {
  const supabase = createClient();
  const results = { success: [], failed: [] };

  // Process each topic
  for (const topicId of topicIds) {
    try {
      // First check if the topic exists
      const { data: topicExists, error: checkError } = await supabase
        .from('forum_topics')
        .select('id')
        .eq('id', topicId)
        .maybeSingle();

      if (checkError) {
        console.error(`Error checking if topic ${topicId} exists:`, checkError);
        results.failed.push({ id: topicId, error: checkError.message });
        continue;
      }

      if (!topicExists) {
        console.error(`Topic with ID ${topicId} not found`);
        results.failed.push({ id: topicId, error: `Topic with ID ${topicId} not found` });
        continue;
      }

      // Update the topic
      const { error } = await supabase
        .from('forum_topics')
        .update({
          is_pinned: isPinned,
          updated_at: new Date().toISOString()
        })
        .eq('id', topicId);

      if (error) {
        console.error(`Error updating pin status for topic ${topicId}:`, error);
        results.failed.push({ id: topicId, error: error.message });
        continue;
      }

      // Log the moderation action
      try {
        await logModerationAction({
          actionType: isPinned ? 'pin_topic' : 'unpin_topic',
          moderatorId,
          contentId: topicId,
          contentType: 'forum_topic',
          reason: reason || (isPinned ? 'Topic pinned by moderator in bulk action' : 'Topic unpinned by moderator in bulk action')
        });
      } catch (logError) {
        console.error(`Error logging moderation action for topic ${topicId}:`, logError);
        // Don't fail the operation if logging fails
      }

      results.success.push(topicId);
    } catch (err: any) {
      console.error(`Error processing topic ${topicId}:`, err);
      results.failed.push({ id: topicId, error: err.message });
    }
  }

  return results;
}

// Bulk move topics to a different category
export async function bulkMoveTopicsToCategory({
  topicIds,
  newCategoryId,
  moderatorId,
  reason
}: {
  topicIds: string[];
  newCategoryId: string;
  moderatorId: string;
  reason: string;
}) {
  const supabase = createClient();
  const results = { success: [], failed: [] };

  // Process each topic
  for (const topicId of topicIds) {
    try {
      // Get the original category first
      const { data: originalTopic, error: fetchError } = await supabase
        .from('forum_topics')
        .select('category_id')
        .eq('id', topicId)
        .single();

      if (fetchError) {
        console.error(`Error fetching topic ${topicId}:`, fetchError);
        results.failed.push({ id: topicId, error: fetchError.message });
        continue;
      }

      // Update the topic
      const { error } = await supabase
        .from('forum_topics')
        .update({
          category_id: newCategoryId,
          updated_at: new Date().toISOString()
        })
        .eq('id', topicId);

      if (error) {
        console.error(`Error moving topic ${topicId}:`, error);
        results.failed.push({ id: topicId, error: error.message });
        continue;
      }

      // Log the moderation action
      await logModerationAction({
        actionType: 'move_topic',
        moderatorId,
        contentId: topicId,
        contentType: 'forum_topic',
        reason: reason || 'Topic moved to another category in bulk action',
        additionalData: {
          from_category_id: originalTopic.category_id,
          to_category_id: newCategoryId
        }
      });

      results.success.push(topicId);
    } catch (err: any) {
      console.error(`Error processing topic ${topicId}:`, err);
      results.failed.push({ id: topicId, error: err.message });
    }
  }

  return results;
}

// Moderation Queue System

// Get reported content for moderation queue
export async function getReportedContent({
  limit = 20,
  offset = 0,
  status = 'pending',
  contentType
}: {
  limit?: number;
  offset?: number;
  status?: 'pending' | 'resolved' | 'dismissed' | 'all';
  contentType?: 'post' | 'topic' | 'all';
}) {
  const supabase = createClient();

  let query = supabase
    .from('reported_content')
    .select(`
      *,
      reporter:reporter_id (username, full_name, avatar_url)
    `, { count: 'exact' });

  // Apply status filter
  if (status !== 'all') {
    query = query.eq('status', status);
  }

  // Apply content type filter
  if (contentType && contentType !== 'all') {
    query = query.eq('content_type', contentType);
  }

  const { data, error, count } = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error fetching reported content:', error);
    throw error;
  }

  // Fetch the actual content for each report
  const reportsWithContent = await Promise.all(
    (data || []).map(async (report) => {
      try {
        if (report.content_type === 'topic') {
          const { data: topic } = await supabase
            .from('forum_topics') // Corrected table name
            .select(`
              *,
              author:author_id (username, full_name, avatar_url),
              category:category_id (name, slug)
            `)
            .eq('id', report.content_id)
            .single();

          return { ...report, content: topic };
        } else if (report.content_type === 'post') {
          const { data: post } = await supabase
            .from('forum_posts') // Corrected table name
            .select(`
              *,
              author:author_id (username, full_name, avatar_url),
              topic:topic_id (title, slug, category:category_id (name, slug))
            `)
            .eq('id', report.content_id)
            .single();

          return { ...report, content: post };
        }
        return report;
      } catch (err) {
        console.error(`Error fetching content for report ${report.id}:`, err);
        return { ...report, content: null, error: 'Content not found' };
      }
    })
  );

  return { data: reportsWithContent, count };
}

// Update report status
export async function updateReportStatus({
  reportId,
  status,
  moderatorId,
  notes
}: {
  reportId: string;
  status: 'resolved' | 'dismissed';
  moderatorId: string;
  notes?: string;
}) {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('reported_content')
    .update({
      status,
      moderator_id: moderatorId,
      moderator_notes: notes,
      resolved_at: new Date().toISOString()
    })
    .eq('id', reportId)
    .select()
    .single();

  if (error) {
    console.error('Error updating report status:', error);
    throw error;
  }

  // Log the moderation action
  await logModerationAction({
    actionType: `${status}_report`,
    moderatorId,
    contentId: reportId,
    contentType: 'report',
    reason: notes || `Report ${status} by moderator`,
    additionalData: {
      content_id: data.content_id,
      content_type: data.content_type
    }
  });

  return data;
}

// Report content
export async function reportContent({
  contentId,
  contentType,
  reporterId,
  reason
}: {
  contentId: string;
  contentType: 'post' | 'topic';
  reporterId: string;
  reason: string;
}) {
  const supabase = createClient();

  // Check if this content has already been reported by this user
  const { data: existingReport, error: checkError } = await supabase
    .from('reported_content')
    .select('id')
    .eq('content_id', contentId)
    .eq('content_type', contentType)
    .eq('reporter_id', reporterId)
    .eq('status', 'pending')
    .maybeSingle();

  if (checkError) {
    console.error('Error checking existing report:', checkError);
    throw checkError;
  }

  if (existingReport) {
    return { success: false, message: 'You have already reported this content', reportId: existingReport.id };
  }

  // Create the report
  const { data, error } = await supabase
    .from('reported_content')
    .insert({
      content_id: contentId,
      content_type: contentType,
      reporter_id: reporterId,
      reason,
      status: 'pending',
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error('Error reporting content:', error);
    throw error;
  }

  return { success: true, message: 'Content reported successfully', reportId: data.id };
}