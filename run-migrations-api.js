const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Replace with your Supabase URL and service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Migration files in order
const migrationFiles = [
  '20250601_forum_system.sql',
  '20250602_activities_notifications.sql',
  '20250603_reporting_system.sql',
  '20250604_search_function.sql',
  '20250605_rate_limiting.sql'
];

async function runMigrations() {
  for (const file of migrationFiles) {
    try {
      console.log(`Running migration: ${file}`);
      const filePath = path.join(__dirname, 'supabase', 'migrations', file);
      const sql = fs.readFileSync(filePath, 'utf8');
      
      // Split the SQL file into separate statements
      const statements = sql.split(';').filter(stmt => stmt.trim());
      
      for (const statement of statements) {
        if (statement.trim()) {
          const { error } = await supabase.rpc('pgbouncer_exec', { query: statement });
          if (error) {
            console.error(`Error executing statement: ${error.message}`);
            console.error(statement);
          }
        }
      }
      
      console.log(`Migration ${file} completed successfully`);
    } catch (error) {
      console.error(`Error running migration ${file}: ${error.message}`);
    }
  }
}

runMigrations()
  .then(() => console.log('All migrations completed'))
  .catch(err => console.error('Migration failed:', err));
