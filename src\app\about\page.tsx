'use client';

import React from 'react';

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-lg p-8">
        <h1 className="text-3xl font-bold mb-6 text-gray-900">About NatureHeals.info</h1>
        <p className="text-lg text-gray-800 mb-6">
          <span className="font-semibold text-nature-green">NatureHeals.info</span> is a collaborative, community-driven resource dedicated to holistic healing, natural remedies, and integrative wellness. Our mission is to empower individuals with accurate, accessible, and well-researched information about the healing power of nature.
        </p>
        <h2 className="text-2xl font-semibold mb-4 text-gray-900">Our Mission</h2>
        <p className="text-gray-800 mb-6">
          We believe in the value of traditional knowledge, scientific research, and the wisdom of diverse healing traditions. NatureHeals.info brings together contributors from around the world to share insights on plants, therapies, nutrition, and mind-body practices that support health and well-being.
        </p>
        <h2 className="text-2xl font-semibold mb-4 text-gray-900">What You&apos;ll Find Here</h2>
        <ul className="list-disc list-inside text-gray-800 mb-6 space-y-1">
          <li>In-depth articles on herbs, remedies, and holistic therapies</li>
          <li>Guides to safe and effective natural practices</li>
          <li>Evidence-based information and references</li>
          <li>Community contributions and personal experiences</li>
        </ul>
        <h2 className="text-2xl font-semibold mb-4 text-gray-900">Get Involved</h2>
        <p className="text-gray-800 mb-6">
          NatureHeals.info is open to everyone who wants to share knowledge, research, or personal stories about natural healing. You can contribute articles, upload media, or help review and improve content. Visit our <a href="/contribute" className="text-nature-green font-medium hover:underline">Contribute</a> page to get started!
        </p>
        <h2 className="text-2xl font-semibold mb-4 text-gray-900">Contact & Feedback</h2>
        <p className="text-gray-800">
          We welcome your feedback and suggestions. Please reach out via our contact form or email us at <a href="mailto:<EMAIL>" className="text-nature-green font-medium hover:underline"><EMAIL></a>.
        </p>
      </div>
    </div>
  );
}