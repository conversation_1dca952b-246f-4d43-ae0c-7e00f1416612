'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import Link from 'next/link';

export default function ModerationHelpPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  
  useEffect(() => {
    if (!loading && (!user || (user.role !== 'admin' && user.role !== 'moderator'))) {
      router.push('/auth/signin?redirect=/help/moderation');
    }
  }, [user, loading, router]);
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }
  
  if (!user || (user.role !== 'admin' && user.role !== 'moderator')) {
    return null; // Will redirect in useEffect
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Moderator Guide</h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden mb-8">
          <div className="p-6">
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Welcome to the moderator guide for NatureHeals.info. This guide will help you understand your role and responsibilities as a moderator, and how to use the moderation tools effectively.
            </p>
            
            <div className="prose prose-green max-w-none dark:prose-invert">
              <h2 id="role">Moderator Role</h2>
              <p>
                As a moderator, your primary responsibilities are:
              </p>
              <ul>
                <li>Reviewing and handling reported content</li>
                <li>Ensuring discussions remain civil and productive</li>
                <li>Enforcing community guidelines</li>
                <li>Helping new users navigate the platform</li>
                <li>Identifying and addressing spam or harmful content</li>
              </ul>
              
              <h2 id="tools">Moderation Tools</h2>
              <p>
                You have access to several tools to help you moderate effectively:
              </p>
              <ul>
                <li><strong>Moderation Dashboard:</strong> Available at <Link href="/admin/forums" className="text-nature-green hover:underline">/admin/forums</Link></li>
                <li><strong>Reported Content Queue:</strong> Review and handle user reports</li>
                <li><strong>Topic Management:</strong> Lock, pin, or delete topics</li>
                <li><strong>Post Management:</strong> Edit or delete inappropriate posts</li>
              </ul>
              
              <h2 id="reported-content">Handling Reported Content</h2>
              <p>
                When users report content, it appears in the Reported Content queue:
              </p>
              <ol>
                <li>Review the reported content and the reason for the report</li>
                <li>Determine if the content violates our community guidelines</li>
                <li>Take appropriate action:
                  <ul>
                    <li><strong>Dismiss:</strong> If the content doesn't violate guidelines</li>
                    <li><strong>Remove:</strong> Delete content that violates guidelines</li>
                    <li><strong>Lock:</strong> Prevent further replies to a problematic topic</li>
                    <li><strong>Contact user:</strong> For educational purposes or warnings</li>
                  </ul>
                </li>
                <li>Resolve the report with notes on the action taken</li>
              </ol>
              
              <h2 id="topic-management">Topic Management</h2>
              <p>
                You can manage forum topics in several ways:
              </p>
              <ul>
                <li><strong>Pinning:</strong> Keep important topics at the top of the category</li>
                <li><strong>Locking:</strong> Prevent new replies when a topic has run its course or become problematic</li>
                <li><strong>Moving:</strong> Relocate topics to more appropriate categories</li>
                <li><strong>Deleting:</strong> Remove topics that violate guidelines (use sparingly)</li>
              </ul>
              <p>
                To manage a topic, go to the topic page and use the moderator actions dropdown, or use the bulk actions in the moderation dashboard.
              </p>
              
              <h2 id="post-management">Post Management</h2>
              <p>
                For individual posts, you can:
              </p>
              <ul>
                <li><strong>Delete:</strong> Remove posts that violate guidelines</li>
                <li><strong>Mark as solution:</strong> Highlight helpful answers</li>
              </ul>
              <p>
                When deleting content, consider whether editing would be sufficient. Deletion should be reserved for clear violations.
              </p>
              
              <h2 id="guidelines">Moderation Guidelines</h2>
              <p>
                Follow these principles when moderating:
              </p>
              <ul>
                <li><strong>Be consistent:</strong> Apply rules uniformly across all users</li>
                <li><strong>Be transparent:</strong> Explain your actions when appropriate</li>
                <li><strong>Be educational:</strong> Help users understand guidelines rather than just enforcing them</li>
                <li><strong>Be proportional:</strong> Match the response to the severity of the violation</li>
                <li><strong>Be respectful:</strong> Treat all users with courtesy, even when enforcing rules</li>
              </ul>
              
              <h2 id="content-standards">Content Standards</h2>
              <p>
                Content should be removed if it:
              </p>
              <ul>
                <li>Contains hate speech, harassment, or personal attacks</li>
                <li>Promotes dangerous or illegal activities</li>
                <li>Makes false medical claims that could cause harm</li>
                <li>Contains spam or excessive self-promotion</li>
                <li>Violates copyright or plagiarizes others' work</li>
                <li>Contains personal information about others without consent</li>
                <li>Is deliberately misleading or contains misinformation</li>
              </ul>
              
              <h2 id="escalation">Escalation Process</h2>
              <p>
                Some situations require escalation to administrators:
              </p>
              <ul>
                <li>Repeated violations by the same user</li>
                <li>Complex disputes between users</li>
                <li>Legal concerns (copyright claims, etc.)</li>
                <li>Technical issues with the moderation tools</li>
                <li>Situations not covered by existing guidelines</li>
              </ul>
              <p>
                To escalate an issue, contact an administrator through the moderator chat or email <a href="mailto:<EMAIL>" className="text-nature-green hover:underline"><EMAIL></a>.
              </p>
              
              <h2 id="self-care">Moderator Self-Care</h2>
              <p>
                Moderation can sometimes be stressful. Remember to:
              </p>
              <ul>
                <li>Take breaks when needed</li>
                <li>Reach out to other moderators for support</li>
                <li>Don't take negative interactions personally</li>
                <li>Focus on the positive impact you're making</li>
              </ul>
              <p>
                If you're feeling overwhelmed, please reach out to the admin team.
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
          <h2 className="text-xl font-semibold mb-4 text-blue-800 dark:text-blue-300">Quick Reference</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-blue-700 dark:text-blue-400 mb-2">Common Actions</h3>
              <ul className="list-disc pl-5 space-y-1 text-gray-700 dark:text-gray-300">
                <li>Review reported content daily</li>
                <li>Lock topics that violate guidelines</li>
                <li>Delete spam posts immediately</li>
                <li>Pin important announcements</li>
                <li>Welcome new users in their first posts</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-blue-700 dark:text-blue-400 mb-2">Key Links</h3>
              <ul className="space-y-1">
                <li><Link href="/admin/forums" className="text-blue-600 dark:text-blue-400 hover:underline">Moderation Dashboard</Link></li>
                <li><Link href="/admin/users" className="text-blue-600 dark:text-blue-400 hover:underline">User Management</Link></li>
                <li><Link href="/terms" className="text-blue-600 dark:text-blue-400 hover:underline">Community Guidelines</Link></li>
                <li><Link href="/admin/logs" className="text-blue-600 dark:text-blue-400 hover:underline">Moderation Logs</Link></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
