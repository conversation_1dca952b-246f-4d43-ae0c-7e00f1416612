'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

type Category = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  icon?: string;
};

export default function TestCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<any>(null);
  const [testLoading, setTestLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // Function to load categories
  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      // Add a timestamp to bust the cache
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/categories?t=${timestamp}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Loaded categories:', data);
      setCategories(data || []);
    } catch (err: any) {
      console.error('Error loading categories:', err);
      setError(err.message || 'An error occurred while loading categories');
    } finally {
      setLoading(false);
    }
  };

  // Function to run the test API
  const runTest = async () => {
    try {
      setTestLoading(true);
      setTestResult(null);

      const timestamp = new Date().getTime();
      const response = await fetch(`/api/test-categories?t=${timestamp}`);
      const data = await response.json();

      console.log('Test result:', data);
      setTestResult(data);

      // Refresh categories after test
      await loadCategories();
    } catch (err: any) {
      console.error('Error running test:', err);
      setTestResult({ error: err.message || 'An error occurred while running the test' });
    } finally {
      setTestLoading(false);
    }
  };

  // Function to create a new category
  const createCategory = async () => {
    try {
      setLoading(true);
      setError(null);

      const name = `Test Category ${Date.now()}`;
      const slug = `test-category-${Date.now()}`;

      const response = await fetch('/api/admin/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description: 'This is a test category created from the test page',
          icon: 'leaf'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create category');
      }

      // Refresh categories
      await loadCategories();
    } catch (err: any) {
      console.error('Error creating category:', err);
      setError(err.message || 'An error occurred while creating the category');
    } finally {
      setLoading(false);
    }
  };

  // Load categories on mount and when refreshKey changes
  useEffect(() => {
    loadCategories();
  }, [refreshKey]);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Categories Test Page</h1>

      <div className="mb-6 flex flex-wrap gap-2">
        <button
          onClick={() => setRefreshKey(prev => prev + 1)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          disabled={loading}
        >
          {loading ? 'Loading...' : 'Refresh Categories'}
        </button>

        <button
          onClick={runTest}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          disabled={testLoading}
        >
          {testLoading ? 'Running Test...' : 'Run Database Test'}
        </button>

        <button
          onClick={createCategory}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          disabled={loading}
        >
          {loading ? 'Creating...' : 'Create Test Category'}
        </button>

        <Link
          href="/categories"
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          View Categories Page
        </Link>

        <button
          onClick={async () => {
            try {
              setTestLoading(true);
              const response = await fetch('/api/check-rls');
              const data = await response.json();
              console.log('RLS status:', data);
              setTestResult({ type: 'rls-check', ...data });
            } catch (err) {
              console.error('Error checking RLS:', err);
              setTestResult({ type: 'rls-check', error: err.message });
            } finally {
              setTestLoading(false);
            }
          }}
          className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          disabled={testLoading}
        >
          Check RLS Status
        </button>

        <button
          onClick={async () => {
            try {
              setTestLoading(true);
              const response = await fetch('/api/fix-categories-rls', { method: 'POST' });
              const data = await response.json();
              console.log('Fix RLS result:', data);
              setTestResult({ type: 'rls-fix', ...data });

              // Refresh categories after fixing RLS
              await loadCategories();
            } catch (err) {
              console.error('Error fixing RLS:', err);
              setTestResult({ type: 'rls-fix', error: err.message });
            } finally {
              setTestLoading(false);
            }
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          disabled={testLoading}
        >
          Fix Categories RLS
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md">
          <h2 className="font-bold mb-2">Error:</h2>
          <p>{error}</p>
        </div>
      )}

      {testResult && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <h2 className="font-bold mb-2">Test Results:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
            {JSON.stringify(testResult, null, 2)}
          </pre>
        </div>
      )}

      <div className="mb-6">
        <h2 className="text-xl font-bold mb-4">Categories ({categories.length})</h2>

        {loading ? (
          <p>Loading categories...</p>
        ) : categories.length === 0 ? (
          <p>No categories found.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map(category => (
              <div key={category.id} className="border border-gray-200 rounded-md p-4">
                <h3 className="font-bold">{category.name}</h3>
                <p className="text-sm text-gray-500 mb-2">Slug: {category.slug}</p>
                {category.description && (
                  <p className="text-sm mb-2">{category.description}</p>
                )}
                <p className="text-sm text-gray-500">Icon: {category.icon || 'None'}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
