'use client';

import React from 'react';

// Simple provider that just renders children without dark mode functionality
export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

// Dummy hook for compatibility with existing code
export function useTheme() {
  return {
    theme: 'light',
    setTheme: () => {},
    resolvedTheme: 'light'
  };
}
