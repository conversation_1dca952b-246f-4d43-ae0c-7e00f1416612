'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { createClient } from '@supabase/supabase-js';

type Category = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  icon?: string;
};

export default function ClientSideCategories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    let retryCount = 0;
    const maxRetries = 3;

    async function fetchCategories() {
      if (!isMounted) return;

      try {
        setLoading(true);
        setError(null);

        // Use our improved API endpoint that handles all fallbacks
        try {
          console.log('Fetching categories from API');

          // Simple fetch with no cache-busting parameters
          const response = await fetch('/api/categories');

          if (!response.ok) {
            throw new Error(`API call failed: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          console.log(`Successfully fetched ${data?.length || 0} categories via API`);

          if (isMounted) {
            if (Array.isArray(data) && data.length > 0) {
              setCategories(data);
              setLoading(false);
              return;
            } else {
              throw new Error('No categories returned from API');
            }
          }
        } catch (apiError) {
          console.error('Error with API call:', apiError);

          if (retryCount < maxRetries) {
            console.log(`Retrying API call (${retryCount + 1}/${maxRetries})...`);
            retryCount++;

            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (isMounted) {
              fetchCategories();
              return;
            }
          }

          console.log('Falling back to direct REST API call');

          // Initialize Supabase client
          const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
          const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

          if (!supabaseUrl || !supabaseKey) {
            throw new Error('Missing Supabase environment variables');
          }

          // Try direct REST API call as fallback
          const response = await fetch(`${supabaseUrl}/rest/v1/categories?select=id,name,slug,description,parent_id,icon&order=name`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseKey,
              'Authorization': `Bearer ${supabaseKey}`,
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          });

          if (!response.ok) {
            throw new Error(`REST API call failed: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          console.log(`Successfully fetched ${data?.length || 0} categories via REST API`);

          if (isMounted && Array.isArray(data) && data.length > 0) {
            setCategories(data);
          } else {
            throw new Error('No categories returned from REST API');
          }
        }
      } catch (err: any) {
        console.error('Error fetching categories:', err);
        if (isMounted) {
          setError(err.message || 'An error occurred while loading categories');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchCategories();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  // Function to get icon for a category
  const getCategoryIcon = (slug: string, iconName?: string) => {
    // Default icon mapping
    const iconMap: Record<string, string> = {
      'herbal-remedies': 'leaf',
      'mind-body-practices': 'sparkles',
      'nutritional-healing': 'cake',
      'medicinal-herbs': 'beaker',
      'aromatherapy': 'fire',
      'traditional-medicine': 'academic-cap',
      'ayurveda': 'sun',
      'chinese-medicine': 'moon',
      'homeopathy': 'droplet',
      'naturopathy': 'globe',
      'foraging': 'map',
      'gardening': 'home',
      'herbalism': 'collection',
      'nutrition': 'shopping-cart',
      'essential-oils': 'color-swatch',
      'holistic-health': 'heart',
      'sustainable-living': 'lightning-bolt'
    };

    // Use the provided icon name or fall back to the mapping
    return iconName || iconMap[slug] || 'bookmark';
  };

  // Render an icon based on its name
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'leaf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      case 'sparkles':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
        );
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green mb-4"></div>
        <p className="text-gray-600">Loading categories...</p>
        <p className="text-gray-500 text-sm mt-2">This may take a moment...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-md text-red-700 mb-4">
        <p className="font-bold">Error loading categories:</p>
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="bg-yellow-50 p-4 rounded-md text-yellow-700 mb-4">
        <p>No categories found. Please check back later or contact the administrator.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {categories.map((category) => (
        <Link
          key={category.id}
          href={`/categories/${category.slug}`}
          className="block bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border border-gray-200"
        >
          <div className="p-6">
            <div className="flex items-center mb-4">
              <div className="bg-[#2e7d32]/10 p-2 rounded-full w-10 h-10 flex items-center justify-center text-[#2e7d32] mr-3">
                {renderIcon(getCategoryIcon(category.slug, category.icon))}
              </div>
              <h2 className="text-xl font-semibold text-gray-900">{category.name}</h2>
            </div>

            {category.description && (
              <p className="text-gray-700 mb-3 line-clamp-2">{category.description}</p>
            )}

            <div className="text-[#2e7d32] font-medium flex items-center">
              View Articles
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}
