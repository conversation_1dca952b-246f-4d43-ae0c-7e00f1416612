'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { checkRequiredTables, TableStatus } from '@/lib/check-database';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';

export default function DatabaseSetupPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [tableStatuses, setTableStatuses] = useState<TableStatus[]>([]);
  const [isChecking, setIsChecking] = useState(true);
  const [isRunningMigration, setIsRunningMigration] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [authStatus, setAuthStatus] = useState<string>('checking');

  useEffect(() => {
    async function checkAuth() {
      if (loading) {
        setAuthStatus('checking');
        return;
      }

      if (!user) {
        console.log('No user found, redirecting to login');
        setAuthStatus('not_authenticated');
        // Uncomment this to redirect
        // router.push('/auth/signin?redirect=/admin/database');
        return;
      }

      console.log('User authenticated:', user);
      console.log('User role:', user.role);

      if (!user.role || (user.role !== 'admin' && user.role !== 'moderator')) {
        console.log('User does not have admin privileges');
        setAuthStatus('not_authorized');
        // Uncomment this to redirect
        // router.push('/');
        return;
      }

      setAuthStatus('authenticated');
      await checkTables();
    }

    checkAuth();
  }, [user, loading, router]);

  const checkTables = async () => {
    setIsChecking(true);
    setError(null);

    try {
      const statuses = await checkRequiredTables();
      setTableStatuses(statuses);
    } catch (err: any) {
      console.error('Error checking tables:', err);
      setError(err.message || 'Failed to check database tables');
    } finally {
      setIsChecking(false);
    }
  };

  const runMigration = async (migrationName: string) => {
    setIsRunningMigration(true);
    setError(null);
    setSuccess(null);

    try {
      // Get the migration SQL
      const response = await fetch(`/api/admin/migrations/${migrationName}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch migration: ${response.statusText}`);
      }

      const { sql } = await response.json();

      // Run the SQL
      const { error } = await supabase.rpc('execute_sql', { sql });

      if (error) {
        throw error;
      }

      setSuccess(`Migration ${migrationName} completed successfully`);

      // Refresh table statuses
      await checkTables();
    } catch (err: any) {
      console.error(`Error running migration ${migrationName}:`, err);
      setError(err.message || `Failed to run migration ${migrationName}`);
    } finally {
      setIsRunningMigration(false);
    }
  };

  if (loading || authStatus === 'checking') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Database Setup</h1>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green mx-auto mb-4"></div>
            <p className="text-gray-500 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (authStatus === 'not_authenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Database Setup</h1>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
            <div className="text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-yellow-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-4V7a3 3 0 00-3-3H6a3 3 0 00-3 3v4a3 3 0 003 3h4a3 3 0 003-3z" />
              </svg>
              <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                You need to be logged in to access this page.
              </p>
              <button
                onClick={() => router.push('/auth/signin?redirect=/admin/database')}
                className="px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700"
              >
                Sign In
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (authStatus === 'not_authorized') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Database Setup</h1>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
            <div className="text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-4V7a3 3 0 00-3-3H6a3 3 0 00-3 3v4a3 3 0 003 3h4a3 3 0 003-3z" />
              </svg>
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                You don't have permission to access this page. This page is only available to administrators.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-4 p-4 bg-gray-50 dark:bg-gray-750 rounded">
                <p className="font-medium mb-2">Debug Information:</p>
                <p>User ID: {user?.id}</p>
                <p>User Role: {user?.role || 'No role assigned'}</p>
              </div>
              <button
                onClick={() => router.push('/')}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Go to Homepage
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Database Setup</h1>

        {error && (
          <div className="mb-6 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-md">
            {success}
          </div>
        )}

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden mb-8">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Database Tables</h2>
              <button
                onClick={checkTables}
                disabled={isChecking}
                className="px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 disabled:opacity-50"
              >
                {isChecking ? 'Checking...' : 'Refresh'}
              </button>
            </div>

            {isChecking ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green"></div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-750">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Table Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {tableStatuses.map((table) => (
                      <tr key={table.name} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {table.name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {table.exists ? (
                            <span className="px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full">
                              Exists
                            </span>
                          ) : (
                            <span className="px-2 py-1 text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full">
                              Missing
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-6">Run Migrations</h2>

            <div className="space-y-4">
              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-red-50 dark:bg-red-900/20 border-red-300 dark:border-red-700">
                <h3 className="font-medium mb-2">Fix Admin Permissions</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  <strong>IMPORTANT:</strong> Adds a policy that allows admins to update other users' profiles. Run this to fix issues with role updates not being applied.
                </p>
                <button
                  onClick={() => runMigration('20250701_admin_profile_update_policy')}
                  disabled={isRunningMigration}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                >
                  {isRunningMigration ? 'Running...' : 'Run Fix Now'}
                </button>
              </div>

              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="font-medium mb-2">Forum System</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Creates tables for forum categories, topics, posts, and reactions.
                </p>
                <button
                  onClick={() => runMigration('20250601_forum_system')}
                  disabled={isRunningMigration}
                  className="px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {isRunningMigration ? 'Running...' : 'Run Migration'}
                </button>
              </div>

              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="font-medium mb-2">Activities & Notifications</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Creates tables for user activities, notifications, and notification preferences.
                </p>
                <button
                  onClick={() => runMigration('20250602_activities_notifications')}
                  disabled={isRunningMigration}
                  className="px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {isRunningMigration ? 'Running...' : 'Run Migration'}
                </button>
              </div>

              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="font-medium mb-2">Reporting System</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Creates tables for content reporting and moderation.
                </p>
                <button
                  onClick={() => runMigration('20250603_reporting_system')}
                  disabled={isRunningMigration}
                  className="px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {isRunningMigration ? 'Running...' : 'Run Migration'}
                </button>
              </div>

              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="font-medium mb-2">Search Function</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Creates a database function for searching across different content types.
                </p>
                <button
                  onClick={() => runMigration('20250604_search_function')}
                  disabled={isRunningMigration}
                  className="px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {isRunningMigration ? 'Running...' : 'Run Migration'}
                </button>
              </div>

              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="font-medium mb-2">Rate Limiting</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Creates tables and functions for rate limiting user actions.
                </p>
                <button
                  onClick={() => runMigration('20250605_rate_limiting')}
                  disabled={isRunningMigration}
                  className="px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {isRunningMigration ? 'Running...' : 'Run Migration'}
                </button>
              </div>

              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                <h3 className="font-medium mb-2">Add Updated By to Forum Categories</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Adds the updated_by column to the forum_categories table to track who last updated a category.
                </p>
                <button
                  onClick={() => runMigration('20250604_add_updated_by_to_forum_categories')}
                  disabled={isRunningMigration}
                  className="px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700 disabled:opacity-50"
                >
                  {isRunningMigration ? 'Running...' : 'Run Migration'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
