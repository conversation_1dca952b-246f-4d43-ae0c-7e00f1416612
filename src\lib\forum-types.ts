export interface ForumCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  display_order: number;
  parent_id?: string;
  created_at: string;
  updated_at: string;
  topic_count?: number; // Calculated field
  post_count?: number; // Calculated field
}

export interface ForumTopic {
  id: string;
  title: string;
  slug: string;
  category_id: string;
  author_id: string;
  is_pinned: boolean;
  is_locked: boolean;
  view_count: number;
  last_post_at: string;
  created_at: string;
  updated_at: string;
  post_count?: number; // Calculated field
  author?: {
    username: string;
    full_name?: string;
    avatar_url?: string;
    is_verified_expert?: boolean;
    level?: number;
  };
  category?: ForumCategory;
  last_post?: ForumPost;
}

export interface ForumPost {
  id: string;
  topic_id: string;
  author_id: string;
  content: string;
  is_solution: boolean;
  parent_id?: string;
  created_at: string;
  updated_at: string;
  author?: {
    username: string;
    full_name?: string;
    avatar_url?: string;
    is_verified_expert?: boolean;
    level?: number;
  };
  reactions?: ForumPostReaction[];
  replies?: ForumPost[];
}

export interface ForumPostReaction {
  id: string;
  post_id: string;
  user_id: string;
  reaction_type: 'like' | 'helpful' | 'insightful';
  created_at: string;
  user?: {
    username: string;
  };
}

export const REACTION_TYPES = {
  like: {
    name: 'Like',
    icon: '👍',
    points: 1
  },
  helpful: {
    name: 'Helpful',
    icon: '🙏',
    points: 2
  },
  insightful: {
    name: 'Insightful',
    icon: '💡',
    points: 3
  }
};

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_type: string;
  achievement_data: any;
  created_at: string;
}

export const ACHIEVEMENT_TYPES = {
  level_up: {
    name: 'Level Up',
    description: 'Reached a new level in the community',
    icon: '⬆️'
  },
  post_milestone: {
    name: 'Post Milestone',
    description: 'Reached a milestone number of posts',
    icon: '📝'
  },
  topic_milestone: {
    name: 'Topic Milestone',
    description: 'Created a milestone number of topics',
    icon: '🧵'
  },
  reaction_milestone: {
    name: 'Reaction Milestone',
    description: 'Received a milestone number of reactions',
    icon: '👍'
  },
  expert_verification: {
    name: 'Expert Verification',
    description: 'Verified as an expert in the community',
    icon: '✅'
  },
  solution_milestone: {
    name: 'Solution Milestone',
    description: 'Provided a milestone number of solutions',
    icon: '💡'
  }
};
