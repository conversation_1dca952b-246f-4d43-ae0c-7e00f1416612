-- <PERSON><PERSON>t to verify that the moderation tables were created successfully

-- Check if tables exist
SELECT 
    table_name,
    EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_bans'
    ) AS user_bans_exists,
    EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'reported_content'
    ) AS reported_content_exists,
    EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'moderation_actions'
    ) AS moderation_actions_exists,
    EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'auto_moderation_logs'
    ) AS auto_moderation_logs_exists,
    EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_notifications'
    ) AS user_notifications_exists
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('user_bans', 'reported_content', 'moderation_actions', 'auto_moderation_logs', 'user_notifications')
ORDER BY table_name;

-- Check if profiles table has ban-related columns
SELECT 
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
AND column_name IN ('is_banned', 'ban_expires_at');

-- Check if RLS is enabled on the tables
SELECT 
    tablename,
    rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('user_bans', 'reported_content', 'moderation_actions', 'auto_moderation_logs', 'user_notifications');

-- Check if policies exist for the tables
SELECT 
    tablename,
    policyname
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('user_bans', 'reported_content', 'moderation_actions', 'auto_moderation_logs', 'user_notifications')
ORDER BY tablename, policyname;
