'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import { format, subDays, startOfDay, endOfDay, parseISO } from 'date-fns';
import Link from 'next/link';

// Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

interface AnalyticsData {
  pageViews: {
    date: string;
    count: number;
  }[];
  topArticles: {
    title: string;
    slug: string;
    views: number;
  }[];
  topSearches: {
    query: string;
    count: number;
  }[];
  userActivity: {
    date: string;
    signups: number;
    logins: number;
  }[];
  deviceStats: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  socialShares: {
    platform: string;
    count: number;
  }[];
  totalUsers: number;
  totalArticles: number;
  totalPageViews: number;
}

export default function AnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d'>('30d');
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Redirect if not logged in or not an admin
    if (!authLoading && (!user || user.role !== 'admin')) {
      router.push('/');
      return;
    }

    fetchAnalyticsData();
  }, [user, authLoading, router, dateRange]);

  async function fetchAnalyticsData() {
    if (!user || user.role !== 'admin') return;

    setLoading(true);
    setError(null);

    try {
      // Calculate date range
      const today = new Date();
      const startDate = format(
        startOfDay(
          dateRange === '7d'
            ? subDays(today, 7)
            : dateRange === '30d'
            ? subDays(today, 30)
            : subDays(today, 90)
        ),
        'yyyy-MM-dd'
      );
      const endDate = format(endOfDay(today), 'yyyy-MM-dd');

      // Fetch page views
      const { data: pageViewsData, error: pageViewsError } = await supabase
        .from('analytics_events')
        .select('created_at, event_type')
        .eq('event_type', 'page_view')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (pageViewsError) throw pageViewsError;

      // Process page views by date
      const pageViewsByDate = pageViewsData.reduce((acc: Record<string, number>, item) => {
        const date = format(parseISO(item.created_at), 'yyyy-MM-dd');
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});

      // Generate date range for chart
      const dateLabels = [];
      const dateValues = [];
      let currentDate = startOfDay(
        dateRange === '7d'
          ? subDays(today, 7)
          : dateRange === '30d'
          ? subDays(today, 30)
          : subDays(today, 90)
      );

      while (currentDate <= today) {
        const dateStr = format(currentDate, 'yyyy-MM-dd');
        dateLabels.push(format(currentDate, 'MMM d'));
        dateValues.push(pageViewsByDate[dateStr] || 0);
        currentDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
      }

      // Fetch top articles
      const { data: articlesData, error: articlesError } = await supabase
        .from('articles')
        .select('title, slug, view_count')
        .order('view_count', { ascending: false })
        .limit(10);

      if (articlesError) throw articlesError;

      // Fetch top searches
      const { data: searchesData, error: searchesError } = await supabase
        .from('analytics_events')
        .select('metadata')
        .eq('event_type', 'search')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (searchesError) throw searchesError;

      // Process search queries
      const searchQueries: Record<string, number> = {};
      searchesData.forEach(item => {
        if (item.metadata && item.metadata.query) {
          const query = item.metadata.query.toLowerCase();
          searchQueries[query] = (searchQueries[query] || 0) + 1;
        }
      });

      const topSearches = Object.entries(searchQueries)
        .map(([query, count]) => ({ query, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Fetch user activity
      const { data: signupsData, error: signupsError } = await supabase
        .from('analytics_events')
        .select('created_at')
        .eq('event_type', 'signup')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (signupsError) throw signupsError;

      const { data: loginsData, error: loginsError } = await supabase
        .from('analytics_events')
        .select('created_at')
        .eq('event_type', 'login')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (loginsError) throw loginsError;

      // Process user activity by date
      const signupsByDate: Record<string, number> = {};
      const loginsByDate: Record<string, number> = {};

      signupsData.forEach(item => {
        const date = format(parseISO(item.created_at), 'yyyy-MM-dd');
        signupsByDate[date] = (signupsByDate[date] || 0) + 1;
      });

      loginsData.forEach(item => {
        const date = format(parseISO(item.created_at), 'yyyy-MM-dd');
        loginsByDate[date] = (loginsByDate[date] || 0) + 1;
      });

      const userActivity = dateLabels.map((label, index) => {
        const date = format(
          new Date(
            new Date().setDate(
              new Date().getDate() - (dateLabels.length - 1 - index)
            )
          ),
          'yyyy-MM-dd'
        );
        return {
          date,
          signups: signupsByDate[date] || 0,
          logins: loginsByDate[date] || 0
        };
      });

      // Fetch device stats
      const { data: deviceData, error: deviceError } = await supabase
        .from('analytics_events')
        .select('metadata')
        .eq('event_type', 'page_view')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (deviceError) throw deviceError;

      // Process device stats
      let desktop = 0;
      let mobile = 0;
      let tablet = 0;

      deviceData.forEach(item => {
        if (item.metadata && item.metadata.device_type) {
          if (item.metadata.device_type === 'desktop') desktop++;
          else if (item.metadata.device_type === 'mobile') mobile++;
          else if (item.metadata.device_type === 'tablet') tablet++;
        }
      });

      // Fetch social shares
      const { data: sharesData, error: sharesError } = await supabase
        .from('articles')
        .select('facebook_shares, twitter_shares, pinterest_shares, email_shares');

      if (sharesError) throw sharesError;

      // Process social shares
      let facebookShares = 0;
      let twitterShares = 0;
      let pinterestShares = 0;
      let emailShares = 0;

      sharesData.forEach(item => {
        facebookShares += item.facebook_shares || 0;
        twitterShares += item.twitter_shares || 0;
        pinterestShares += item.pinterest_shares || 0;
        emailShares += item.email_shares || 0;
      });

      const socialShares = [
        { platform: 'Facebook', count: facebookShares },
        { platform: 'Twitter', count: twitterShares },
        { platform: 'Pinterest', count: pinterestShares },
        { platform: 'Email', count: emailShares }
      ].sort((a, b) => b.count - a.count);

      // Fetch total counts
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('id', { count: 'exact', head: true });

      const { count: totalArticles } = await supabase
        .from('articles')
        .select('id', { count: 'exact', head: true });

      const { count: totalPageViews } = await supabase
        .from('analytics_events')
        .select('id', { count: 'exact', head: true })
        .eq('event_type', 'page_view');

      // Set analytics data
      setAnalyticsData({
        pageViews: dateLabels.map((label, index) => ({
          date: label,
          count: dateValues[index]
        })),
        topArticles: articlesData.map(article => ({
          title: article.title,
          slug: article.slug,
          views: article.view_count
        })),
        topSearches,
        userActivity,
        deviceStats: {
          desktop,
          mobile,
          tablet
        },
        socialShares,
        totalUsers: totalUsers || 0,
        totalArticles: totalArticles || 0,
        totalPageViews: totalPageViews || 0
      });
    } catch (err: any) {
      console.error('Error fetching analytics data:', err);
      setError(err.message || 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  }

  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">
          Access Denied
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          You do not have permission to view this page.
        </p>
        <Link
          href="/"
          className="inline-flex items-center px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
        >
          Back to Home
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setDateRange('7d')}
            className={`px-3 py-1 rounded-md ${
              dateRange === '7d'
                ? 'bg-nature-green text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            7 Days
          </button>
          <button
            onClick={() => setDateRange('30d')}
            className={`px-3 py-1 rounded-md ${
              dateRange === '30d'
                ? 'bg-nature-green text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            30 Days
          </button>
          <button
            onClick={() => setDateRange('90d')}
            className={`px-3 py-1 rounded-md ${
              dateRange === '90d'
                ? 'bg-nature-green text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            90 Days
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[40vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-md">
          {error}
        </div>
      ) : analyticsData ? (
        <div className="space-y-8">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Total Users</p>
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{analyticsData.totalUsers}</h3>
                </div>
                <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Total Articles</p>
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{analyticsData.totalArticles}</h3>
                </div>
                <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Total Page Views</p>
                  <h3 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{analyticsData.totalPageViews}</h3>
                </div>
                <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Page Views Chart */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Page Views</h2>
            <div className="h-80">
              <Line
                data={{
                  labels: analyticsData.pageViews.map(item => item.date),
                  datasets: [
                    {
                      label: 'Page Views',
                      data: analyticsData.pageViews.map(item => item.count),
                      borderColor: '#2e7d32',
                      backgroundColor: 'rgba(46, 125, 50, 0.1)',
                      fill: true,
                      tension: 0.4
                    }
                  ]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                      grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                      }
                    },
                    x: {
                      grid: {
                        display: false
                      }
                    }
                  },
                  plugins: {
                    legend: {
                      display: false
                    }
                  }
                }}
              />
            </div>
          </div>

          {/* Top Articles and Top Searches */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Top Articles</h2>
              <div className="overflow-hidden">
                <div className="h-80 overflow-y-auto pr-2">
                  <table className="min-w-full">
                    <thead>
                      <tr>
                        <th className="text-left text-sm font-medium text-gray-500 dark:text-gray-400 pb-2">Title</th>
                        <th className="text-right text-sm font-medium text-gray-500 dark:text-gray-400 pb-2">Views</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analyticsData.topArticles.map((article, index) => (
                        <tr key={index} className="border-t border-gray-200 dark:border-gray-700">
                          <td className="py-3 text-gray-800 dark:text-gray-200 truncate max-w-xs">
                            <Link href={`/wiki/${article.slug}`} className="hover:text-nature-green">
                              {article.title}
                            </Link>
                          </td>
                          <td className="py-3 text-right text-gray-800 dark:text-gray-200">{article.views}</td>
                        </tr>
                      ))}
                      {analyticsData.topArticles.length === 0 && (
                        <tr>
                          <td colSpan={2} className="py-4 text-center text-gray-500 dark:text-gray-400">
                            No data available
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Top Searches</h2>
              <div className="h-80">
                {analyticsData.topSearches.length > 0 ? (
                  <Bar
                    data={{
                      labels: analyticsData.topSearches.map(item => item.query),
                      datasets: [
                        {
                          label: 'Search Count',
                          data: analyticsData.topSearches.map(item => item.count),
                          backgroundColor: '#4caf50',
                          borderRadius: 4
                        }
                      ]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      indexAxis: 'y',
                      scales: {
                        x: {
                          beginAtZero: true,
                          grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                          }
                        },
                        y: {
                          grid: {
                            display: false
                          }
                        }
                      },
                      plugins: {
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-500 dark:text-gray-400">No search data available</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* User Activity Chart */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">User Activity</h2>
            <div className="h-80">
              <Line
                data={{
                  labels: analyticsData.userActivity.map(item => item.date),
                  datasets: [
                    {
                      label: 'Signups',
                      data: analyticsData.userActivity.map(item => item.signups),
                      borderColor: '#2e7d32',
                      backgroundColor: 'rgba(46, 125, 50, 0.1)',
                      fill: true,
                      tension: 0.4
                    },
                    {
                      label: 'Logins',
                      data: analyticsData.userActivity.map(item => item.logins),
                      borderColor: '#1976d2',
                      backgroundColor: 'rgba(25, 118, 210, 0.1)',
                      fill: true,
                      tension: 0.4
                    }
                  ]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                      grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                      }
                    },
                    x: {
                      grid: {
                        display: false
                      }
                    }
                  }
                }}
              />
            </div>
          </div>

          {/* Device Stats and Social Shares */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Device Stats</h2>
              <div className="h-80 flex items-center justify-center">
                {analyticsData.deviceStats.desktop > 0 || analyticsData.deviceStats.mobile > 0 || analyticsData.deviceStats.tablet > 0 ? (
                  <Pie
                    data={{
                      labels: ['Desktop', 'Mobile', 'Tablet'],
                      datasets: [
                        {
                          data: [
                            analyticsData.deviceStats.desktop,
                            analyticsData.deviceStats.mobile,
                            analyticsData.deviceStats.tablet
                          ],
                          backgroundColor: [
                            '#4caf50',
                            '#2196f3',
                            '#ff9800'
                          ],
                          borderWidth: 1
                        }
                      ]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom'
                        }
                      }
                    }}
                  />
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">No device data available</p>
                )}
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Social Shares</h2>
              <div className="h-80">
                {analyticsData.socialShares.some(item => item.count > 0) ? (
                  <Bar
                    data={{
                      labels: analyticsData.socialShares.map(item => item.platform),
                      datasets: [
                        {
                          label: 'Shares',
                          data: analyticsData.socialShares.map(item => item.count),
                          backgroundColor: [
                            '#3b5998', // Facebook
                            '#1da1f2', // Twitter
                            '#e60023', // Pinterest
                            '#6b7280'  // Email
                          ],
                          borderRadius: 4
                        }
                      ]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                          grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                          }
                        },
                        x: {
                          grid: {
                            display: false
                          }
                        }
                      },
                      plugins: {
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-500 dark:text-gray-400">No social share data available</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
