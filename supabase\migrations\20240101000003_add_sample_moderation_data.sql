-- This script adds sample data for testing the moderation system
-- It uses exception handling to catch any errors

-- Function to safely insert sample moderation data
CREATE OR REPLACE FUNCTION insert_sample_moderation_data() RETURNS void AS $$
DECLARE
    admin_id UUID;
    moderator_id UUID;
    user1_id UUID;
    user2_id UUID;
    topic1_id UUID;
    post1_id UUID;
BEGIN
    -- Get user IDs for sample data
    BEGIN
        SELECT id INTO admin_id FROM public.profiles WHERE role = 'admin' LIMIT 1;
        IF admin_id IS NULL THEN
            RAISE NOTICE 'No admin user found. Using a random UUID.';
            admin_id := uuid_generate_v4();
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting admin user: %', SQLERRM;
        admin_id := uuid_generate_v4();
    END;

    BEGIN
        SELECT id INTO moderator_id FROM public.profiles WHERE role = 'moderator' LIMIT 1;
        IF moderator_id IS NULL THEN
            RAISE NOTICE 'No moderator user found. Using admin ID.';
            moderator_id := admin_id;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting moderator user: %', SQLERRM;
        moderator_id := admin_id;
    END;

    BEGIN
        SELECT id INTO user1_id FROM public.profiles WHERE role = 'user' LIMIT 1;
        IF user1_id IS NULL THEN
            RAISE NOTICE 'No regular user found. Using admin ID.';
            user1_id := admin_id;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting regular user: %', SQLERRM;
        user1_id := admin_id;
    END;

    BEGIN
        SELECT id INTO user2_id FROM public.profiles WHERE role = 'user' OFFSET 1 LIMIT 1;
        IF user2_id IS NULL THEN
            RAISE NOTICE 'No second regular user found. Using moderator ID.';
            user2_id := moderator_id;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting second regular user: %', SQLERRM;
        user2_id := moderator_id;
    END;
    -- Generate placeholder IDs for content
    topic1_id := uuid_generate_v4();
    post1_id := uuid_generate_v4();

    -- Try to get real IDs if available
    BEGIN
        SELECT id INTO topic1_id FROM public.forum_topics LIMIT 1;
        RAISE NOTICE 'Using existing topic ID: %', topic1_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting topic ID: %', SQLERRM;
        RAISE NOTICE 'Using generated topic ID: %', topic1_id;
    END;

    BEGIN
        SELECT id INTO post1_id FROM public.forum_posts LIMIT 1;
        RAISE NOTICE 'Using existing post ID: %', post1_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error getting post ID: %', SQLERRM;
        RAISE NOTICE 'Using generated post ID: %', post1_id;
    END;

    -- Insert sample user ban
    BEGIN
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_bans') THEN
            INSERT INTO public.user_bans (
                user_id,
                moderator_id,
                ban_type,
                reason,
                expires_at
            ) VALUES (
                user1_id,
                moderator_id,
                'post_only',
                'Repeated violations of community guidelines',
                NOW() + INTERVAL '7 days'
            );
            RAISE NOTICE 'Inserted sample user ban';
        ELSE
            RAISE NOTICE 'Table user_bans does not exist. Skipping.';
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting sample user ban: %', SQLERRM;
    END;

    -- Insert sample reported content
    BEGIN
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'reported_content') THEN
            INSERT INTO public.reported_content (
                content_type,
                content_id,
                reporter_id,
                reason,
                status,
                created_at
            ) VALUES (
                'post',
                post1_id,
                user1_id,
                'This post contains inappropriate language',
                'pending',
                NOW() - INTERVAL '2 days'
            );
            RAISE NOTICE 'Inserted sample reported content';
        ELSE
            RAISE NOTICE 'Table reported_content does not exist. Skipping.';
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting sample reported content: %', SQLERRM;
    END;

    -- Insert sample moderation action
    BEGIN
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'moderation_actions') THEN
            -- Check column existence
            IF EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = 'moderation_actions'
                AND column_name = 'action_type'
            ) THEN
                -- With action_type
                INSERT INTO public.moderation_actions (
                    action_type,
                    moderator_id,
                    content_id,
                    content_type,
                    reason,
                    created_at
                ) VALUES (
                    'lock_topic',
                    moderator_id,
                    topic1_id::text,
                    'forum_topic',
                    'Topic has gone off-topic',
                    NOW() - INTERVAL '3 days'
                );
            ELSE
                -- Without action_type
                INSERT INTO public.moderation_actions (
                    moderator_id,
                    content_id,
                    content_type,
                    reason,
                    created_at
                ) VALUES (
                    moderator_id,
                    topic1_id::text,
                    'forum_topic',
                    'Topic has gone off-topic',
                    NOW() - INTERVAL '3 days'
                );
            END IF;
            RAISE NOTICE 'Inserted sample moderation action';
        ELSE
            RAISE NOTICE 'Table moderation_actions does not exist. Skipping.';
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting sample moderation action: %', SQLERRM;
    END;

    -- Insert sample auto moderation log
    BEGIN
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'auto_moderation_logs') THEN
            INSERT INTO public.auto_moderation_logs (
                user_id,
                content_type,
                original_content,
                filtered_content,
                matched_words,
                created_at
            ) VALUES (
                user1_id,
                'post',
                'This post contains badword1 and offensive1 language',
                'This post contains ******** and ********** language',
                ARRAY['badword1', 'offensive1'],
                NOW() - INTERVAL '6 hours'
            );
            RAISE NOTICE 'Inserted sample auto moderation log';
        ELSE
            RAISE NOTICE 'Table auto_moderation_logs does not exist. Skipping.';
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting sample auto moderation log: %', SQLERRM;
    END;

    -- Insert sample user notification
    BEGIN
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_notifications') THEN
            INSERT INTO public.user_notifications (
                user_id,
                type,
                title,
                message,
                link_url,
                created_at
            ) VALUES (
                user1_id,
                'content_moderated',
                'Your post has been edited',
                'A moderator has edited your post. Reason: Removed inappropriate language',
                '/forums/post/' || post1_id,
                NOW() - INTERVAL '1 day'
            );
            RAISE NOTICE 'Inserted sample user notification';
        ELSE
            RAISE NOTICE 'Table user_notifications does not exist. Skipping.';
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting sample user notification: %', SQLERRM;
    END;

    RAISE NOTICE 'Sample data insertion complete';
END;
$$ LANGUAGE plpgsql;

-- Execute the function to insert sample data
SELECT insert_sample_moderation_data();

-- Drop the function after use
DROP FUNCTION IF EXISTS insert_sample_moderation_data();
