'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase';
import Link from 'next/link';
import UserLevelDisplay from '@/components/UserLevelDisplay';
import VerificationBadge from '@/components/VerificationBadge';

interface LeaderboardUser {
  id: string;
  username: string;
  full_name?: string;
  avatar_url?: string;
  reputation_points: number;
  level: number;
  is_verified_expert: boolean;
  contribution_stats?: {
    articles: number;
    comments: number;
    likes_received: number;
    forum_topics?: number;
    forum_posts?: number;
  };
}

interface UserLeaderboardProps {
  type?: 'reputation' | 'articles' | 'forum_posts' | 'solutions';
  limit?: number;
  className?: string;
  title?: string;
}

export default function UserLeaderboard({
  type = 'reputation',
  limit = 10,
  className = '',
  title = 'Top Contributors'
}: UserLeaderboardProps) {
  const [users, setUsers] = useState<LeaderboardUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    loadLeaderboard();
  }, [type]);
  
  const loadLeaderboard = async () => {
    setIsLoading(true);
    setError(null);
    
    const supabase = createClient();
    
    try {
      let query = supabase
        .from('profiles')
        .select(`
          id,
          username,
          full_name,
          avatar_url,
          reputation_points,
          level,
          is_verified_expert,
          contribution_stats
        `)
        .order('reputation_points', { ascending: false })
        .limit(limit);
      
      // Apply different sorting based on type
      switch (type) {
        case 'reputation':
          query = query.order('reputation_points', { ascending: false });
          break;
        
        case 'articles':
          query = query.order('contribution_stats->articles', { ascending: false });
          break;
        
        case 'forum_posts':
          query = query.order('contribution_stats->forum_posts', { ascending: false });
          break;
        
        case 'solutions':
          // For solutions, we need to count forum posts marked as solutions
          const { data: solutionUsers, error: solutionError } = await supabase
            .from('forum_posts')
            .select(`
              author_id,
              count
            `)
            .eq('is_solution', true)
            .group('author_id')
            .order('count', { ascending: false })
            .limit(limit);
          
          if (solutionError) {
            throw solutionError;
          }
          
          if (solutionUsers && solutionUsers.length > 0) {
            const userIds = solutionUsers.map(u => u.author_id);
            
            const { data: userData, error: userError } = await supabase
              .from('profiles')
              .select(`
                id,
                username,
                full_name,
                avatar_url,
                reputation_points,
                level,
                is_verified_expert,
                contribution_stats
              `)
              .in('id', userIds);
            
            if (userError) {
              throw userError;
            }
            
            // Sort users by solution count
            const sortedUsers = userData?.map(user => {
              const solutionCount = solutionUsers.find(s => s.author_id === user.id)?.count || 0;
              return { ...user, solution_count: solutionCount };
            }).sort((a, b) => (b.solution_count || 0) - (a.solution_count || 0));
            
            setUsers(sortedUsers || []);
            setIsLoading(false);
            return;
          }
          
          break;
      }
      
      const { data, error: fetchError } = await query;
      
      if (fetchError) {
        throw fetchError;
      }
      
      setUsers(data || []);
    } catch (err: any) {
      console.error('Error loading leaderboard:', err);
      setError(err.message || 'Failed to load leaderboard');
    } finally {
      setIsLoading(false);
    }
  };
  
  const getMetricValue = (user: LeaderboardUser) => {
    switch (type) {
      case 'reputation':
        return user.reputation_points || 0;
      
      case 'articles':
        return user.contribution_stats?.articles || 0;
      
      case 'forum_posts':
        return user.contribution_stats?.forum_posts || 0;
      
      case 'solutions':
        return (user as any).solution_count || 0;
      
      default:
        return 0;
    }
  };
  
  const getMetricLabel = () => {
    switch (type) {
      case 'reputation':
        return 'Points';
      
      case 'articles':
        return 'Articles';
      
      case 'forum_posts':
        return 'Posts';
      
      case 'solutions':
        return 'Solutions';
      
      default:
        return '';
    }
  };
  
  return (
    <div className={`${className}`}>
      {title && (
        <h2 className="text-lg font-semibold mb-4">{title}</h2>
      )}
      
      {error && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-md mb-4">
          {error}
        </div>
      )}
      
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : users.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <p className="text-gray-500 dark:text-gray-400">No data available</p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="grid grid-cols-12 gap-2 p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 text-sm font-medium text-gray-500 dark:text-gray-400">
            <div className="col-span-1 text-center">#</div>
            <div className="col-span-7">User</div>
            <div className="col-span-4 text-right">{getMetricLabel()}</div>
          </div>
          
          <ul>
            {users.map((user, index) => (
              <li 
                key={user.id} 
                className={`border-b border-gray-200 dark:border-gray-700 last:border-b-0 ${
                  index < 3 ? 'bg-yellow-50 dark:bg-yellow-900/10' : ''
                }`}
              >
                <Link 
                  href={`/profile/${user.username}`}
                  className="grid grid-cols-12 gap-2 p-3 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                >
                  <div className="col-span-1 flex items-center justify-center">
                    {index === 0 ? (
                      <span className="text-xl text-yellow-500">🥇</span>
                    ) : index === 1 ? (
                      <span className="text-xl text-gray-400">🥈</span>
                    ) : index === 2 ? (
                      <span className="text-xl text-amber-700">🥉</span>
                    ) : (
                      <span className="font-medium text-gray-500 dark:text-gray-400">{index + 1}</span>
                    )}
                  </div>
                  
                  <div className="col-span-7 flex items-center">
                    <div className="flex-shrink-0 mr-3">
                      {user.avatar_url ? (
                        <img
                          src={user.avatar_url}
                          alt={user.username}
                          className="w-8 h-8 rounded-full"
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                          <span className="text-gray-500 dark:text-gray-400 font-medium">
                            {user.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 flex items-center">
                        {user.full_name || user.username}
                        {user.is_verified_expert && (
                          <VerificationBadge isVerified={true} size="sm" className="ml-1" />
                        )}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                        @{user.username}
                        <UserLevelDisplay level={user.level} size="sm" className="ml-2" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="col-span-4 flex items-center justify-end">
                    <span className="font-semibold text-gray-900 dark:text-gray-100">
                      {getMetricValue(user).toLocaleString()}
                    </span>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
