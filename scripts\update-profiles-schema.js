// <PERSON>ript to update the profiles table schema
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Get Supabase credentials from environment variables or command line arguments
let supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
let supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Check for command line arguments
const args = process.argv.slice(2);
if (args.length >= 2) {
  supabaseUrl = args[0];
  supabaseServiceKey = args[1];
  console.log('Using Supabase credentials from command line arguments');
}

// Prompt for credentials if not provided
if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service key.');
  console.log('\nPlease run the script with your Supabase credentials:');
  console.log('node scripts/update-profiles-schema.js <SUPABASE_URL> <SERVICE_KEY>');
  console.log('\nOr set the environment variables NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateProfilesSchema() {
  try {
    console.log('Updating profiles table schema...');

    // SQL to update the profiles table
    const sql = `
      -- Add missing columns to the profiles table
      ALTER TABLE public.profiles
      ADD COLUMN IF NOT EXISTS avatar_type TEXT,
      ADD COLUMN IF NOT EXISTS preset_avatar TEXT,
      ADD COLUMN IF NOT EXISTS visibility TEXT DEFAULT 'public',
      ADD COLUMN IF NOT EXISTS show_email BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS show_location BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS location TEXT,
      ADD COLUMN IF NOT EXISTS interests TEXT[],
      ADD COLUMN IF NOT EXISTS expertise_level TEXT,
      ADD COLUMN IF NOT EXISTS healing_philosophy TEXT,
      ADD COLUMN IF NOT EXISTS favorite_herbs TEXT[],
      ADD COLUMN IF NOT EXISTS social_links JSONB,
      ADD COLUMN IF NOT EXISTS spirit_plant TEXT,
      ADD COLUMN IF NOT EXISTS badges TEXT[];
    `;

    // Execute the SQL directly
    const { error } = await supabase.rpc('execute_sql', { sql });

    if (error) {
      // If the execute_sql function doesn't exist, create it and try again
      if (error.message.includes('function execute_sql') || error.code === '42883') {
        console.log('Creating execute_sql function...');

        // Create the function directly using a raw query
        const { error: createFnError } = await supabase
          .from('_rpc')
          .select('*')
          .limit(1)
          .then(() => {
            return supabase.rpc('execute_sql', {
              sql: `
                CREATE OR REPLACE FUNCTION execute_sql(sql text)
                RETURNS void AS $$
                BEGIN
                  EXECUTE sql;
                END;
                $$ LANGUAGE plpgsql SECURITY DEFINER;
              `
            });
          });

        if (createFnError) {
          console.error('Error creating execute_sql function:', createFnError);
          return false;
        }

        // Try executing the original SQL again
        const { error: retryError } = await supabase.rpc('execute_sql', { sql });

        if (retryError) {
          console.error('Error updating profiles schema after creating function:', retryError);
          return false;
        }
      } else {
        console.error('Error updating profiles schema:', error);
        return false;
      }
    }

    console.log('Successfully updated profiles table schema.');
    return true;
  } catch (err) {
    console.error('Error updating profiles schema:', err);
    return false;
  }
}

// Main function
async function main() {
  console.log('Starting profiles schema update...');

  const success = await updateProfilesSchema();

  if (success) {
    console.log('Profiles schema update completed successfully.');
  } else {
    console.error('Profiles schema update failed.');
    process.exit(1);
  }
}

main().catch(err => {
  console.error('Unhandled error during schema update:', err);
  process.exit(1);
});
