export type NotificationType = 
  | 'mention'
  | 'reply'
  | 'reaction'
  | 'solution'
  | 'verification'
  | 'level_up'
  | 'badge_earned'
  | 'article_approved'
  | 'article_rejected'
  | 'article_comment';

export type ContentType = 
  | 'forum_topic'
  | 'forum_post'
  | 'article'
  | 'comment'
  | 'profile'
  | 'expert_verification';

export interface Notification {
  id: string;
  user_id: string;
  sender_id?: string;
  notification_type: NotificationType;
  content_type: ContentType;
  content_id: string;
  message: string;
  link?: string;
  metadata?: any;
  is_read: boolean;
  created_at: string;
  sender?: {
    username: string;
    full_name?: string;
    avatar_url?: string;
  };
}

export interface NotificationPreferences {
  user_id: string;
  email_notifications: boolean;
  push_notifications: boolean;
  mention_notifications: boolean;
  reply_notifications: boolean;
  reaction_notifications: boolean;
  forum_notifications: boolean;
  article_notifications: boolean;
  created_at: string;
  updated_at: string;
}

export const NOTIFICATION_ICONS = {
  mention: '🔖',
  reply: '💬',
  reaction: '👍',
  solution: '✅',
  verification: '🏅',
  level_up: '⬆️',
  badge_earned: '🏆',
  article_approved: '📝',
  article_rejected: '❌',
  article_comment: '💭'
};

export const NOTIFICATION_COLORS = {
  mention: 'bg-blue-100 text-blue-800 border-blue-200',
  reply: 'bg-green-100 text-green-800 border-green-200',
  reaction: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  solution: 'bg-green-100 text-green-800 border-green-200',
  verification: 'bg-purple-100 text-purple-800 border-purple-200',
  level_up: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  badge_earned: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  article_approved: 'bg-green-100 text-green-800 border-green-200',
  article_rejected: 'bg-red-100 text-red-800 border-red-200',
  article_comment: 'bg-blue-100 text-blue-800 border-blue-200'
};
