import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  FaEdit, FaTrash, FaLock, FaUnlock, FaT<PERSON>btack, FaExchangeAlt,
  FaCheck, FaEllipsisV, FaExclamationTriangle
} from 'react-icons/fa';
import { 
  editForumPost, 
  deleteForumPost, 
  lockTopic, 
  unlockTopic, 
  pinTopic, 
  unpinTopic, 
  moveTopic, 
  deleteTopic 
} from '@/lib/forums';
import { getForumCategories } from '@/lib/forums';

interface ForumModerationProps {
  contentType: 'post' | 'topic';
  contentId: string;
  topicId?: string;
  categoryId?: string;
  isLocked?: boolean;
  isPinned?: boolean;
  currentContent?: string;
  onSuccess?: () => void;
}

export default function ForumModeration({
  contentType,
  contentId,
  topicId,
  categoryId,
  isLocked = false,
  isPinned = false,
  currentContent,
  onSuccess
}: ForumModerationProps) {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<string | null>(null);
  const [reason, setReason] = useState('');
  const [editedContent, setEditedContent] = useState(currentContent || '');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState(categoryId || '');
  const [hardDelete, setHardDelete] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const openModal = async (type: string) => {
    setModalType(type);
    setReason('');
    setError(null);
    
    if (type === 'edit_post') {
      setEditedContent(currentContent || '');
    }
    
    if (type === 'move_topic') {
      try {
        const categoriesData = await getForumCategories();
        setCategories(categoriesData);
      } catch (err) {
        console.error('Error loading categories:', err);
        setError('Failed to load categories');
      }
    }
    
    setIsModalOpen(true);
    setIsMenuOpen(false);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setModalType(null);
  };

  const handleAction = async () => {
    if (!reason.trim() && modalType !== 'edit_post') {
      setError('Please provide a reason for this action');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      switch (modalType) {
        case 'edit_post':
          if (!editedContent.trim()) {
            setError('Post content cannot be empty');
            return;
          }
          
          await editForumPost({
            postId: contentId,
            content: editedContent,
            moderatorId: 'current-user', // This will be replaced with the actual user ID in the function
            reason: reason || 'Content edited by moderator'
          });
          break;
          
        case 'delete_post':
          await deleteForumPost({
            postId: contentId,
            moderatorId: 'current-user',
            reason,
            hardDelete
          });
          break;
          
        case 'lock_topic':
          await lockTopic({
            topicId: contentId,
            moderatorId: 'current-user',
            reason
          });
          break;
          
        case 'unlock_topic':
          await unlockTopic({
            topicId: contentId,
            moderatorId: 'current-user',
            reason
          });
          break;
          
        case 'pin_topic':
          await pinTopic({
            topicId: contentId,
            moderatorId: 'current-user',
            reason
          });
          break;
          
        case 'unpin_topic':
          await unpinTopic({
            topicId: contentId,
            moderatorId: 'current-user',
            reason
          });
          break;
          
        case 'move_topic':
          if (!selectedCategoryId) {
            setError('Please select a destination category');
            return;
          }
          
          await moveTopic({
            topicId: contentId,
            newCategoryId: selectedCategoryId,
            moderatorId: 'current-user',
            reason
          });
          break;
          
        case 'delete_topic':
          await deleteTopic({
            topicId: contentId,
            moderatorId: 'current-user',
            reason,
            hardDelete
          });
          
          // Redirect to category page after topic deletion
          if (categoryId) {
            const category = categories.find(c => c.id === categoryId);
            if (category) {
              router.push(`/forums/${category.slug}`);
              return;
            }
          }
          
          router.push('/forums');
          return;
          
        default:
          setError('Unknown action');
          return;
      }
      
      // Close modal and call success callback
      closeModal();
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error(`Error performing ${modalType}:`, err);
      setError(err.message || `Failed to ${modalType.replace('_', ' ')}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      <div className="flex justify-end mb-2">
        <button
          onClick={toggleMenu}
          className="p-2 text-gray-500 hover:text-nature-green hover:bg-gray-100 rounded-full"
          aria-label="Moderation options"
        >
          <FaEllipsisV />
        </button>
      </div>
      
      {isMenuOpen && (
        <div className="absolute right-0 top-10 z-10 w-48 bg-white rounded-md shadow-lg py-1">
          {contentType === 'post' && (
            <>
              <button
                onClick={() => openModal('edit_post')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <FaEdit className="mr-2 text-yellow-500" />
                Edit Post
              </button>
              <button
                onClick={() => openModal('delete_post')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <FaTrash className="mr-2 text-red-500" />
                Delete Post
              </button>
            </>
          )}
          
          {contentType === 'topic' && (
            <>
              {isLocked ? (
                <button
                  onClick={() => openModal('unlock_topic')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                >
                  <FaUnlock className="mr-2 text-green-500" />
                  Unlock Topic
                </button>
              ) : (
                <button
                  onClick={() => openModal('lock_topic')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                >
                  <FaLock className="mr-2 text-gray-500" />
                  Lock Topic
                </button>
              )}
              
              {isPinned ? (
                <button
                  onClick={() => openModal('unpin_topic')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                >
                  <FaThumbtack className="mr-2 text-gray-500" />
                  Unpin Topic
                </button>
              ) : (
                <button
                  onClick={() => openModal('pin_topic')}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                >
                  <FaThumbtack className="mr-2 text-blue-500" />
                  Pin Topic
                </button>
              )}
              
              <button
                onClick={() => openModal('move_topic')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <FaExchangeAlt className="mr-2 text-purple-500" />
                Move Topic
              </button>
              
              <button
                onClick={() => openModal('delete_topic')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <FaTrash className="mr-2 text-red-500" />
                Delete Topic
              </button>
            </>
          )}
        </div>
      )}
      
      {/* Moderation Action Modal */}
      {isModalOpen && modalType && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              {modalType === 'edit_post' && <FaEdit className="text-xl text-yellow-500 mr-2" />}
              {modalType === 'delete_post' && <FaTrash className="text-xl text-red-500 mr-2" />}
              {modalType === 'lock_topic' && <FaLock className="text-xl text-gray-500 mr-2" />}
              {modalType === 'unlock_topic' && <FaUnlock className="text-xl text-green-500 mr-2" />}
              {modalType === 'pin_topic' && <FaThumbtack className="text-xl text-blue-500 mr-2" />}
              {modalType === 'unpin_topic' && <FaThumbtack className="text-xl text-gray-500 mr-2" />}
              {modalType === 'move_topic' && <FaExchangeAlt className="text-xl text-purple-500 mr-2" />}
              {modalType === 'delete_topic' && <FaTrash className="text-xl text-red-500 mr-2" />}
              
              <h3 className="text-lg font-bold capitalize">
                {modalType.replace('_', ' ')}
              </h3>
            </div>
            
            {error && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-100">
                <div className="flex items-center">
                  <FaExclamationTriangle className="mr-2" />
                  <span>{error}</span>
                </div>
              </div>
            )}
            
            {modalType === 'edit_post' && (
              <div className="mb-4">
                <label htmlFor="editedContent" className="block text-sm font-medium text-gray-700 mb-1">
                  Edit Post Content
                </label>
                <textarea
                  id="editedContent"
                  value={editedContent}
                  onChange={(e) => setEditedContent(e.target.value)}
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                />
              </div>
            )}
            
            {modalType === 'move_topic' && (
              <div className="mb-4">
                <label htmlFor="categorySelect" className="block text-sm font-medium text-gray-700 mb-1">
                  Select Destination Category
                </label>
                <select
                  id="categorySelect"
                  value={selectedCategoryId}
                  onChange={(e) => setSelectedCategoryId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option 
                      key={category.id} 
                      value={category.id}
                      disabled={category.id === categoryId}
                    >
                      {category.name} {category.id === categoryId ? '(Current)' : ''}
                    </option>
                  ))}
                </select>
              </div>
            )}
            
            {(modalType === 'delete_post' || modalType === 'delete_topic') && (
              <div className="mb-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="hardDelete"
                    checked={hardDelete}
                    onChange={(e) => setHardDelete(e.target.checked)}
                    className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                  />
                  <label htmlFor="hardDelete" className="ml-2 block text-sm text-gray-700">
                    Permanently delete (cannot be undone)
                  </label>
                </div>
                <p className="text-xs text-gray-500 mt-1 ml-6">
                  If unchecked, content will be soft-deleted and can be restored later.
                </p>
              </div>
            )}
            
            <div className="mb-4">
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
                Reason {modalType !== 'edit_post' && '*'}
              </label>
              <textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Explain why you're taking this action..."
                required={modalType !== 'edit_post'}
              />
              <p className="text-xs text-gray-500 mt-1">
                This will be logged in the moderation history and may be visible to the user.
              </p>
            </div>
            
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAction}
                disabled={isLoading}
                className={`px-4 py-2 text-white rounded-md transition-colors flex items-center ${
                  modalType.includes('delete') 
                    ? 'bg-red-600 hover:bg-red-700' 
                    : 'bg-nature-green hover:bg-green-700'
                }`}
              >
                <FaCheck className="mr-2" />
                {isLoading ? 'Processing...' : 'Confirm'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
