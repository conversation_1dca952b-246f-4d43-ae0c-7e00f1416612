[[plugins]]
package = "@netlify/plugin-nextjs"

[build]
  command = "npm install --legacy-peer-deps && npm run build"
  publish = ".next"

[build.environment]
  # Enable Next.js build cache
  NEXT_USE_NETLIFY_EDGE = "true"
  NEXT_FORCE_EDGE_IMAGES = "true"
  # Disable static generation for dynamic routes
  NETLIFY_NEXT_PLUGIN_SKIP_INSTALL = "true"
  # Enable server-side rendering for dynamic routes
  NEXT_DISABLE_NETLIFY_EDGE = "true"
  # Note: Supabase environment variables should be set in the Netlify UI
  # NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY

# Enable Netlify's build cache
[[plugins]]
package = "netlify-plugin-cache"
  [plugins.inputs]
  paths = [
    ".next/cache", # Next.js cache
    "node_modules/.cache", # Node modules cache
    ".cache" # Gatsby cache (if applicable)
  ]
