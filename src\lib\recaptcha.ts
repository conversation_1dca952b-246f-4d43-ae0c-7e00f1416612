/**
 * Verifies a reCAPTCHA token with Google's verification API
 * @param token The reCAPTCHA token to verify
 * @returns A promise that resolves to a boolean indicating if the token is valid
 */
export async function verifyRecaptcha(token: string): Promise<boolean> {
  if (!token) return false;
  
  // For testing purposes, if using the test key, always return true
  if (process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY === '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI') {
    return true;
  }

  try {
    const secretKey = process.env.RECAPTCHA_SECRET_KEY;
    if (!secretKey) {
      console.error('RECAPTCHA_SECRET_KEY is not defined');
      return false;
    }

    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${secretKey}&response=${token}`,
    });

    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('Error verifying reCAPTCHA:', error);
    return false;
  }
}
