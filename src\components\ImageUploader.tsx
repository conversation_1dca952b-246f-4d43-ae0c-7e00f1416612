'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { FaUpload, FaImage, FaTimes, FaSpinner } from 'react-icons/fa';

interface ImageUploaderProps {
  onImageUploaded: (imageData: { id: string; url: string; title: string }) => void;
  maxSize?: number; // in MB
  allowedTypes?: string[];
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUploaded,
  maxSize = 5, // Default 5MB
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [imageTitle, setImageTitle] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);

    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];

      // Check file type
      if (!allowedTypes.includes(selectedFile.type)) {
        setError(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
        return;
      }

      // Check file size
      if (selectedFile.size > maxSize * 1024 * 1024) {
        setError(`File size exceeds ${maxSize}MB limit`);
        return;
      }

      setFile(selectedFile);
      setImageTitle(selectedFile.name.split('.')[0].replace(/-|_/g, ' '));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(selectedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    setError(null);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];

      // Check file type
      if (!allowedTypes.includes(droppedFile.type)) {
        setError(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
        return;
      }

      // Check file size
      if (droppedFile.size > maxSize * 1024 * 1024) {
        setError(`File size exceeds ${maxSize}MB limit`);
        return;
      }

      setFile(droppedFile);
      setImageTitle(droppedFile.name.split('.')[0].replace(/-|_/g, ' '));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(droppedFile);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setError('Please select a file to upload');
      return;
    }

    if (!imageTitle.trim()) {
      setError('Please provide a title for the image');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const supabase = createClientComponentClient();

      // Skip bucket check - we'll try to upload directly
      // If it fails, we'll handle the error appropriately

      // 1. Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      // Don't use subdirectories until we're sure they're supported
      const filePath = fileName;

      // Get the current user ID for RLS policies
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      if (!currentUser) {
        throw new Error('You must be logged in to upload images');
      }

      const { error: uploadError, data: uploadData } = await supabase.storage
        .from('media')
        .upload(filePath, file, {
          // Add cacheControl and upsert options
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);

        if (uploadError.message.includes('status = \'published\'')) {
          throw new Error(
            'Invalid RLS policy: The storage.objects table does not have a "status" column. ' +
            'Please check the Storage RLS Guide for the correct policies.'
          );
        }

        if (uploadError.message.includes('row-level security') ||
            uploadError.message.includes('new row violates')) {
          throw new Error(
            'Permission denied: Storage bucket RLS policies need to be configured. ' +
            'Please check the Storage RLS Guide for the correct policies.'
          );
        }

        if (uploadError.message.includes('not found') ||
            uploadError.message.includes('does not exist')) {
          throw new Error(
            'The media bucket does not exist or is not accessible. ' +
            'Please create a bucket named "media" in your Supabase storage.'
          );
        }

        throw new Error(`Error uploading file: ${uploadError.message}`);
      }

      // 2. Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('media')
        .getPublicUrl(filePath);

      // 3. Create a record in the media table
      const { data: mediaData, error: mediaError } = await supabase
        .from('media')
        .insert({
          title: imageTitle,
          description: imageAlt || imageTitle,
          file_url: publicUrl,
          file_type: file.type,
          file_size: file.size,
          uploader_id: (await supabase.auth.getUser()).data.user?.id,
          status: 'published' // Auto-approve images uploaded by admins
        })
        .select()
        .single();

      if (mediaError) {
        // If we successfully uploaded but failed to create a record, we should clean up the uploaded file
        try {
          await supabase.storage.from('media').remove([filePath]);
        } catch (cleanupErr) {
          console.error('Failed to clean up uploaded file after media record creation failed:', cleanupErr);
        }

        if (mediaError.message.includes('row-level security') ||
            mediaError.message.includes('new row violates')) {
          throw new Error(
            'Permission denied: Media table RLS policies need to be configured. ' +
            'Please check your Supabase RLS policies for the media table.'
          );
        }

        throw new Error(`Error creating media record: ${mediaError.message}`);
      }

      // 4. Return the uploaded image data
      onImageUploaded({
        id: mediaData.id,
        url: publicUrl,
        title: imageTitle
      });

      // 5. Reset the form
      setFile(null);
      setPreview(null);
      setImageTitle('');
      setImageAlt('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (err: any) {
      console.error('Error uploading image:', err);
      setError(err.message || 'An error occurred while uploading the image');
    } finally {
      setUploading(false);
    }
  };

  const handleCancel = () => {
    setFile(null);
    setPreview(null);
    setImageTitle('');
    setImageAlt('');
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="image-uploader">
      {!file ? (
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <FaImage className="h-12 w-12 mx-auto text-gray-400 mb-3" />
          <p className="text-gray-700 mb-2">Drag and drop an image here, or click to browse</p>
          <p className="text-xs text-gray-500">
            Supported formats: JPG, PNG, GIF, WebP (Max {maxSize}MB)
          </p>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept={allowedTypes.join(',')}
            className="hidden"
          />
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex justify-between items-start mb-4">
            <h3 className="text-lg font-medium text-gray-900">Upload Image</h3>
            <button
              type="button"
              onClick={handleCancel}
              className="text-gray-400 hover:text-gray-500"
              disabled={uploading}
            >
              <FaTimes />
            </button>
          </div>

          {preview && (
            <div className="mb-4 relative">
              <div className="aspect-w-16 aspect-h-9 bg-gray-100 rounded-md overflow-hidden">
                <Image
                  src={preview}
                  alt="Preview"
                  width={400}
                  height={225}
                  className="object-contain w-full h-full"
                />
              </div>
            </div>
          )}

          <div className="space-y-3">
            <div>
              <label htmlFor="image-title" className="block text-sm font-medium text-gray-700 mb-1">
                Image Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="image-title"
                value={imageTitle}
                onChange={(e) => setImageTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Enter a descriptive title"
                disabled={uploading}
                required
              />
            </div>

            <div>
              <label htmlFor="image-alt" className="block text-sm font-medium text-gray-700 mb-1">
                Alt Text (for accessibility)
              </label>
              <input
                type="text"
                id="image-alt"
                value={imageAlt}
                onChange={(e) => setImageAlt(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Describe the image for screen readers"
                disabled={uploading}
              />
            </div>

            {error && (
              <div className="text-red-500 text-sm py-2">
                {error}
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={uploading}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleUpload}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
                disabled={uploading}
              >
                {uploading ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <FaUpload className="mr-2" />
                    Upload
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
