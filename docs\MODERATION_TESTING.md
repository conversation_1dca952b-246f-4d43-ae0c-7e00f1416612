# Testing the Moderation System

This document provides instructions for testing the moderation system features that have been implemented.

## Prerequisites

1. Make sure you have applied the database migrations in the `supabase/migrations` directory
2. Ensure you have at least one admin user, one moderator user, and a few regular users in your system
3. Have some forum categories, topics, and posts created for testing

## Testing User Banning/Suspension

### Admin/Moderator Actions

1. Log in as an admin or moderator
2. Navigate to `/admin/users/bans`
3. Click "Ban User" to create a new ban
4. Search for a user and select them
5. Choose a ban type (full, post-only, read-only)
6. Set a duration (temporary or permanent)
7. Provide a reason and submit the ban
8. Verify the user appears in the banned users list

### Testing Ban Effects

1. Log in as the banned user
2. Depending on the ban type:
   - Full ban: Should be redirected to a ban notification page
   - Post-only: Should be able to browse but not create posts
   - Read-only: Should be able to browse but not interact

### Testing Unbanning

1. Log in as an admin or moderator
2. Navigate to `/admin/users/bans`
3. Find the banned user and click "Unban"
4. Provide a reason and confirm
5. Verify the user is removed from the active bans list
6. Log in as the unbanned user and verify they can use the site normally

## Testing Content Filtering

1. Log in as a regular user
2. Try to create a post or topic with words that match the banned words list
3. Verify that the content is automatically filtered
4. Log in as an admin and check the auto moderation logs

## Testing Bulk Moderation

1. Log in as an admin or moderator
2. Navigate to a forum category with multiple topics
3. Select multiple topics using the checkboxes
4. Use the bulk actions dropdown to perform an action (lock, pin, move, delete)
5. Provide a reason and confirm
6. Verify the action was applied to all selected topics

## Testing the Moderation Queue

### Reporting Content

1. Log in as a regular user
2. Find a post or topic
3. Click the "Report" button
4. Select a reason and submit the report
5. Verify a confirmation message appears

### Processing Reports

1. Log in as an admin or moderator
2. Navigate to `/admin/moderation-queue`
3. View the pending reports
4. For each report:
   - Click "View" to see the reported content
   - Click "Dismiss" if no action is needed
   - Click "Resolve" if you've taken action on the content
5. Provide notes and confirm
6. Verify the report moves to the resolved or dismissed status

## Testing Notifications

1. Perform moderation actions on content (edit, delete, lock, etc.)
2. Log in as the content owner
3. Check for notifications about the moderation actions
4. Click on a notification to view the affected content
5. Mark notifications as read and verify they update correctly

## Testing Moderation Logs

1. Log in as an admin or moderator
2. Navigate to `/admin/moderation-logs`
3. View the list of moderation actions
4. Test the filters (by action type, content type, moderator, date range)
5. Click on an action to view its details
6. Test the CSV export functionality

## Testing Category Management

1. Log in as an admin
2. Navigate to `/admin/forums/categories`
3. Test creating a new category
4. Test editing an existing category
5. Test reordering categories using drag and drop
6. Test deleting a category (with options to move or delete topics)

## Troubleshooting

If you encounter issues during testing:

1. Check the browser console for JavaScript errors
2. Check the server logs for backend errors
3. Verify that the database tables were created correctly
4. Ensure the RLS policies are properly applied
5. Check that the user has the correct role (admin, moderator, or user)

## Automated Testing

You can use the `scripts/test-moderation.js` script to perform automated tests of the moderation system's backend functionality:

1. Update the script with your Supabase URL and anon key
2. Update the test user credentials
3. Run the script with Node.js: `node scripts/test-moderation.js`
4. Review the output for any errors or warnings
