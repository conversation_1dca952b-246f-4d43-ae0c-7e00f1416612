'use client';

import React from 'react';
import { FaInfoCircle } from 'react-icons/fa';

const StorageRLSGuide: React.FC = () => {
  return (
    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 my-4">
      <div className="flex items-start">
        <FaInfoCircle className="text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
        <div>
          <h4 className="text-sm font-medium text-blue-800">Storage RLS Policy Setup Guide</h4>
          <p className="text-sm text-blue-700 mt-1 mb-4">
            You need to set up the correct Row Level Security (RLS) policies for your storage bucket. Follow these steps:
          </p>
          
          <ol className="list-decimal list-inside text-sm text-blue-700 space-y-2 mb-4">
            <li>Go to your Supabase project dashboard</li>
            <li>Navigate to the Storage section</li>
            <li>Click on the "Policies" tab</li>
            <li>Delete any existing policies that are causing errors</li>
            <li>Create the following policies for your media bucket:</li>
          </ol>
          
          <h5 className="text-sm font-medium text-blue-800 mt-4 mb-2">1. Policy for SELECT (downloading files)</h5>
          <div className="bg-gray-100 p-3 rounded-md mb-4 overflow-x-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {`-- Allow anyone to download files
CREATE POLICY "Public Access" 
ON storage.objects 
FOR SELECT 
TO public 
USING (bucket_id = 'media');`}
            </pre>
          </div>
          
          <h5 className="text-sm font-medium text-blue-800 mt-4 mb-2">2. Policy for INSERT (uploading files)</h5>
          <div className="bg-gray-100 p-3 rounded-md mb-4 overflow-x-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {`-- Allow authenticated users to upload files
CREATE POLICY "Authenticated Upload" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'media' AND 
  (auth.uid() = owner OR auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'))
);`}
            </pre>
          </div>
          
          <h5 className="text-sm font-medium text-blue-800 mt-4 mb-2">3. Policy for UPDATE (modifying files)</h5>
          <div className="bg-gray-100 p-3 rounded-md mb-4 overflow-x-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {`-- Allow file owners and admins to update files
CREATE POLICY "Owner and Admin Update" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (
  bucket_id = 'media' AND 
  (auth.uid() = owner OR auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'))
);`}
            </pre>
          </div>
          
          <h5 className="text-sm font-medium text-blue-800 mt-4 mb-2">4. Policy for DELETE (removing files)</h5>
          <div className="bg-gray-100 p-3 rounded-md mb-4 overflow-x-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {`-- Allow file owners and admins to delete files
CREATE POLICY "Owner and Admin Delete" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'media' AND 
  (auth.uid() = owner OR auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'))
);`}
            </pre>
          </div>
          
          <p className="text-sm text-blue-700 mt-4">
            After setting up these policies, refresh the page and try uploading images again.
          </p>
        </div>
      </div>
    </div>
  );
};

export default StorageRLSGuide;
