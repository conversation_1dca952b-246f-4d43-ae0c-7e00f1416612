-- Create user_bans table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_bans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    moderator_id UUID NOT NULL REFERENCES auth.users(id),
    ban_type TEXT NOT NULL CHECK (ban_type IN ('full', 'post_only', 'read_only')),
    reason TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ,
    updated_by UUID REFERENCES auth.users(id),
    revoked_at TIMESTAMPTZ,
    revoked_by UUID REFERENCES auth.users(id),
    revoke_reason TEXT
);

-- Check if reported_content table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'reported_content') THEN
        -- Create reported_content table if it doesn't exist
        CREATE TABLE public.reported_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type TEXT NOT NULL CHECK (content_type IN ('post', 'topic')),
    content_id UUID NOT NULL,
    reporter_id UUID NOT NULL REFERENCES auth.users(id),
    reason TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'resolved', 'dismissed')),
    moderator_id UUID REFERENCES auth.users(id),
    moderator_notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    resolved_at TIMESTAMPTZ
        );
    END IF;
END $$;

-- Check if moderation_actions table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'moderation_actions') THEN
        -- Create moderation_actions table if it doesn't exist
        CREATE TABLE public.moderation_actions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    action_type TEXT NOT NULL,
    moderator_id UUID NOT NULL REFERENCES auth.users(id),
    content_id TEXT NOT NULL,
    content_type TEXT NOT NULL,
    reason TEXT,
    previous_content TEXT,
    new_content TEXT,
    additional_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
    ELSE
        -- Check if action_type column exists and add it if it doesn't
        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'moderation_actions' AND column_name = 'action_type') THEN
            ALTER TABLE public.moderation_actions ADD COLUMN action_type TEXT;
        END IF;
    END IF;
END $$;

-- Create auto_moderation_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.auto_moderation_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    content_type TEXT NOT NULL CHECK (content_type IN ('post', 'topic')),
    original_content TEXT NOT NULL,
    filtered_content TEXT NOT NULL,
    matched_words TEXT[],
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create user_notifications table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    link_url TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    read_at TIMESTAMPTZ
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_bans_user_id ON public.user_bans(user_id);
CREATE INDEX IF NOT EXISTS idx_user_bans_active ON public.user_bans(user_id) WHERE revoked_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_reported_content_status ON public.reported_content(status);
CREATE INDEX IF NOT EXISTS idx_reported_content_content ON public.reported_content(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_reported_content_reporter ON public.reported_content(reporter_id);

CREATE INDEX IF NOT EXISTS idx_moderation_actions_moderator ON public.moderation_actions(moderator_id);
CREATE INDEX IF NOT EXISTS idx_moderation_actions_content ON public.moderation_actions(content_type, content_id);
-- Create index on action_type column if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'moderation_actions' AND column_name = 'action_type') THEN
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'moderation_actions' AND indexname = 'idx_moderation_actions_action_type') THEN
            CREATE INDEX idx_moderation_actions_action_type ON public.moderation_actions(action_type);
        END IF;
    END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_auto_moderation_logs_user ON public.auto_moderation_logs(user_id);

CREATE INDEX IF NOT EXISTS idx_user_notifications_user ON public.user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_unread ON public.user_notifications(user_id) WHERE read_at IS NULL;

-- Add comment to tables
COMMENT ON TABLE public.user_bans IS 'Stores information about banned users';
COMMENT ON TABLE public.reported_content IS 'Stores reports of inappropriate content';
COMMENT ON TABLE public.moderation_actions IS 'Logs all moderation actions taken by moderators';
COMMENT ON TABLE public.auto_moderation_logs IS 'Logs automatic content filtering actions';
COMMENT ON TABLE public.user_notifications IS 'Stores notifications for users';
