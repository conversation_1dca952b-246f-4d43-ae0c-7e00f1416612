require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

async function applyMigrations() {
  // Create Supabase client with service role key
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );

  console.log('Applying new feature migrations...');

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250501_add_features.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    // Execute the SQL
    const { error } = await supabase.rpc('execute_sql', { sql: migrationSql });

    if (error) {
      console.error('Error applying migrations:', error);
      process.exit(1);
    }

    console.log('✅ Migrations applied successfully!');

    // Verify tables were created
    const tables = [
      'bookmarks',
      'notifications',
      'user_activities',
      'user_preferences',
      'analytics_events'
    ];

    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('id')
        .limit(1);

      if (error && !error.message.includes('does not exist')) {
        console.error(`Error verifying table ${table}:`, error);
      } else {
        console.log(`✅ Table ${table} verified`);
      }
    }

    console.log('Migration complete!');
  } catch (err) {
    console.error('Unexpected error:', err);
    process.exit(1);
  }
}

applyMigrations();
