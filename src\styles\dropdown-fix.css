/* Dropdown menu text color fix */
.user-dropdown-menu {
  background-color: white !important;
  color: black !important;
}

.user-dropdown-menu * {
  color: black !important;
}

.user-dropdown-menu p {
  color: black !important;
}

.user-dropdown-menu a {
  color: black !important;
}

.user-dropdown-menu span {
  color: black !important;
}

.user-dropdown-menu button {
  color: black !important;
}

.user-dropdown-menu svg {
  color: #2e7d32 !important;
}

.user-dropdown-menu .sign-out {
  color: #dc2626 !important;
}

.user-dropdown-menu .sign-out * {
  color: #dc2626 !important;
}

.user-dropdown-menu .sign-out svg {
  color: #dc2626 !important;
}

.user-dropdown-menu .header-text {
  color: #666666 !important;
}

.user-dropdown-menu .email-text {
  color: black !important;
  font-weight: 500 !important;
}

/* Override any dark mode styles */
@media (prefers-color-scheme: dark) {
  .user-dropdown-menu {
    background-color: white !important;
    color: black !important;
  }

  .user-dropdown-menu * {
    color: black !important;
  }

  .user-dropdown-menu p {
    color: black !important;
  }

  .user-dropdown-menu a {
    color: black !important;
  }

  .user-dropdown-menu span {
    color: black !important;
  }

  .user-dropdown-menu button {
    color: black !important;
  }

  .user-dropdown-menu svg {
    color: #2e7d32 !important;
  }

  .user-dropdown-menu .sign-out {
    color: #dc2626 !important;
  }

  .user-dropdown-menu .sign-out * {
    color: #dc2626 !important;
  }

  .user-dropdown-menu .sign-out svg {
    color: #dc2626 !important;
  }

  .user-dropdown-menu .header-text {
    color: #666666 !important;
  }

  .user-dropdown-menu .email-text {
    color: black !important;
    font-weight: 500 !important;
  }
}
