'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getModerationActionDetails } from '@/lib/moderation';
import { format } from 'date-fns';
import Link from 'next/link';
import {
  FaEdit, FaTrash, FaLock, FaUnlock, FaThumbtack, FaExchangeAlt,
  FaCheck, FaExclamationTriangle, FaUser, FaFolder, FaArrowLeft,
  FaEye, FaHistory, FaCode
} from 'react-icons/fa';

export default function ModerationLogDetailPage({ params }) {
  const router = useRouter();
  const { id } = params;
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [action, setAction] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showPreviousContent, setShowPreviousContent] = useState(false);
  const [showNewContent, setShowNewContent] = useState(false);
  const [showAdditionalData, setShowAdditionalData] = useState(false);

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        // Redirect to unauthorized page if not admin/moderator
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Load moderation action details
      loadActionDetails();
    }

    checkAuth();
  }, [router, user, loading, id]);

  async function loadActionDetails() {
    try {
      setIsLoading(true);
      setError(null);

      const data = await getModerationActionDetails(id);

      if (!data) {
        setError('Moderation log not found');
        setAction(null);
        return;
      }

      setAction(data);
    } catch (err) {
      console.error('Error loading moderation action details:', err);
      setError('Failed to load moderation log details: ' + (err.message || 'Unknown error'));
      setAction(null);
    } finally {
      setIsLoading(false);
    }
  }

  // Helper function to get icon for action type
  const getActionIcon = (actionType) => {
    // Use action_type if available, otherwise use action
    const actionValue = actionType || '';
    switch (actionValue) {
      case 'edit_post':
        return <FaEdit className="text-yellow-500" />;
      case 'soft_delete_post':
      case 'hard_delete_post':
      case 'soft_delete_topic':
      case 'hard_delete_topic':
        return <FaTrash className="text-red-500" />;
      case 'lock_topic':
        return <FaLock className="text-gray-500" />;
      case 'unlock_topic':
        return <FaUnlock className="text-green-500" />;
      case 'pin_topic':
      case 'unpin_topic':
        return <FaThumbtack className="text-blue-500" />;
      case 'move_topic':
        return <FaExchangeAlt className="text-purple-500" />;
      case 'mark_solution':
        return <FaCheck className="text-green-500" />;
      case 'ban_user':
        return <FaUser className="text-red-500" />;
      case 'unban_user':
        return <FaUser className="text-green-500" />;
      case 'create_category':
      case 'update_category':
      case 'delete_category':
        return <FaFolder className="text-blue-500" />;
      case 'resolved_report':
      case 'dismissed_report':
        return <FaExclamationTriangle className="text-orange-500" />;
      default:
        return <FaHistory className="text-gray-500" />;
    }
  };

  // Helper function to format content for display
  const formatContent = (content) => {
    if (!content) return null;

    try {
      // If it's JSON, parse and format it
      const parsed = JSON.parse(content);
      return (
        <pre className="bg-gray-50 p-4 rounded-md overflow-auto text-xs">
          {JSON.stringify(parsed, null, 2)}
        </pre>
      );
    } catch (e) {
      // If it's not JSON, just display as text
      return (
        <div className="bg-gray-50 p-4 rounded-md whitespace-pre-wrap text-sm">
          {content}
        </div>
      );
    }
  };

  // Helper function to get content link
  const getContentLink = () => {
    if (!action) return null;

    switch (action.content_type) {
      case 'forum_post':
        if (action.contentDetails?.topic) {
          return `/forums/${action.contentDetails.topic.category?.slug || 'category'}/${action.contentDetails.topic.slug || 'topic'}#post-${action.content_id}`;
        }
        return null;
      case 'forum_topic':
        if (action.contentDetails?.category) {
          return `/forums/${action.contentDetails.category.slug || 'category'}/${action.contentDetails.slug || 'topic'}`;
        }
        return null;
      case 'user':
        return `/profile/${action.contentDetails?.username || action.content_id}`;
      case 'forum_category':
        return `/forums/${action.contentDetails?.slug || 'category'}`;
      default:
        return null;
    }
  };

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading moderation log details...</span>
      </div>
    );
  }

  if (error || !action) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <button
            onClick={() => router.push('/admin/moderation-logs')}
            className="mr-4 text-nature-green hover:underline flex items-center"
          >
            <FaArrowLeft className="mr-1" />
            Back to Logs
          </button>
          <h1 className="text-3xl font-bold">Moderation Log Details</h1>
        </div>

        <div className="bg-red-50 text-red-700 p-6 rounded-lg shadow-md">
          <p className="font-bold text-xl mb-2">Error</p>
          <p>{error || 'Moderation log not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.push('/admin/moderation-logs')}
          className="mr-4 text-nature-green hover:underline flex items-center"
        >
          <FaArrowLeft className="mr-1" />
          Back to Logs
        </button>
        <h1 className="text-3xl font-bold">Moderation Log Details</h1>
      </div>

      {/* Action Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            <div className="bg-gray-100 p-3 rounded-full mr-4">
              {getActionIcon(action.action_type || action.action)}
            </div>
            <div>
              <h2 className="text-xl font-bold capitalize">
                {((action.action_type || action.action || '') + '').replace(/_/g, ' ')}
              </h2>
              <p className="text-gray-500">
                {format(new Date(action.created_at), 'MMMM d, yyyy h:mm a')}
              </p>
            </div>
          </div>

          {getContentLink() && (
            <Link
              href={getContentLink()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
              target="_blank"
            >
              <FaEye className="mr-2" />
              View Content
            </Link>
          )}
        </div>
      </div>

      {/* Action Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">Moderator</h3>
          <div className="flex items-center">
            {action.moderator?.avatar_url ? (
              <img
                src={action.moderator.avatar_url}
                alt={action.moderator.username}
                className="w-12 h-12 rounded-full mr-4"
              />
            ) : (
              <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                <FaUser className="text-gray-500" />
              </div>
            )}
            <div>
              <p className="font-medium">{action.moderator?.full_name || action.moderator?.username || 'Unknown'}</p>
              <p className="text-sm text-gray-500">{action.moderator?.role || 'Moderator'}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">Content Information</h3>
          <div className="space-y-2">
            <div>
              <span className="font-medium">Type:</span>
              <span className="ml-2">{action.content_type}</span>
            </div>
            <div>
              <span className="font-medium">ID:</span>
              <span className="ml-2">{action.content_id}</span>
            </div>
            {action.contentDetails && (
              <div>
                <span className="font-medium">Details:</span>
                <div className="mt-2 bg-gray-50 p-3 rounded-md text-sm">
                  {action.content_type === 'forum_post' && (
                    <>
                      <p><span className="font-medium">Author:</span> {action.contentDetails.author?.username || 'Unknown'}</p>
                      <p><span className="font-medium">Topic:</span> {action.contentDetails.topic?.title || 'Unknown'}</p>
                      <p className="mt-2 text-xs text-gray-500 line-clamp-3">{action.contentDetails.content}</p>
                    </>
                  )}
                  {action.content_type === 'forum_topic' && (
                    <>
                      <p><span className="font-medium">Title:</span> {action.contentDetails.title || 'Unknown'}</p>
                      <p><span className="font-medium">Author:</span> {action.contentDetails.author?.username || 'Unknown'}</p>
                      <p><span className="font-medium">Category:</span> {action.contentDetails.category?.name || 'Unknown'}</p>
                    </>
                  )}
                  {action.content_type === 'user' && (
                    <>
                      <p><span className="font-medium">Username:</span> {action.contentDetails.username || 'Unknown'}</p>
                      <p><span className="font-medium">Name:</span> {action.contentDetails.full_name || 'Unknown'}</p>
                      <p><span className="font-medium">Role:</span> {action.contentDetails.role || 'User'}</p>
                      {action.contentDetails.is_banned && (
                        <p className="text-red-600">
                          <span className="font-medium">Banned:</span> Yes
                          {action.contentDetails.ban_expires_at && (
                            <span> (until {format(new Date(action.contentDetails.ban_expires_at), 'MMM d, yyyy')})</span>
                          )}
                        </p>
                      )}
                    </>
                  )}
                  {action.content_type === 'forum_category' && (
                    <>
                      <p><span className="font-medium">Name:</span> {action.contentDetails.name || 'Unknown'}</p>
                      <p><span className="font-medium">Slug:</span> {action.contentDetails.slug || 'Unknown'}</p>
                      <p><span className="font-medium">Description:</span> {action.contentDetails.description || 'None'}</p>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Reason */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">Reason</h3>
        <div className="bg-gray-50 p-4 rounded-md">
          <p className="whitespace-pre-line">{action.reason || 'No reason provided'}</p>
        </div>
      </div>

      {/* Content Changes */}
      {action.previous_content && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Previous Content</h3>
            <button
              onClick={() => setShowPreviousContent(!showPreviousContent)}
              className="text-nature-green hover:underline flex items-center"
            >
              <FaCode className="mr-1" />
              {showPreviousContent ? 'Hide' : 'Show'}
            </button>
          </div>
          {showPreviousContent && formatContent(action.previous_content)}
        </div>
      )}

      {action.new_content && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">New Content</h3>
            <button
              onClick={() => setShowNewContent(!showNewContent)}
              className="text-nature-green hover:underline flex items-center"
            >
              <FaCode className="mr-1" />
              {showNewContent ? 'Hide' : 'Show'}
            </button>
          </div>
          {showNewContent && formatContent(action.new_content)}
        </div>
      )}

      {/* Additional Data */}
      {action.additional_data && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Additional Data</h3>
            <button
              onClick={() => setShowAdditionalData(!showAdditionalData)}
              className="text-nature-green hover:underline flex items-center"
            >
              <FaCode className="mr-1" />
              {showAdditionalData ? 'Hide' : 'Show'}
            </button>
          </div>
          {showAdditionalData && (
            <pre className="bg-gray-50 p-4 rounded-md overflow-auto text-xs">
              {JSON.stringify(action.additional_data, null, 2)}
            </pre>
          )}
        </div>
      )}
    </div>
  );
}
