-- Add color column to forum_categories table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'forum_categories' 
        AND column_name = 'color'
    ) THEN
        ALTER TABLE public.forum_categories ADD COLUMN color TEXT;
        RAISE NOTICE 'Added color column to forum_categories table';
    ELSE
        RAISE NOTICE 'color column already exists in forum_categories table';
    END IF;
END $$;

-- Add is_active column to forum_categories table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'forum_categories' 
        AND column_name = 'is_active'
    ) THEN
        ALTER TABLE public.forum_categories ADD COLUMN is_active BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added is_active column to forum_categories table';
    ELSE
        RAISE NOTICE 'is_active column already exists in forum_categories table';
    END IF;
END $$;

-- Add created_by column to forum_categories table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'forum_categories' 
        AND column_name = 'created_by'
    ) THEN
        ALTER TABLE public.forum_categories ADD COLUMN created_by UUID REFERENCES public.profiles(id);
        RAISE NOTICE 'Added created_by column to forum_categories table';
    ELSE
        RAISE NOTICE 'created_by column already exists in forum_categories table';
    END IF;
END $$;
