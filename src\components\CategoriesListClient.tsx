'use client';

import Image from "next/image";
import { useState, useEffect } from 'react';

type Category = {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon?: string;
};

const CategoryCard = ({ category }: { category: Category }) => {
  // Default icon mapping for categories
  const getIconForCategory = () => {
    // If the category has an icon specified, use it
    if (category.icon) {
      return category.icon;
    }

    // Otherwise, use a default icon based on the slug
    const iconMap: Record<string, string> = {
      'herbal-remedies': 'leaf',
      'mind-body-practices': 'sparkles',
      'nutritional-healing': 'cake',
      'medicinal-herbs': 'beaker',
      'aromatherapy': 'fire',
      'traditional-medicine': 'academic-cap',
      'ayurveda': 'sun',
      'chinese-medicine': 'moon',
      'homeopathy': 'droplet',
      'naturopathy': 'globe',
      'foraging': 'map',
      'gardening': 'home',
      'herbalism': 'collection',
      'nutrition': 'shopping-cart',
      'essential-oils': 'color-swatch',
      'holistic-health': 'heart',
      'sustainable-living': 'lightning-bolt'
    };

    return iconMap[category.slug] || 'bookmark';
  };

  // Get the icon name for this category
  const iconName = getIconForCategory();

  // Render the appropriate icon
  const renderIcon = () => {
    switch (iconName) {
      case 'leaf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      case 'sparkles':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
          </svg>
        );
      case 'cake':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.701 2.701 0 01-1.5.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zm-3-9v-2a2 2 0 00-2-2H8a2 2 0 00-2 2v2h12z" />
          </svg>
        );
      case 'beaker':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
          </svg>
        );
      case 'fire':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
          </svg>
        );
      case 'academic-cap':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path d="M12 14l9-5-9-5-9 5 9 5z" />
            <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
          </svg>
        );
      case 'heart':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
        );
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:border-green-200 group">
      <div className="bg-[#2e7d32]/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4 text-[#2e7d32] group-hover:bg-[#2e7d32]/20 transition-all duration-300 shadow-sm">
        {renderIcon()}
      </div>
      <h3 className="text-xl font-semibold mb-3 text-gray-900">{category.name}</h3>
      <div className="h-px w-16 bg-green-200 mb-4"></div>
      <p className="text-gray-700 mb-4">{category.description}</p>
      <a
        href={`/categories/${category.slug}`}
        className="text-[#2e7d32] font-medium hover:underline flex items-center transition-all duration-200 transform hover:translate-x-1"
      >
        Explore
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 ml-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </a>
    </div>
  );
};

// Fallback sample categories to ensure we always have something to display
const SAMPLE_CATEGORIES: Category[] = [
  {
    id: '1',
    name: 'Herbal Remedies',
    slug: 'herbal-remedies',
    description: 'Natural plant-based medicines and treatments',
    icon: 'leaf'
  },
  {
    id: '2',
    name: 'Nutritional Healing',
    slug: 'nutritional-healing',
    description: 'Healing through food and dietary approaches',
    icon: 'cake'
  },
  {
    id: '3',
    name: 'Traditional Medicine',
    slug: 'traditional-medicine',
    description: 'Ancient healing practices from around the world',
    icon: 'academic-cap'
  }
];

const CategoriesListClient = () => {
  // Initialize with sample categories so we always have something to show
  const [categories, setCategories] = useState<Category[]>(SAMPLE_CATEGORIES);
  const [loading, setLoading] = useState(false); // Start with false since we have sample data

  useEffect(() => {
    let isMounted = true;
    let loadingTimeout: NodeJS.Timeout;

    async function fetchCategories() {
      try {
        console.log('CategoriesListClient: Fetching categories from API...');

        // Set a timeout to prevent infinite loading
        loadingTimeout = setTimeout(() => {
          if (isMounted) {
            console.log('Categories loading timeout reached, using sample categories');
            setLoading(false);
          }
        }, 3000); // 3 second timeout

        const response = await fetch('/api/categories', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!isMounted) return;

        if (!response.ok) {
          console.log('API call failed, using sample categories');
          setLoading(false);
          return; // Keep using sample categories
        }

        const data = await response.json();
        console.log(`CategoriesListClient: Received ${data?.length || 0} categories`);

        if (isMounted && Array.isArray(data) && data.length > 0) {
          setCategories(data);
        }
      } catch (error: any) {
        console.error('Error fetching categories:', error);
        // Keep using sample categories
      } finally {
        if (isMounted) {
          setLoading(false);
          if (loadingTimeout) {
            clearTimeout(loadingTimeout);
          }
        }
      }
    }

    fetchCategories();

    return () => {
      isMounted = false;
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, []);

  if (loading) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Loading categories...</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {categories.slice(0, 3).map((category) => (
        <CategoryCard key={category.slug} category={category} />
      ))}
    </div>
  );
};

export default CategoriesListClient;