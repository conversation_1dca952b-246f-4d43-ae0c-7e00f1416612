'use client';

import React, { useState, useEffect, useRef, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { getTopicBySlug, getPostsByTopic, createPost, addReaction, markAsSolution } from '@/lib/forums';
import { ForumTopic, ForumPost, REACTION_TYPES } from '@/lib/forum-types';
import Link from 'next/link';
import UserLevelDisplay from '@/components/UserLevelDisplay';
import VerificationBadge from '@/components/VerificationBadge';
import { formatDistanceToNow, format } from 'date-fns';
import { Tooltip } from '@/components/Tooltip';
import ReportContentModal from '@/components/ReportContentModal';
import { motion as Motion, AnimatePresence } from 'framer-motion';
import ForumModeration from '@/components/admin/ForumModeration';
import {
  FaLea<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON>in<PERSON>ang, FaBookMedical, FaMicroscope, FaQuestion,
  FaThumbtack
} from 'react-icons/fa';
import {
  FaRegComments, FaRegLightbulb, FaRegClock,
  FaRegUser, FaRegBookmark, FaRegEye, FaRegBell,
  FaRegThumbsUp, FaRegHandPeace, FaRegLightbulb as FaRegLightbulbOutline,
  FaRegFlag, FaRegEdit
} from 'react-icons/fa';
import { GiMeditation } from 'react-icons/gi';
import {
  HiOutlinePencilSquare, HiChevronLeft, HiChevronRight,
  HiArrowLongLeft, HiCheck, HiLockClosed, HiPaperAirplane,
  HiExclamationTriangle, HiXMark
} from 'react-icons/hi2';
import { FaReply } from 'react-icons/fa';

// Helper function to get icon based on category slug
const getCategoryIcon = (slug: string) => {
  const iconMap: Record<string, React.ReactNode> = {
    'general-discussion': <FaRegComments className="text-nature-green" />,
    'herbal-remedies': <FaLeaf className="text-nature-green" />,
    'nutrition-diet': <FaApple className="text-nature-green" />,
    'mind-body-practices': <GiMeditation className="text-nature-green" />,
    'traditional-medicine': <FaYinYang className="text-nature-green" />,
    'success-stories': <FaRegLightbulb className="text-nature-green" />,
    'research-science': <FaMicroscope className="text-nature-green" />,
    'questions-help': <FaQuestion className="text-nature-green" />
  };

  return iconMap[slug] || <FaRegBookmark className="text-nature-green" />;
};

interface TopicPageProps {
  params: {
    category: string;
    topic: string;
  };
}

export default function TopicPage({ params }: TopicPageProps) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const categorySlug = unwrappedParams.category;
  const topicSlug = unwrappedParams.topic;

  const router = useRouter();
  const { user } = useAuth();
  const [topic, setTopic] = useState<ForumTopic | null>(null);
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [totalPosts, setTotalPosts] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [replyToPost, setReplyToPost] = useState<ForumPost | null>(null);
  const [reportContent, setReportContent] = useState<{type: 'topic' | 'post', id: string} | null>(null);
  const replyFormRef = useRef<HTMLDivElement>(null);

  const postsPerPage = 50;

  useEffect(() => {
    async function loadTopicAndPosts() {
      setIsLoading(true);
      setError(null);

      try {
        // Load topic
        const topicData = await getTopicBySlug(topicSlug);
        setTopic(topicData);

        // Load posts
        const { data, count } = await getPostsByTopic({
          topicId: topicData.id,
          limit: postsPerPage,
          offset: (currentPage - 1) * postsPerPage
        });

        setPosts(data);
        setTotalPosts(count || 0);
      } catch (err: any) {
        setError(err.message || 'Failed to load topic or posts');
      } finally {
        setIsLoading(false);
      }
    }

    loadTopicAndPosts();
  }, [topicSlug, currentPage]);

  const totalPages = Math.ceil(totalPosts / postsPerPage);

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      router.push('/auth/signin?redirect=' + encodeURIComponent(window.location.pathname));
      return;
    }

    if (!replyContent.trim()) {
      setError('Reply content cannot be empty');
      return;
    }

    if (!topic) return;

    setIsSubmitting(true);
    setError(null);

    try {
      await createPost({
        topicId: topic.id,
        content: replyContent,
        authorId: user.id,
        parentId: replyToPost?.id
      });

      // Reload posts
      const { data } = await getPostsByTopic({
        topicId: topic.id,
        limit: postsPerPage,
        offset: (currentPage - 1) * postsPerPage
      });

      setPosts(data);
      setReplyContent('');
      setReplyToPost(null);

      // Scroll to the bottom
      window.scrollTo(0, document.body.scrollHeight);
    } catch (err: any) {
      setError(err.message || 'Failed to submit reply');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReaction = async (postId: string, reactionType: 'like' | 'helpful' | 'insightful') => {
    if (!user) {
      router.push('/auth/signin?redirect=' + encodeURIComponent(window.location.pathname));
      return;
    }

    try {
      await addReaction({
        postId,
        userId: user.id,
        reactionType
      });

      // Reload posts
      const { data } = await getPostsByTopic({
        topicId: topic!.id,
        limit: postsPerPage,
        offset: (currentPage - 1) * postsPerPage
      });

      setPosts(data);
    } catch (err: any) {
      setError(err.message || 'Failed to add reaction');
    }
  };

  const handleMarkAsSolution = async (postId: string) => {
    if (!user || !topic) return;

    try {
      await markAsSolution({
        postId,
        topicId: topic.id,
        isSolution: true
      });

      // Reload posts
      const { data } = await getPostsByTopic({
        topicId: topic.id,
        limit: postsPerPage,
        offset: (currentPage - 1) * postsPerPage
      });

      setPosts(data);
    } catch (err: any) {
      setError(err.message || 'Failed to mark as solution');
    }
  };

  const handleReplyTo = (post: ForumPost) => {
    setReplyToPost(post);
    setReplyContent(`@${post.author?.username} `);

    // Scroll to reply form
    if (replyFormRef.current) {
      replyFormRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const renderPost = (post: ForumPost, isReply = false, index = 0) => {
    const userHasReacted = (reactionType: string) => {
      return post.reactions?.some(r => r.user_id === user?.id && r.reaction_type === reactionType);
    };

    const getReactionCount = (reactionType: string) => {
      return post.reactions?.filter(r => r.reaction_type === reactionType).length || 0;
    };

    // Map reaction types to icons
    const reactionIcons = {
      like: <FaRegThumbsUp className="mr-1" />,
      helpful: <FaRegHandPeace className="mr-1" />,
      insightful: <FaRegLightbulbOutline className="mr-1" />
    };

    return (
      <Motion.div
        key={post.id}
        id={`post-${post.id}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
        className={`mb-4 ${isReply ? 'ml-8 mt-4' : ''}`}
      >
        <div className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden ${post.is_solution ? 'border-l-4 border-l-green-500' : ''}`}>
          <div className="p-5">
            {/* Moderation Tools for Post */}
            {user && (user.role === 'admin' || user.role === 'moderator') && !isReply && (
              <ForumModeration
                contentType="post"
                contentId={post.id}
                topicId={post.topic_id}
                currentContent={post.content}
                onSuccess={() => {
                  // Reload the posts
                  const loadPosts = async () => {
                    try {
                      const { data } = await getPostsByTopic({
                        topicId: topic!.id,
                        limit: postsPerPage,
                        offset: (currentPage - 1) * postsPerPage
                      });
                      setPosts(data);
                    } catch (err) {
                      console.error('Error reloading posts:', err);
                    }
                  };
                  loadPosts();
                }}
              />
            )}

            <div className="flex">
              <div className="flex-shrink-0 mr-4">
                {post.author?.avatar_url ? (
                  <img
                    src={post.author.avatar_url}
                    alt={post.author.username}
                    className="w-12 h-12 rounded-full border-2 border-gray-100"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-nature-green/10 flex items-center justify-center border-2 border-gray-100">
                    <span className="text-nature-green font-medium text-lg">
                      {post.author?.username?.charAt(0).toUpperCase() || '?'}
                    </span>
                  </div>
                )}

                <div className="mt-2 flex flex-col items-center space-y-1">
                  {post.author?.is_verified_expert && (
                    <VerificationBadge isVerified={true} size="sm" />
                  )}
                  {post.author?.level && post.author.level > 1 && (
                    <UserLevelDisplay level={post.author.level} size="sm" />
                  )}
                </div>
              </div>

              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <Link
                    href={`/profile/${post.author?.username}`}
                    className="font-medium text-nature-green hover:underline"
                  >
                    {post.author?.full_name || post.author?.username}
                  </Link>

                  <span className="mx-2 text-gray-400">•</span>
                  <Tooltip content={format(new Date(post.created_at), 'PPpp')}>
                    <span className="text-sm text-gray-500 flex items-center">
                      <FaRegClock className="mr-1 text-gray-400" />
                      {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
                    </span>
                  </Tooltip>

                  {post.is_solution && (
                    <span className="ml-2 px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full flex items-center">
                      <HiCheck className="h-3 w-3 mr-1" />
                      Solution
                    </span>
                  )}
                </div>

                <div className="prose prose-sm max-w-none mb-6 text-gray-700">
                  <div className="whitespace-pre-line">{post.content}</div>
                </div>

                <div className="flex flex-wrap items-center gap-2 pt-3 border-t border-gray-100">
                  {/* Reaction buttons */}
                  <div className="flex space-x-2">
                    {Object.entries(REACTION_TYPES).map(([type, { name }]) => (
                      <Tooltip key={type} content={name}>
                        <button
                          onClick={() => handleReaction(post.id, type as any)}
                          className={`px-2 py-1 text-xs rounded-md flex items-center transition-colors ${
                            userHasReacted(type)
                              ? 'bg-nature-green text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                          disabled={isSubmitting}
                          aria-label={`${name} this post`}
                        >
                          {reactionIcons[type as keyof typeof reactionIcons]}
                          <span className="font-medium">
                            {getReactionCount(type) > 0 ? getReactionCount(type) : name}
                          </span>
                        </button>
                      </Tooltip>
                    ))}
                  </div>

                  {/* Reply button */}
                  <button
                    onClick={() => handleReplyTo(post)}
                    className="text-xs text-gray-600 hover:text-nature-green flex items-center transition-colors"
                    disabled={isSubmitting || topic?.is_locked}
                  >
                    <FaReply className="mr-1" />
                    Reply
                  </button>

                  {/* Mark as solution button (only for topic author or moderators/admins) */}
                  {!isReply && !post.is_solution && user && topic &&
                   (user.id === topic.author_id || user.role === 'moderator' || user.role === 'admin') && (
                    <button
                      onClick={() => handleMarkAsSolution(post.id)}
                      className="text-xs text-gray-600 hover:text-green-600 flex items-center transition-colors"
                      disabled={isSubmitting}
                    >
                      <HiCheck className="mr-1" />
                      Mark as Solution
                    </button>
                  )}

                  {/* Report button */}
                  {user && user.id !== post.author_id && user.role !== 'admin' && user.role !== 'moderator' && (
                    <button
                      onClick={() => setReportContent({type: 'post', id: post.id})}
                      className="text-xs text-gray-500 hover:text-red-600 flex items-center transition-colors ml-auto"
                      disabled={isSubmitting}
                    >
                      <FaRegFlag className="mr-1" />
                      Report
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Render replies */}
        {post.replies && post.replies.length > 0 && (
          <div className="pl-4 border-l-2 border-gray-200 ml-6 mt-2">
            {post.replies.map((reply, replyIndex) => renderPost(reply, true, replyIndex))}
          </div>
        )}
      </Motion.div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <Link href="/forums" className="text-nature-green hover:underline mr-2">
              Forums
            </Link>
            <span className="text-gray-500 mx-2">&gt;</span>
            <Link href={`/forums/${categorySlug}`} className="text-nature-green hover:underline mr-2">
              {topic?.category?.name || categorySlug}
            </Link>
            <span className="text-gray-500 mx-2">&gt;</span>
            <span className="text-gray-700">{topic?.title || 'Loading...'}</span>
          </div>

          <div className="flex items-center mb-2">
            <h1 className="text-2xl font-bold">{topic?.title || 'Loading...'}</h1>
            {topic?.is_pinned && (
              <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full flex items-center">
                <FaThumbtack className="h-3 w-3 mr-1" />
                Pinned
              </span>
            )}
            {topic?.is_locked && (
              <span className="ml-2 px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded-full flex items-center">
                <HiLockClosed className="h-3 w-3 mr-1" />
                Locked
              </span>
            )}
          </div>

          {/* Moderation Tools for Topic */}
          {user && (user.role === 'admin' || user.role === 'moderator') && topic && (
            <ForumModeration
              contentType="topic"
              contentId={topic.id}
              isLocked={topic.is_locked}
              isPinned={topic.is_pinned}
              categoryId={topic.category_id}
              onSuccess={() => {
                // Reload the topic and posts
                window.location.reload();
              }}
            />
          )}

          <div className="flex items-center text-sm text-gray-500">
            <span className="mr-4">
              Started by{' '}
              {topic?.author ? (
                <Link
                  href={`/profile/${topic.author.username}`}
                  className="text-nature-green hover:underline"
                >
                  {topic.author.full_name || topic.author.username}
                </Link>
              ) : (
                'Unknown'
              )}
            </span>
            <span className="mr-4">
              {topic ? formatDistanceToNow(new Date(topic.created_at), { addSuffix: true }) : ''}
            </span>
            <span className="mr-4">
              <span className="font-medium">{totalPosts}</span> replies
            </span>
            <span>
              <span className="font-medium">{topic?.view_count || 0}</span> views
            </span>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="bg-white rounded-xl shadow-md p-6 text-center">
            <p className="text-gray-500">Loading topic...</p>
          </div>
        ) : (
          <>
            {/* Posts List */}
            <div className="mb-8">
              {posts.map((post, index) => renderPost(post, false, index))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mb-8">
                <nav className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="p-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    aria-label="Previous page"
                  >
                    <HiChevronLeft className="h-5 w-5" />
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`w-10 h-10 rounded-md flex items-center justify-center text-sm font-medium ${
                            currentPage === pageNum
                              ? 'bg-nature-green text-white'
                              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="p-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    aria-label="Next page"
                  >
                    <HiChevronRight className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            )}

            {/* Reply Form */}
            <Motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              ref={replyFormRef}
            >
              {!topic?.is_locked ? (
                <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                  {!user ? (
                    <div className="text-center py-4">
                      <FaRegUser className="mx-auto text-4xl text-gray-300 mb-4" />
                      <p className="text-gray-600 mb-6">
                        You need to be logged in to reply to this topic.
                      </p>
                      <Link
                        href={`/auth/signin?redirect=${encodeURIComponent(window.location.pathname)}`}
                        className="px-5 py-2.5 bg-nature-green text-white rounded-lg hover:bg-nature-green-dark transition-colors inline-flex items-center"
                      >
                        <FaRegUser className="mr-2" />
                        Sign In to Reply
                      </Link>
                    </div>
                  ) : (
                    <>
                      <h2 className="text-xl font-semibold mb-4 flex items-center">
                        {replyToPost ? (
                          <>
                            <FaReply className="mr-2 text-nature-green" />
                            Reply to {replyToPost.author?.username}
                          </>
                        ) : (
                          <>
                            <HiOutlinePencilSquare className="mr-2 text-nature-green" />
                            Post a Reply
                          </>
                        )}
                      </h2>

                      {replyToPost && (
                        <div className="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <div className="flex items-center mb-2">
                            <span className="font-medium text-nature-green">{replyToPost.author?.username}</span>
                            <span className="mx-2 text-gray-400">•</span>
                            <span className="text-sm text-gray-500 flex items-center">
                              <FaRegClock className="mr-1 text-gray-400" />
                              {formatDistanceToNow(new Date(replyToPost.created_at), { addSuffix: true })}
                            </span>
                            <button
                              type="button"
                              onClick={() => {
                                setReplyToPost(null);
                                setReplyContent('');
                              }}
                              className="ml-auto text-sm text-gray-500 hover:text-gray-700 flex items-center"
                              aria-label="Cancel reply"
                            >
                              <HiXMark className="mr-1" />
                              Cancel
                            </button>
                          </div>
                          <div className="text-sm text-gray-600 line-clamp-2">
                            {replyToPost.content}
                          </div>
                        </div>
                      )}

                      <form onSubmit={handleReplySubmit} className="space-y-4">
                        <div>
                          <textarea
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent transition-colors"
                            rows={6}
                            placeholder="Write your reply here..."
                            required
                          />
                        </div>

                        <div className="flex justify-end">
                          <button
                            type="submit"
                            disabled={isSubmitting}
                            className="px-5 py-2.5 bg-nature-green text-white rounded-lg hover:bg-nature-green-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                          >
                            <HiPaperAirplane className="mr-2 h-4 w-4" />
                            {isSubmitting ? 'Posting...' : 'Post Reply'}
                          </button>
                        </div>
                      </form>
                    </>
                  )}
                </div>
              ) : (
                <div className="bg-white rounded-xl shadow-md p-6 text-center border border-yellow-200 bg-yellow-50">
                  <div className="flex items-center justify-center mb-2">
                    <HiLockClosed className="h-6 w-6 text-yellow-600 mr-2" />
                    <h2 className="text-lg font-semibold text-yellow-800">Topic Locked</h2>
                  </div>
                  <p className="text-yellow-700">
                    This topic has been locked by a moderator and no new replies can be posted.
                  </p>
                </div>
              )}
            </Motion.div>
          </>
        )}
      </div>

      {/* Report Modal */}
      <AnimatePresence>
        {reportContent && (
          <ReportContentModal
            contentType={reportContent.type}
            contentId={reportContent.id}
            isOpen={!!reportContent}
            onClose={() => setReportContent(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
