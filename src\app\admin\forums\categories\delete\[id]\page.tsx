'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { deleteForumCategory } from '@/lib/category-management';
import { FaArrowLeft, FaExclamationTriangle, FaTrash, FaTimes } from 'react-icons/fa';

export default function DeleteCategoryPage({ params }) {
  const router = useRouter();
  const { id } = params;
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [category, setCategory] = useState(null);
  const [otherCategories, setOtherCategories] = useState([]);
  const [moveTopicsTo, setMoveTopicsTo] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || profile.role !== 'admin') {
        // Redirect to unauthorized page if not admin
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Load category data and other categories
      await Promise.all([
        loadCategory(),
        loadOtherCategories()
      ]);
    }

    checkAuth();
  }, [router, user, loading, id]);

  async function loadCategory() {
    try {
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('forum_categories')
        .select(`
          *,
          topics:forum_topics(count)
        `)
        .eq('id', id)
        .single();
      
      if (error) {
        throw error;
      }
      
      if (!data) {
        throw new Error('Category not found');
      }
      
      // Count the number of topics
      const topicCount = data.topics?.length || 0;
      
      setCategory({
        ...data,
        topicCount
      });
    } catch (err) {
      console.error('Error loading category:', err);
      setError(err.message || 'Failed to load category');
    }
  }

  async function loadOtherCategories() {
    try {
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('forum_categories')
        .select('id, name, slug')
        .neq('id', id)
        .order('name');
      
      if (error) {
        throw error;
      }
      
      setOtherCategories(data || []);
      
      // Set default move target to first category if available
      if (data && data.length > 0) {
        setMoveTopicsTo(data[0].id);
      }
    } catch (err) {
      console.error('Error loading other categories:', err);
      // Don't set error here, as it's not critical
    } finally {
      setIsLoading(false);
    }
  }

  async function handleDelete() {
    try {
      setIsDeleting(true);
      setError(null);
      
      await deleteForumCategory({
        categoryId: id,
        deletedBy: user.id,
        moveTopicsTo: moveTopicsTo || undefined
      });
      
      // Redirect to category management page
      router.push('/admin/forums/categories');
    } catch (err) {
      console.error('Error deleting category:', err);
      setError(err.message || 'Failed to delete category');
      setIsDeleting(false);
    }
  }

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading...</span>
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <button
            onClick={() => router.push('/admin/forums/categories')}
            className="mr-4 text-nature-green hover:underline flex items-center"
          >
            <FaArrowLeft className="mr-1" />
            Back to Categories
          </button>
          <h1 className="text-3xl font-bold">Delete Category</h1>
        </div>

        <div className="bg-red-50 text-red-700 p-6 rounded-lg shadow-md">
          <p className="font-bold text-xl mb-2">Error</p>
          <p>{error || 'Category not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 sm:py-8">
      <div className="flex flex-col sm:flex-row sm:items-center mb-4 sm:mb-6 space-y-2 sm:space-y-0">
        <button
          onClick={() => router.push('/admin/forums/categories')}
          className="self-start sm:mr-4 text-nature-green hover:underline flex items-center"
        >
          <FaArrowLeft className="mr-1" />
          Back to Categories
        </button>
        <h1 className="text-2xl sm:text-3xl font-bold">Delete Category</h1>
      </div>

      {error && (
        <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
          <p className="font-medium text-sm sm:text-base">Error</p>
          <p className="text-sm sm:text-base">{error}</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-4 sm:mb-6">
        <div className="flex items-start sm:items-center mb-4 text-red-600">
          <FaExclamationTriangle className="text-xl sm:text-2xl mr-2 flex-shrink-0 mt-0.5 sm:mt-0" />
          <h2 className="text-lg sm:text-xl font-bold">Warning: This action cannot be undone</h2>
        </div>

        <p className="mb-4">
          You are about to delete the category <strong>{category.name}</strong>.
        </p>

        {category.topicCount > 0 ? (
          <div className="mb-6">
            <p className="mb-2">
              This category contains <strong>{category.topicCount}</strong> topics.
              What would you like to do with these topics?
            </p>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <FaExclamationTriangle className="h-5 w-5 text-yellow-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Choose carefully
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      If you delete the topics, all posts within those topics will also be permanently deleted.
                      This cannot be undone.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="move-topics"
                  name="topic-action"
                  checked={!!moveTopicsTo}
                  onChange={() => setMoveTopicsTo(otherCategories[0]?.id || '')}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                />
                <label htmlFor="move-topics" className="ml-2 block text-sm text-gray-700">
                  Move topics to another category
                </label>
              </div>

              {moveTopicsTo && (
                <div className="ml-6">
                  <label htmlFor="moveTopicsTo" className="block text-sm font-medium text-gray-700 mb-1">
                    Select destination category
                  </label>
                  <select
                    id="moveTopicsTo"
                    value={moveTopicsTo}
                    onChange={(e) => setMoveTopicsTo(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  >
                    {otherCategories.map(cat => (
                      <option key={cat.id} value={cat.id}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div className="flex items-center">
                <input
                  type="radio"
                  id="delete-topics"
                  name="topic-action"
                  checked={!moveTopicsTo}
                  onChange={() => setMoveTopicsTo('')}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <label htmlFor="delete-topics" className="ml-2 block text-sm text-gray-700">
                  Delete all topics and their posts (cannot be undone)
                </label>
              </div>
            </div>
          </div>
        ) : (
          <p className="mb-6">
            This category does not contain any topics.
          </p>
        )}

        <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2">
          <button
            type="button"
            onClick={() => router.push('/admin/forums/categories')}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center justify-center"
            disabled={isDeleting}
          >
            <FaTimes className="mr-2" />
            Cancel
          </button>
          <button
            type="button"
            onClick={handleDelete}
            disabled={isDeleting || (category.topicCount > 0 && !moveTopicsTo && !confirm('Are you sure you want to delete all topics? This cannot be undone.'))}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center justify-center"
          >
            <FaTrash className="mr-2" />
            {isDeleting ? 'Deleting...' : 'Delete Category'}
          </button>
        </div>
      </div>
    </div>
  );
}
