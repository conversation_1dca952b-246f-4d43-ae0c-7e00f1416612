'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { banUser } from '@/lib/forums';
import { FaArrowLeft, FaSave, FaTimes, FaSearch, FaUser } from 'react-icons/fa';
import { format, addDays, addWeeks, addMonths } from 'date-fns';

export default function BanUserPage() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [formData, setFormData] = useState({
    banType: 'full',
    reason: '',
    duration: 'permanent',
    customDuration: '',
    customDurationType: 'days'
  });

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        // Redirect to unauthorized page if not admin/moderator
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);
    }

    checkAuth();
  }, [router, user, loading]);

  async function searchUsers() {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }
    
    try {
      setIsSearching(true);
      
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, full_name, email, avatar_url, role')
        .or(`username.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%,full_name.ilike.%${searchQuery}%`)
        .limit(10);
      
      if (error) {
        throw error;
      }
      
      // Filter out admins and moderators
      const filteredResults = data.filter(profile => 
        profile.role !== 'admin' && profile.role !== 'moderator'
      );
      
      setSearchResults(filteredResults);
    } catch (err) {
      console.error('Error searching users:', err);
      setError('Failed to search users');
    } finally {
      setIsSearching(false);
    }
  }

  function selectUser(user) {
    setSelectedUser(user);
    setSearchResults([]);
    setSearchQuery('');
  }

  function handleChange(e) {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  }

  function calculateExpiryDate() {
    if (formData.duration === 'permanent') {
      return null;
    }
    
    const now = new Date();
    
    switch (formData.duration) {
      case '1day':
        return addDays(now, 1);
      case '3days':
        return addDays(now, 3);
      case '1week':
        return addWeeks(now, 1);
      case '2weeks':
        return addWeeks(now, 2);
      case '1month':
        return addMonths(now, 1);
      case '3months':
        return addMonths(now, 3);
      case '6months':
        return addMonths(now, 6);
      case '1year':
        return addMonths(now, 12);
      case 'custom':
        const duration = parseInt(formData.customDuration);
        if (isNaN(duration) || duration <= 0) {
          return null;
        }
        
        switch (formData.customDurationType) {
          case 'days':
            return addDays(now, duration);
          case 'weeks':
            return addWeeks(now, duration);
          case 'months':
            return addMonths(now, duration);
          default:
            return null;
        }
      default:
        return null;
    }
  }

  async function handleSubmit(e) {
    e.preventDefault();
    
    if (!selectedUser) {
      setError('Please select a user to ban');
      return;
    }
    
    if (!formData.reason.trim()) {
      setError('Please provide a reason for the ban');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      const expiresAt = calculateExpiryDate();
      
      const result = await banUser({
        userId: selectedUser.id,
        moderatorId: user.id,
        reason: formData.reason,
        expiresAt: expiresAt ? expiresAt.toISOString() : null,
        banType: formData.banType
      });
      
      if (result.success) {
        // Redirect to bans page
        router.push('/admin/users/bans');
      } else {
        setError(result.message || 'Failed to ban user');
      }
    } catch (err) {
      console.error('Error banning user:', err);
      setError(err.message || 'Failed to ban user');
    } finally {
      setIsLoading(false);
    }
  }

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.push('/admin/users/bans')}
          className="mr-4 text-nature-green hover:underline flex items-center"
        >
          <FaArrowLeft className="mr-1" />
          Back to Bans
        </button>
        <h1 className="text-3xl font-bold">Ban User</h1>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Select User</h2>
        
        {selectedUser ? (
          <div className="mb-4 p-4 border border-gray-200 rounded-md">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-12 w-12">
                  {selectedUser.avatar_url ? (
                    <img
                      className="h-12 w-12 rounded-full"
                      src={selectedUser.avatar_url}
                      alt={selectedUser.username}
                    />
                  ) : (
                    <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                      <FaUser className="text-gray-500" />
                    </div>
                  )}
                </div>
                <div className="ml-4">
                  <div className="text-lg font-medium text-gray-900">
                    {selectedUser.username}
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedUser.email}
                  </div>
                  {selectedUser.full_name && (
                    <div className="text-sm text-gray-500">
                      {selectedUser.full_name}
                    </div>
                  )}
                </div>
              </div>
              <button
                onClick={() => setSelectedUser(null)}
                className="text-red-600 hover:text-red-800"
              >
                <FaTimes />
              </button>
            </div>
          </div>
        ) : (
          <div className="mb-6">
            <div className="flex items-center">
              <div className="relative flex-grow">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by username, email, or name"
                  className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                />
                <FaSearch className="absolute left-3 top-3 text-gray-400" />
              </div>
              <button
                onClick={searchUsers}
                disabled={isSearching || !searchQuery.trim()}
                className="ml-2 px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
              >
                {isSearching ? 'Searching...' : 'Search'}
              </button>
            </div>
            
            {searchResults.length > 0 && (
              <div className="mt-2 border border-gray-200 rounded-md overflow-hidden">
                <ul className="divide-y divide-gray-200">
                  {searchResults.map(result => (
                    <li
                      key={result.id}
                      className="p-3 hover:bg-gray-50 cursor-pointer"
                      onClick={() => selectUser(result)}
                    >
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          {result.avatar_url ? (
                            <img
                              className="h-8 w-8 rounded-full"
                              src={result.avatar_url}
                              alt={result.username}
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                              <FaUser className="text-gray-500" />
                            </div>
                          )}
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {result.username}
                          </div>
                          <div className="text-xs text-gray-500">
                            {result.email}
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {searchQuery && searchResults.length === 0 && !isSearching && (
              <div className="mt-2 text-gray-500 text-sm">
                No users found matching your search.
              </div>
            )}
          </div>
        )}
      </div>

      {selectedUser && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Ban Details</h2>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="banType" className="block text-sm font-medium text-gray-700 mb-1">
                Ban Type *
              </label>
              <select
                id="banType"
                name="banType"
                value={formData.banType}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              >
                <option value="full">Full Ban (No Access)</option>
                <option value="post_only">Post Only (Can Read, Cannot Post)</option>
                <option value="read_only">Read Only (Can Browse, Cannot Interact)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                {formData.banType === 'full' 
                  ? 'User will not be able to access the site at all.'
                  : formData.banType === 'post_only'
                    ? 'User will be able to read content but not create new posts or comments.'
                    : 'User will be able to browse the site but not interact with content.'}
              </p>
            </div>

            <div className="mb-4">
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Ban *
              </label>
              <textarea
                id="reason"
                name="reason"
                value={formData.reason}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                This reason will be visible to the user and in moderation logs.
              </p>
            </div>

            <div className="mb-6">
              <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
                Ban Duration *
              </label>
              <select
                id="duration"
                name="duration"
                value={formData.duration}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              >
                <option value="permanent">Permanent</option>
                <option value="1day">1 Day</option>
                <option value="3days">3 Days</option>
                <option value="1week">1 Week</option>
                <option value="2weeks">2 Weeks</option>
                <option value="1month">1 Month</option>
                <option value="3months">3 Months</option>
                <option value="6months">6 Months</option>
                <option value="1year">1 Year</option>
                <option value="custom">Custom Duration</option>
              </select>
              
              {formData.duration === 'custom' && (
                <div className="mt-2 flex items-center">
                  <input
                    type="number"
                    name="customDuration"
                    value={formData.customDuration}
                    onChange={handleChange}
                    min="1"
                    className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                    required
                  />
                  <select
                    name="customDurationType"
                    value={formData.customDurationType}
                    onChange={handleChange}
                    className="ml-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  >
                    <option value="days">Days</option>
                    <option value="weeks">Weeks</option>
                    <option value="months">Months</option>
                  </select>
                </div>
              )}
              
              {formData.duration !== 'permanent' && (
                <p className="text-xs text-gray-500 mt-1">
                  Ban will expire on: {format(calculateExpiryDate() || new Date(), 'MMMM d, yyyy')}
                </p>
              )}
            </div>

            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={() => router.push('/admin/users/bans')}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center"
              >
                <FaTimes className="mr-2" />
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
              >
                <FaSave className="mr-2" />
                {isLoading ? 'Processing...' : 'Ban User'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
