'use client';

import React, { useState, useEffect } from 'react';
import { getArticleBySlug, getRelatedArticles } from '@/lib/articles';
import { formatDate } from '@/lib/utils';
import TableOfContents from '@/components/TableOfContents';
import BookmarkButton from '@/components/BookmarkButton';
import SocialShareButtons from '@/components/SocialShareButtons';
import { useInView } from 'react-intersection-observer';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkHtml from 'remark-html';

const useUser = () => {
  // Replace this with your actual user authentication logic
  const [user, setUser] = useState<any>(null);
  return user;
};

export default function ArticlePage({ params }: { params: { slug: string } }) {
  const [article, setArticle] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<any[]>([]);
  const [relatedLoading, setRelatedLoading] = useState(true);
  const [readingProgress, setReadingProgress] = useState(0);

  // Reference for the article content section
  const { ref: articleRef, inView: articleInView } = useInView({
    threshold: 0.1,
    triggerOnce: false
  });

  // Track reading progress
  useEffect(() => {
    const handleScroll = () => {
      if (!articleInView) return;

      const articleElement = document.querySelector('.article-content');
      if (!articleElement) return;

      const windowHeight = window.innerHeight;
      const articleHeight = articleElement.getBoundingClientRect().height;
      const scrollTop = window.scrollY;
      const scrollBottom = scrollTop + windowHeight;
      const articleTop = articleElement.getBoundingClientRect().top + scrollTop;
      const articleBottom = articleTop + articleHeight;

      // Calculate reading progress as percentage
      const progress = Math.min(100, Math.max(0,
        ((scrollTop - articleTop + windowHeight) / (articleHeight + windowHeight)) * 100
      ));

      setReadingProgress(progress);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [articleInView]);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getArticleBySlug(params.slug);
        if (data) {
          setArticle(data);
        } else {
          setError('Article not found');
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch article');
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [params.slug]);

  useEffect(() => {
    const fetchRelatedArticles = async () => {
      if (article) {
        try {
          setRelatedLoading(true);
          const data = await getRelatedArticles(article.id, article.category_id);
          if (data) {
            setRelatedArticles(data);
          } else {
            setRelatedArticles([]);
          }
        } catch (err: any) {
          console.error('Error fetching related articles:', err);
          setRelatedArticles([]);
        } finally {
          setRelatedLoading(false);
        }
      }
    };

    if (article) {
      fetchRelatedArticles();
    }
  }, [article]);

  const user = useUser();

  if (loading) {
    return <div className="container mx-auto px-4 py-8">Loading article...</div>;
  }

  if (error || !article) {
    return <div className="container mx-auto px-4 py-8 text-red-500">{error || 'Article not found'}</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8 relative">
      {/* Reading Progress Bar */}
      <div
        className="fixed top-0 left-0 h-1 bg-nature-green z-50 transition-all duration-300"
        style={{ width: `${readingProgress}%` }}
      ></div>

      <div className="max-w-5xl mx-auto">
        <div className="lg:flex lg:gap-8">
          {/* Main Content Column */}
          <div className="lg:flex-grow">
            {/* Breadcrumb */}
            <div className="text-sm text-gray-500 mb-6">
              <a href="/" className="hover:text-nature-green">Home</a> &gt;{" "}
              <a href="/wiki" className="hover:text-nature-green">Wiki</a> &gt;{" "}
              {article.category ? (
                <a href={`/categories/${article.category.slug}`} className="hover:text-nature-green">
                  {article.category.name}
                </a>
              ) : (
                <span className="text-gray-500">Uncategorized</span>
              )}
              &gt; <span className="text-gray-700">{article.title}</span>
            </div>

            {/* Article Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold mb-3">{article.title}</h1>
              <div className="flex flex-wrap gap-2 mb-4">
                {article.tags &&
                  article.tags.map((tag: { tags: { slug: string; name: string } }, index: number) => (
                    <a
                      key={index}
                      href={`/tags/${tag.tags.slug}`}
                      className="bg-gray-100 text-gray-600 px-2 py-1 rounded-md text-sm hover:bg-gray-200"
                    >
                      #{tag.tags.name}
                    </a>
                  ))}
              </div>
              <div className="text-sm text-gray-500">
                <span>
                  Created by{" "}
                  <a
                    href={`/profile/${article.author.username}`}
                    className="text-nature-green hover:underline"
                  >
                    {article.author.full_name || article.author.username}
                  </a> on {article.created_at}
                </span>
                <span className="mx-2">•</span>
                <span>Last updated on {formatDate(article.updated_at)}</span>
                <span className="mx-2">•</span>
                <span>{article.views} views</span>
              </div>
            </div>

            {/* Article Content */}
            <div ref={articleRef} className="prose prose-lg max-w-none mb-8 article-content">
              {article.content && (
                <div
                  dangerouslySetInnerHTML={{
                    __html: unified()
                      .use(remarkParse)
                      .use(remarkHtml)
                      .processSync(article.content).toString()
                  }}
                />
              )}
            </div>
          </div>

          {/* Table of Contents Sidebar */}
          <div className="hidden lg:block lg:w-72 flex-shrink-0">
            <TableOfContents />
          </div>
        </div>

        {/* Article Footer */}
        <div className="border-t border-gray-200 pt-6 mt-8 lg:col-span-2">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Contributors</h3>
              <div className="flex gap-2">
                {article.contributors && article.contributors.map((contributor: string, index: number) => (
                  <a key={index} href={`/profile/${contributor}`} className="text-nature-green hover:underline">
                    {contributor}
                  </a>
                ))}
              </div>
            </div>
            <div className="flex gap-3">
              <BookmarkButton
                articleId={article.id}
                showText={true}
              />
              <SocialShareButtons
                url={`/wiki/${params.slug}`}
                title={article.title}
                description={article.excerpt || ''}
                articleId={article.id}
                showText={true}
              />
              {(user?.id === article.author.id || user?.role === 'admin') && (
                <a href={`/wiki/${params.slug}/edit`} className="flex items-center gap-1 text-gray-500 hover:text-nature-green dark:hover:text-nature-green">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span>Edit</span>
                </a>
              )}
            </div>
          </div>

          {/* Related Articles */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold mb-4">Related Articles</h3>
            {relatedLoading ? (
              <p>Loading related articles...</p>
            ) : relatedArticles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {relatedArticles.map((relatedArticle) => (
                  <a
                    key={relatedArticle.id}
                    href={`/wiki/${relatedArticle.slug}`}
                    className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <h4 className="font-medium mb-1">{relatedArticle.title}</h4>
                    <p className="text-sm text-gray-600">
                      {relatedArticle.excerpt || 'Read more about this topic.'}
                    </p>
                  </a>
                ))}
              </div>
            ) : (
              <p>No related articles found.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
