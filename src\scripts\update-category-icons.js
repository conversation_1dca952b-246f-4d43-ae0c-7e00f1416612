// This script updates the categories with icon names
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local if available
try {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const match = line.match(/^([^=]+)=(.*)$/);
      if (match) {
        process.env[match[1]] = match[2];
      }
    });
  }
} catch (error) {
  console.warn('Could not load .env.local file:', error);
}

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Icon mapping for categories
const iconMap = {
  'herbal-remedies': 'leaf',
  'mind-body-practices': 'sparkles',
  'nutritional-healing': 'cake',
  'medicinal-herbs': 'beaker',
  'aromatherapy': 'fire',
  'traditional-medicine': 'academic-cap',
  'ayurveda': 'sun',
  'chinese-medicine': 'moon',
  'homeopathy': 'droplet',
  'naturopathy': 'globe',
  'foraging': 'map',
  'gardening': 'home',
  'herbalism': 'collection',
  'nutrition': 'shopping-cart',
  'essential-oils': 'color-swatch',
  'holistic-health': 'heart',
  'sustainable-living': 'lightning-bolt'
};

async function updateCategoryIcons() {
  try {
    // Get all categories
    const { data: categories, error } = await supabase
      .from('categories')
      .select('id, slug');
    
    if (error) {
      console.error('Error fetching categories:', error);
      return;
    }
    
    console.log(`Found ${categories.length} categories to update`);
    
    // Update each category with its icon
    for (const category of categories) {
      const icon = iconMap[category.slug] || 'bookmark';
      
      const { error: updateError } = await supabase
        .from('categories')
        .update({ icon })
        .eq('id', category.id);
      
      if (updateError) {
        console.error(`Error updating category ${category.slug}:`, updateError);
      } else {
        console.log(`Updated category ${category.slug} with icon: ${icon}`);
      }
    }
    
    console.log('Category icons updated successfully!');
  } catch (error) {
    console.error('Unexpected error updating category icons:', error);
  }
}

// Run the update function
updateCategoryIcons()
  .then(() => {
    console.log('Update process completed');
  })
  .catch(error => {
    console.error('Update process failed:', error);
  });
