'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { checkRateLimit, formatRateLimitMessage } from '@/lib/rate-limiting';
import { useAuth } from '@/components/AuthProvider';
import SearchAutocomplete from '@/components/SearchAutocomplete';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/utils';

type SearchResult = {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content?: string;
  created_at?: string;
  author?: {
    username: string;
    full_name?: string;
    avatar_url?: string;
    is_verified_expert?: boolean;
    level?: number;
  };
  type: 'article' | 'category' | 'tag' | 'forum_topic' | 'forum_post';
  topic_id?: string;
  category_id?: string;
  category?: {
    name: string;
    slug: string;
  };
};

// Main component that uses the wrapper
export default function SearchPage() {
  return (
    <Suspense fallback={<div>Loading search...</div>}>
      <SearchContent />
    </Suspense>
  );
}

// Content component that uses useSearchParams
const SearchContent: React.FC = () => {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const { user } = useAuth();

  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<'all' | 'articles' | 'categories' | 'tags' | 'forums'>('all');
  const [totalResults, setTotalResults] = useState(0);

  useEffect(() => {
    if (!query) return;

    const fetchSearchResults = async () => {
      setLoading(true);
      setError(null);

      try {
        // Check rate limit if user is logged in
        if (user) {
          const rateLimitCheck = await checkRateLimit(user.id, 'search');
          if (rateLimitCheck.isLimited) {
            throw new Error(formatRateLimitMessage('search', rateLimitCheck.resetTime));
          }
        }

        // Use the API route to search
        try {
          const response = await fetch(`/api/search/?q=${encodeURIComponent(query)}`);
          if (!response.ok) {
            console.error(`Search API returned ${response.status}: ${response.statusText}`);
            throw new Error(`Search API returned ${response.status}: ${response.statusText}`);
          }
          
          const searchResults = await response.json();
          console.log('Search results:', searchResults);
          
          // Debug output to the page
          if (searchResults.error) {
            console.error(`API error: ${searchResults.error}`);
            throw new Error(`API error: ${searchResults.error}`);
          }
          
          if (!Array.isArray(searchResults)) {
            console.error(`Expected array of results, got: ${typeof searchResults}`);
            throw new Error(`Expected array of results, got: ${typeof searchResults}`);
          }
          
          if (searchResults.length === 0) {
            console.log('No search results found');
          }
          
          // Get additional data for articles and forum content if needed
          const enrichedResults = await Promise.all(searchResults.map(async (item: any) => {
            try {
              if (item.type === 'article') {
                // Get author information for articles
                const { data: articleData } = await supabase
                  .from('articles')
                  .select('created_at, user_id')
                  .eq('id', item.id)
                  .single();
                
                let author = null;
                if (articleData?.user_id) {
                  const { data: authorData } = await supabase
                    .from('profiles')
                    .select('username, full_name, avatar_url, is_verified_expert, level')
                    .eq('id', articleData.user_id)
                    .single();
                  
                  author = authorData;
                }
                
                return {
                  ...item,
                  created_at: articleData?.created_at,
                  author
                };
              } else if (item.type === 'forum_topic') {
                // Get author and category information for forum topics
                const { data: topicData } = await supabase
                  .from('forum_topics')
                  .select('created_at, user_id, category_id')
                  .eq('id', item.id)
                  .single();
                
                let author = null;
                if (topicData?.user_id) {
                  const { data: authorData } = await supabase
                    .from('profiles')
                    .select('username, full_name, avatar_url, is_verified_expert, level')
                    .eq('id', topicData.user_id)
                    .single();
                  
                  author = authorData;
                }
                
                let category = null;
                if (topicData?.category_id) {
                  const { data: categoryData } = await supabase
                    .from('forum_categories')
                    .select('name, slug')
                    .eq('id', topicData.category_id)
                    .single();
                  
                  category = categoryData;
                }
                
                return {
                  ...item,
                  created_at: topicData?.created_at,
                  author,
                  category
                };
              } else if (item.type === 'forum_post') {
                // For forum posts, we already have the necessary information from the API
                // But we can get the author information if needed
                const { data: postData } = await supabase
                  .from('forum_posts')
                  .select('created_at, user_id')
                  .eq('id', item.id)
                  .single();
                
                let author = null;
                if (postData?.user_id) {
                  const { data: authorData } = await supabase
                    .from('profiles')
                    .select('username, full_name, avatar_url, is_verified_expert, level')
                    .eq('id', postData.user_id)
                    .single();
                  
                  author = authorData;
                }
                
                return {
                  ...item,
                  created_at: postData?.created_at,
                  author
                };
              }
              
              return item;
            } catch (err) {
              console.error('Error enriching search result:', err);
              return item;
            }
          }));
          
          // Combine results
          const allResults = enrichedResults;
          setResults(allResults);
          setTotalResults(allResults.length);
        } catch (err) {
          console.error('Search error:', err);
          setError('An error occurred while searching. Please try again.');
          setResults([]);
          setTotalResults(0);
        } finally {
          setLoading(false);
        }
      } catch (err) {
        console.error('Search error:', err);
        setError('An error occurred while searching. Please try again.');
        setLoading(false);
      }
    };

    fetchSearchResults();
  }, [query, user]);

  // Filter results based on active filter
  const filteredResults = activeFilter === 'all'
    ? results
    : activeFilter === 'forums'
      ? results.filter(result => result.type === 'forum_topic' || result.type === 'forum_post')
      : results.filter(result => result.type === activeFilter.slice(0, -1));

  // Highlight search terms in text
  const highlightText = (text: string, query: string) => {
    if (!text) return '';

    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Search Results</h1>

      {/* Search bar */}
      <div className="mb-8">
        <SearchAutocomplete />
      </div>

      {/* Filters */}
      <div className="mb-6 border-b border-gray-200">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveFilter('all')}
            className={`py-2 px-4 font-medium ${activeFilter === 'all' ? 'text-nature-green border-b-2 border-nature-green' : 'text-gray-600 hover:text-nature-green'}`}
          >
            All ({totalResults})
          </button>
          <button
            onClick={() => setActiveFilter('articles')}
            className={`py-2 px-4 font-medium ${activeFilter === 'articles' ? 'text-nature-green border-b-2 border-nature-green' : 'text-gray-600 hover:text-nature-green'}`}
          >
            Articles ({results.filter(r => r.type === 'article').length})
          </button>
          <button
            onClick={() => setActiveFilter('categories')}
            className={`py-2 px-4 font-medium ${activeFilter === 'categories' ? 'text-nature-green border-b-2 border-nature-green' : 'text-gray-600 hover:text-nature-green'}`}
          >
            Categories ({results.filter(r => r.type === 'category').length})
          </button>
          <button
            onClick={() => setActiveFilter('tags')}
            className={`py-2 px-4 font-medium ${activeFilter === 'tags' ? 'text-nature-green border-b-2 border-nature-green' : 'text-gray-600 hover:text-nature-green'}`}
          >
            Tags ({results.filter(r => r.type === 'tag').length})
          </button>
          <button
            onClick={() => setActiveFilter('forums')}
            className={`py-2 px-4 font-medium ${activeFilter === 'forums' ? 'text-nature-green border-b-2 border-nature-green' : 'text-gray-600 hover:text-nature-green'}`}
          >
            Forums ({results.filter(r => r.type === 'forum_topic' || r.type === 'forum_post').length})
          </button>
        </div>
      </div>

      {/* Results */}
      <div className="space-y-6">
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green mx-auto"></div>
            <p className="mt-4 text-gray-600">Searching...</p>
          </div>
        ) : error ? (
          <div className="bg-red-50 text-red-700 p-4 rounded-md">
            {error}
          </div>
        ) : filteredResults.length === 0 ? (
          <div className="text-center py-12">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h2 className="text-xl font-semibold mt-4">No results found</h2>
            <p className="text-gray-600 mt-2">
              We couldn't find any matches for "{query}". Please try a different search term.
            </p>
          </div>
        ) : (
          <>
            <p className="text-gray-600 mb-4">
              Found {filteredResults.length} {activeFilter === 'all' ? 'results' : activeFilter} for "{query}"
            </p>

            <div className="divide-y divide-gray-200">
              {filteredResults.map((result) => (
                <div key={`${result.type}-${result.id}`} className="py-6">
                  {result.type === 'article' && (
                    <article>
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <span className="bg-nature-green/10 text-nature-green px-2 py-1 rounded-full text-xs font-medium">
                          Article
                        </span>
                        {result.created_at && (
                          <>
                            <span className="mx-2">•</span>
                            <time dateTime={result.created_at}>{formatDate(result.created_at)}</time>
                          </>
                        )}
                        {result.author && (
                          <>
                            <span className="mx-2">•</span>
                            <span>By {result.author.full_name || result.author.username}</span>
                          </>
                        )}
                      </div>
                      <h2 className="text-xl font-semibold mb-2">
                        <Link href={`/wiki/${result.slug}`} className="text-gray-900 hover:text-nature-green">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(result.title, query) }} />
                        </Link>
                      </h2>
                      {result.excerpt && (
                        <p className="text-gray-700 mb-3">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(result.excerpt, query) }} />
                        </p>
                      )}
                      <Link href={`/wiki/${result.slug}`} className="text-nature-green hover:underline font-medium">
                        Read article
                      </Link>
                    </article>
                  )}

                  {result.type === 'category' && (
                    <div>
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <span className="bg-blue-50 text-blue-600 px-2 py-1 rounded-full text-xs font-medium">
                          Category
                        </span>
                      </div>
                      <h2 className="text-xl font-semibold mb-2">
                        <Link href={`/categories/${result.slug}`} className="text-gray-900 hover:text-blue-600">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(result.title, query) }} />
                        </Link>
                      </h2>
                      {result.excerpt && (
                        <p className="text-gray-700 mb-3">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(result.excerpt, query) }} />
                        </p>
                      )}
                      <Link href={`/categories/${result.slug}`} className="text-blue-600 hover:underline font-medium">
                        Browse category
                      </Link>
                    </div>
                  )}

                  {result.type === 'tag' && (
                    <div>
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <span className="bg-purple-50 text-purple-600 px-2 py-1 rounded-full text-xs font-medium">
                          Tag
                        </span>
                      </div>
                      <h2 className="text-xl font-semibold mb-2">
                        <Link href={`/tags/${result.slug}`} className="text-gray-900 hover:text-purple-600">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(result.title, query) }} />
                        </Link>
                      </h2>
                      <Link href={`/tags/${result.slug}`} className="text-purple-600 hover:underline font-medium">
                        Browse tag
                      </Link>
                    </div>
                  )}

                  {result.type === 'forum_topic' && (
                    <div>
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <span className="bg-orange-50 text-orange-600 px-2 py-1 rounded-full text-xs font-medium">
                          Forum Topic
                        </span>
                        {result.created_at && (
                          <>
                            <span className="mx-2">•</span>
                            <time dateTime={result.created_at}>{formatDate(result.created_at)}</time>
                          </>
                        )}
                        {result.author && (
                          <>
                            <span className="mx-2">•</span>
                            <span>By {result.author.full_name || result.author.username}</span>
                          </>
                        )}
                      </div>
                      <h2 className="text-xl font-semibold mb-2">
                        <Link href={`/forums/${result.category?.slug}/${result.slug}`} className="text-gray-900 hover:text-orange-600">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(result.title, query) }} />
                        </Link>
                      </h2>
                      <Link href={`/forums/${result.category?.slug}/${result.slug}`} className="text-orange-600 hover:underline font-medium">
                        View topic
                      </Link>
                    </div>
                  )}

                  {result.type === 'forum_post' && (
                    <div>
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <span className="bg-amber-50 text-amber-600 px-2 py-1 rounded-full text-xs font-medium">
                          Forum Post
                        </span>
                        {result.created_at && (
                          <>
                            <span className="mx-2">•</span>
                            <time dateTime={result.created_at}>{formatDate(result.created_at)}</time>
                          </>
                        )}
                        {result.author && (
                          <>
                            <span className="mx-2">•</span>
                            <span>By {result.author.full_name || result.author.username}</span>
                          </>
                        )}
                      </div>
                      <h2 className="text-xl font-semibold mb-2">
                        <Link href={`/forums/${result.slug.split('/')[0]}/${result.slug.split('/')[1]}#post-${result.id}`} className="text-gray-900 hover:text-amber-600">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(`Re: ${result.title}`, query) }} />
                        </Link>
                      </h2>
                      {result.content && (
                        <p className="text-gray-700 mb-3">
                          <span dangerouslySetInnerHTML={{ __html: highlightText(result.content.substring(0, 200) + (result.content.length > 200 ? '...' : ''), query) }} />
                        </p>
                      )}
                      <Link href={`/forums/${result.slug.split('/')[0]}/${result.slug.split('/')[1]}#post-${result.id}`} className="text-amber-600 hover:underline font-medium">
                        View post
                      </Link>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
