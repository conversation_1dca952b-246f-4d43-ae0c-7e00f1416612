# Moderation System Database Migrations

This directory contains SQL migrations for setting up the moderation system database tables and policies.

## Migration Files

1. `20240101000001_create_moderation_tables.sql` - Creates the necessary tables for the moderation system
2. `20240101000002_create_moderation_rls_policies.sql` - Sets up Row Level Security policies for the tables
3. `20240101000003_add_sample_moderation_data.sql` - Adds sample data for testing (optional)
4. `20240101000004_update_profiles_for_bans.sql` - Updates the profiles table to add ban-related fields

## How to Apply Migrations

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed, you can apply these migrations with:

```bash
supabase db push
```

### Option 2: Using Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of each migration file
4. Execute them in order (1-4)

## Tables Created

1. `user_bans` - Stores information about banned users
2. `reported_content` - Stores reports of inappropriate content
3. `moderation_actions` - Logs all moderation actions taken by moderators
4. `auto_moderation_logs` - Logs automatic content filtering actions
5. `user_notifications` - Stores notifications for users

## Testing

After applying the migrations, you can test the tables by:

1. Checking if the tables were created correctly in the Table Editor
2. Verifying that the RLS policies are in place
3. Testing the sample data (if you applied the sample data migration)

## Troubleshooting

If you encounter any issues:

1. Check the Supabase logs for error messages
2. Verify that the migrations were applied in the correct order
3. Make sure your Supabase instance has the `uuid-ossp` extension enabled
