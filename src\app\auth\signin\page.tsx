'use client';

import React, { useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Wrapper component for useSearchParams
function SearchParamsWrapper({ children }: { children: (params: ReturnType<typeof useSearchParams>) => React.ReactNode }) {
  const searchParams = useSearchParams();
  return <>{children(searchParams)}</>;
}

// Main component that uses the wrapper
export default function SignInPage() {
  return (
    <Suspense fallback={<div>Loading sign in page...</div>}>
      <SignInContent />
    </Suspense>
  );
}

// Content component that uses useSearchParams
function SignInContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const registered = searchParams.get('registered');
  const unverified = searchParams.get('unverified');

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.email || !formData.password) {
      setError('Email and password are required');
      return;
    }

    try {
      setLoading(true);
      const supabase = createClientComponentClient();

      const { error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password
      });

      if (error) {
        // Check if the error is due to an unverified email
        if (error.message.includes('Email not confirmed')) {
          // Redirect to the sign-in page with the unverified parameter
          router.push('/auth/signin?unverified=true');
          return;
        }

        setError(error.message);
        return;
      }

      // Check if user is admin or moderator and redirect accordingly
      const { data: { user: authUser } } = await supabase.auth.getUser();

      if (!authUser) {
        setError('Failed to get user information');
        return;
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', authUser.id)
        .single();

      if (profile && (profile.role === 'admin' || profile.role === 'moderator')) {
        // Redirect admin/moderator to admin dashboard
        router.push('/admin');
      } else {
        // Redirect regular users to home page
        router.push('/');
      }
      router.refresh();
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <div className="py-8 px-6">
          <h2 className="text-2xl font-bold text-center mb-6">Sign In to NatureHeals.info</h2>
          {registered && (
            <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md border border-green-200">
              Account created successfully! Please check your email to verify your account before signing in.
            </div>
          )}
          {unverified && (
            <div className="mb-4 p-3 bg-yellow-50 text-yellow-700 rounded-md border border-yellow-200">
              Your email has not been verified. Please check your inbox for the verification email.
              <div className="mt-2">
                <a href="/auth/resend-verification" className="text-yellow-700 font-medium underline">
                  Resend verification email
                </a>
              </div>
            </div>
          )}
          <form onSubmit={handleSubmit}>
            {error && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-200">
                {error}
              </div>
            )}
            <div className="mb-4">
              <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div className="mb-6">
              <label htmlFor="password" className="block text-gray-700 font-medium mb-2">
                Password
              </label>
              <input
                type="password"
                id="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
                placeholder="••••••••"
                required
              />
              <div className="flex justify-end mt-1">
                <a href="/auth/forgot-password" className="text-sm text-nature-green hover:underline">
                  Forgot Password?
                </a>
              </div>
            </div>
            <div className="mb-6">
              <button
                type="submit"
                disabled={loading}
                className="w-full bg-nature-green text-white py-2 px-4 rounded-md font-medium hover:bg-nature-green-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {loading ? 'Signing In...' : 'Sign In'}
              </button>
            </div>
          </form>
          <div className="relative flex items-center justify-center mb-6">
            <div className="border-t border-gray-300 absolute w-full"></div>
            <div className="bg-white px-3 relative text-gray-500 text-sm">or continue with</div>
          </div>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <button className="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
              <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24">
                <path
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  fill="#4285F4"
                />
                <path
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  fill="#34A853"
                />
                <path
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  fill="#FBBC05"
                />
                <path
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  fill="#EA4335"
                />
              </svg>
              Google
            </button>
            <button className="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
              <svg className="h-5 w-5 mr-2" fill="#1877F2" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
              </svg>
              Facebook
            </button>
          </div>
          <div className="text-center text-gray-600">
            Don&apos;t have an account?{' '}
            <a href="/auth/signup" className="text-nature-green font-medium hover:underline">
              Sign Up
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
