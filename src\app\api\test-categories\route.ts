import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // 1. First, try to read all categories
    console.log('Attempting to read categories...');
    const { data: readData, error: readError } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (readError) {
      console.error('Error reading categories:', readError);
      return NextResponse.json({ 
        error: 'Error reading categories', 
        details: readError,
        step: 'read'
      }, { status: 500 });
    }

    // 2. Try to create a test category
    const testSlug = `test-category-${Date.now()}`;
    console.log(`Attempting to create test category with slug: ${testSlug}...`);
    
    const { data: createData, error: createError } = await supabase
      .from('categories')
      .insert({
        name: `Test Category ${Date.now()}`,
        slug: testSlug,
        description: 'This is a test category created by the diagnostic API',
        icon: 'leaf'
      })
      .select();

    if (createError) {
      console.error('Error creating test category:', createError);
      return NextResponse.json({ 
        error: 'Error creating test category', 
        details: createError,
        step: 'create',
        readData: readData || []
      }, { status: 500 });
    }

    // 3. Try to update the test category
    const testCategoryId = createData[0].id;
    console.log(`Attempting to update test category with ID: ${testCategoryId}...`);
    
    const { data: updateData, error: updateError } = await supabase
      .from('categories')
      .update({
        description: `Updated at ${new Date().toISOString()}`
      })
      .eq('id', testCategoryId)
      .select();

    if (updateError) {
      console.error('Error updating test category:', updateError);
      return NextResponse.json({ 
        error: 'Error updating test category', 
        details: updateError,
        step: 'update',
        readData: readData || [],
        createData: createData || []
      }, { status: 500 });
    }

    // 4. Try to delete the test category
    console.log(`Attempting to delete test category with ID: ${testCategoryId}...`);
    
    const { error: deleteError } = await supabase
      .from('categories')
      .delete()
      .eq('id', testCategoryId);

    if (deleteError) {
      console.error('Error deleting test category:', deleteError);
      return NextResponse.json({ 
        error: 'Error deleting test category', 
        details: deleteError,
        step: 'delete',
        readData: readData || [],
        createData: createData || [],
        updateData: updateData || []
      }, { status: 500 });
    }

    // 5. Read categories again to confirm changes
    console.log('Reading categories again to confirm changes...');
    const { data: finalReadData, error: finalReadError } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (finalReadError) {
      console.error('Error in final read of categories:', finalReadError);
      return NextResponse.json({ 
        error: 'Error in final read of categories', 
        details: finalReadError,
        step: 'final-read',
        readData: readData || [],
        createData: createData || [],
        updateData: updateData || []
      }, { status: 500 });
    }

    // Return success with all the data
    return NextResponse.json({
      success: true,
      initialCategories: readData || [],
      createdCategory: createData || [],
      updatedCategory: updateData || [],
      finalCategories: finalReadData || [],
      message: 'All database operations completed successfully'
    });
  } catch (error) {
    console.error('Unexpected error in test-categories API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error
    }, { status: 500 });
  }
}
