'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getPendingContent, reviewContent } from '../actions';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Wrapper component for useSearchParams
function SearchParamsWrapper({ children }: { children: (params: ReturnType<typeof useSearchParams>) => React.ReactNode }) {
  const searchParams = useSearchParams();
  return <>{children(searchParams)}</>;
}

type ContentItem = {
  id: string;
  title: string;
  contentType: 'article' | 'media' | 'comment';
  created_at: string;
  status: string;
  profiles?: {
    username: string;
    full_name: string;
  };
  categories?: {
    name: string;
    slug: string;
  };
  excerpt?: string;
  description?: string;
  file_url?: string;
  file_type?: string;
};

type PaginationData = {
  page: number;
  limit: number;
  total: number;
  pages: number;
};

export default function ModerationPage() {
  const { user, loading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return;

      if (!user) {
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        router.push('/');
        return;
      }

      setIsAdmin(true);
      setIsLoading(false);
    }

    checkAuth();
  }, [user, loading, router]);

  // Initialize state at the top level, not conditionally
  const [contentType, setContentType] = useState<'article' | 'media' | 'comment' | undefined>(undefined);
  const [pendingContent, setPendingContent] = useState<ContentItem[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [contentLoading, setContentLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [reviewingItem, setReviewingItem] = useState<ContentItem | null>(null);
  const [reviewReason, setReviewReason] = useState('');
  const [processingId, setProcessingId] = useState<string | null>(null);

  // Define loadPendingContent inside useEffect to avoid dependency issues
  useEffect(() => {
    // Only load content if user is authenticated and authorized
    if (!isAdmin || loading || isLoading) return;

    // Initialize content type from URL on first load
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const typeParam = params.get('type') as 'article' | 'media' | 'comment' | null;
      if (typeParam && contentType === undefined) {
        setContentType(typeParam);
        return; // Will trigger another useEffect run with the updated contentType
      }
    }

    async function loadPendingContent() {
      try {
        setContentLoading(true);
        const result = await getPendingContent(contentType, pagination.page, pagination.limit);

        if (result.error) {
          setError('Failed to load pending content: ' + result.error.message);
          return;
        }

        setPendingContent(result.data || []);
        setPagination(result.pagination || { page: 1, limit: 10, total: 0, pages: 0 });
      } catch (err) {
        console.error('Error loading pending content:', err);
        setError('An unexpected error occurred while loading pending content');
      } finally {
        setContentLoading(false);
      }
    }

    loadPendingContent();
  }, [contentType, pagination.page, pagination.limit, isAdmin, loading, isLoading]);

  if (loading || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        <span className="ml-3 text-lg">Loading moderation panel...</span>
      </div>
    );
  }

  if (!isAdmin) {
    return null; // Don't render anything while redirecting
  }

  const handleContentTypeChange = (type?: 'article' | 'media' | 'comment') => {
    setContentType(type);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page

    // Update URL query parameter
    const params = new URLSearchParams(window.location.search);
    if (type) {
      params.set('type', type);
    } else {
      params.delete('type');
    }
    router.push(`/admin/moderation?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination(prev => ({ ...prev, page: newPage }));
    }
  };

  const openReviewModal = (item: ContentItem) => {
    setReviewingItem(item);
    setReviewReason('');
  };

  const closeReviewModal = () => {
    setReviewingItem(null);
    setReviewReason('');
  };

  const handleReviewAction = async (status: 'published' | 'rejected') => {
    if (!reviewingItem) return;

    try {
      setProcessingId(reviewingItem.id);
      setError('');
      setSuccess('');

      const result = await reviewContent({
        contentId: reviewingItem.id,
        contentType: reviewingItem.contentType,
        status,
        reason: reviewReason
      });

      if (result.error) {
        setError(`Failed to ${status} content: ${result.error.message}`);
        return;
      }

      // Remove the reviewed item from the list
      setPendingContent(prev => prev.filter(item => item.id !== reviewingItem.id));

      setSuccess(`Content has been ${status === 'published' ? 'approved and published' : 'rejected'} successfully`);
      closeReviewModal();

      // Refresh the list if it's now empty
      if (pendingContent.length <= 1) {
        // Trigger a re-render to reload content
        setPagination(prev => ({ ...prev }));
      }
    } catch (err) {
      console.error(`Error ${status} content:`, err);
      setError(`An unexpected error occurred while ${status === 'published' ? 'publishing' : 'rejecting'} content`);
    } finally {
      setProcessingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Content Moderation</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          {success}
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Pending Content Reviews</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => handleContentTypeChange(undefined)}
              className={`px-3 py-1 rounded-md text-sm ${!contentType ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              All
            </button>
            <button
              onClick={() => handleContentTypeChange('article')}
              className={`px-3 py-1 rounded-md text-sm ${contentType === 'article' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Articles
            </button>
            <button
              onClick={() => handleContentTypeChange('media')}
              className={`px-3 py-1 rounded-md text-sm ${contentType === 'media' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Media
            </button>
            <button
              onClick={() => handleContentTypeChange('comment')}
              className={`px-3 py-1 rounded-md text-sm ${contentType === 'comment' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Comments
            </button>
          </div>
        </div>

        {contentLoading && <p className="text-gray-500">Loading pending content...</p>}

        {!contentLoading && pendingContent.length === 0 && (
          <div className="text-center py-8">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-1">All Caught Up!</h3>
            <p className="text-gray-500">There are no pending content items that need review.</p>
          </div>
        )}

        {!contentLoading && pendingContent.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Author
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Submitted
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {pendingContent.map(item => (
                  <tr key={`${item.contentType}-${item.id}`}>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">{item.title}</div>
                      <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                        {item.excerpt || item.description || ''}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.contentType === 'article' ? 'bg-blue-100 text-blue-800' : item.contentType === 'media' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`}>
                        {item.contentType.charAt(0).toUpperCase() + item.contentType.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {item.profiles ? item.profiles.full_name || item.profiles.username : 'Unknown'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{formatDate(item.created_at)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => openReviewModal(item)}
                        className="text-nature-green hover:text-nature-green-dark"
                        disabled={processingId === item.id}
                      >
                        Review
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {pagination.pages > 1 && (
          <div className="mt-6 flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Showing {(pagination.page - 1) * pagination.limit + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} items
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-sm hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                // Show pages around current page
                let pageNum;
                if (pagination.pages <= 5) {
                  pageNum = i + 1;
                } else if (pagination.page <= 3) {
                  pageNum = i + 1;
                } else if (pagination.page >= pagination.pages - 2) {
                  pageNum = pagination.pages - 4 + i;
                } else {
                  pageNum = pagination.page - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-1 rounded-md text-sm ${pagination.page === pageNum ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.pages}
                className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-sm hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Review Modal */}
      {reviewingItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-bold">
                  Review {reviewingItem.contentType.charAt(0).toUpperCase() + reviewingItem.contentType.slice(1)}
                </h3>
                <button
                  onClick={closeReviewModal}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-6">
                <h4 className="text-lg font-semibold mb-2">{reviewingItem.title}</h4>
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                    Type: {reviewingItem.contentType}
                  </span>
                  {reviewingItem.categories && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                      Category: {reviewingItem.categories.name}
                    </span>
                  )}
                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                    Submitted: {formatDate(reviewingItem.created_at)}
                  </span>
                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                    By: {reviewingItem.profiles ? reviewingItem.profiles.full_name || reviewingItem.profiles.username : 'Unknown'}
                  </span>
                </div>

                <div className="border border-gray-200 rounded-md p-4 mb-4 bg-gray-50">
                  {reviewingItem.contentType === 'article' && (
                    <div>
                      <h5 className="font-medium mb-2">Excerpt:</h5>
                      <p className="text-gray-700 mb-4">{reviewingItem.excerpt}</p>
                      <div className="flex justify-center">
                        <button
                          onClick={() => router.push(`/admin/review/article/${reviewingItem.id}`)}
                          className="text-nature-green hover:underline"
                        >
                          View Full Article
                        </button>
                      </div>
                    </div>
                  )}

                  {reviewingItem.contentType === 'media' && (
                    <div>
                      <h5 className="font-medium mb-2">Description:</h5>
                      <p className="text-gray-700 mb-4">{reviewingItem.description}</p>
                      {reviewingItem.file_url && reviewingItem.file_type?.startsWith('image/') && (
                        <div className="flex justify-center">
                          <img
                            src={reviewingItem.file_url}
                            alt={reviewingItem.title}
                            className="max-h-64 object-contain"
                          />
                        </div>
                      )}
                    </div>
                  )}

                  {reviewingItem.contentType === 'comment' && (
                    <div>
                      <h5 className="font-medium mb-2">Comment:</h5>
                      <p className="text-gray-700">{reviewingItem.description}</p>
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="reviewReason" className="block text-gray-700 font-medium mb-2">
                    Review Notes (optional)
                  </label>
                  <textarea
                    id="reviewReason"
                    value={reviewReason}
                    onChange={(e) => setReviewReason(e.target.value)}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
                    placeholder="Add notes about your decision (will be visible to the author)"
                  ></textarea>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={closeReviewModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleReviewAction('rejected')}
                  disabled={processingId === reviewingItem.id}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {processingId === reviewingItem.id ? 'Processing...' : 'Reject'}
                </button>
                <button
                  onClick={() => handleReviewAction('published')}
                  disabled={processingId === reviewingItem.id}
                  className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {processingId === reviewingItem.id ? 'Processing...' : 'Approve & Publish'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}