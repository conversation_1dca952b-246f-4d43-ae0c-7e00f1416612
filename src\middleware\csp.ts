import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Middleware to add Content Security Policy headers
 */
export function cspMiddleware(request: NextRequest) {
  // Get the response
  const response = NextResponse.next();

  // Define CSP directives
  const csp = [
    // Default (fallback) directive
    "default-src 'self'",

    // Scripts - allow self, Supabase, and inline scripts (with nonce)
    "script-src 'self' https://*.supabase.co https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/ 'unsafe-inline'",

    // Styles - allow self and inline styles
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",

    // Images - allow self, data URLs, and common image CDNs
    "img-src 'self' data: https: blob:",

    // Fonts - allow self and Google Fonts
    "font-src 'self' https://fonts.gstatic.com",

    // Connect - allow self, Supabase, and other necessary services
    "connect-src 'self' https://*.supabase.co https://*.supabase.in https://xjzhbwjuykbxkuarytgx.supabase.co wss://*.supabase.co https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/",

    // Media - allow self
    "media-src 'self'",

    // Object - allow none
    "object-src 'none'",

    // Frame - allow self and Google reCAPTCHA
    "frame-src 'self' https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/",

    // Frame ancestors - allow self only (prevents clickjacking)
    "frame-ancestors 'self'",

    // Form action - allow self only
    "form-action 'self'",

    // Base URI - allow self only
    "base-uri 'self'",

    // Upgrade insecure requests
    "upgrade-insecure-requests",

    // Block mixed content
    "block-all-mixed-content"
  ].join('; ');

  // Add CSP header
  response.headers.set('Content-Security-Policy', csp);

  // Add other security headers

  // X-Content-Type-Options prevents MIME type sniffing
  response.headers.set('X-Content-Type-Options', 'nosniff');

  // X-Frame-Options prevents your site from being framed (clickjacking protection)
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');

  // X-XSS-Protection enables the cross-site scripting filter in browsers
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // Referrer-Policy controls how much referrer information is sent
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Permissions-Policy controls which features and APIs can be used
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(self), interest-cohort=()'
  );

  // Strict-Transport-Security ensures HTTPS is always used
  if (process.env.NODE_ENV === 'production') {
    response.headers.set(
      'Strict-Transport-Security',
      'max-age=63072000; includeSubDomains; preload'
    );
  }

  return response;
}
