import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Load environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function seedCategories() {
  try {
    // Read categories from JSON file
    const categoriesPath = path.join(process.cwd(), 'src', 'data', 'categories.json');
    const categoriesData = JSON.parse(fs.readFileSync(categoriesPath, 'utf8'));
    
    console.log(`Found ${categoriesData.length} categories to seed`);
    
    // Insert categories into the database
    const { data, error } = await supabase
      .from('categories')
      .upsert(
        categoriesData.map((category: any) => ({
          name: category.name,
          slug: category.slug,
          description: category.description || null
        })),
        { onConflict: 'slug' }
      );
    
    if (error) {
      console.error('Error seeding categories:', error);
      return;
    }
    
    console.log('Categories seeded successfully!');
  } catch (error) {
    console.error('Unexpected error seeding categories:', error);
  }
}

// Run the seed function
seedCategories();
