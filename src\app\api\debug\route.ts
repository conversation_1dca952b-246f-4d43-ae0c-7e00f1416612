import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Helper function to handle CORS
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-client-info, apikey, X-Client-Info',
  };
}

// GET /api/debug - Debug endpoint to check database connection
export async function GET(request: NextRequest) {
  // Add cache control headers to prevent caching
  const headers = {
    ...corsHeaders(),
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Surrogate-Control': 'no-store',
    'X-Timestamp': new Date().getTime().toString(),
  };

  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json(
        { error: 'Missing Supabase environment variables' },
        { status: 500, headers }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Try to check connection by testing categories table directly
    let categoriesExists = false;
    let tables = [];

    try {
      // Test connection by trying to access categories table
      const { data: testData, error: testError } = await supabase
        .from('categories')
        .select('id')
        .limit(1);

      if (!testError) {
        categoriesExists = true;
        tables = ['categories']; // We know this exists
      }
    } catch (connectionError) {
      console.error('Connection test failed:', connectionError);
      return NextResponse.json(
        {
          error: 'Database connection failed',
          details: 'Could not connect to Supabase database',
          env: {
            url: supabaseUrl.substring(0, 10) + '...',
            key: supabaseKey.substring(0, 5) + '...'
          }
        },
        { status: 500, headers }
      );
    }

    // If categories table exists, try to fetch categories
    let categories = [];
    let categoriesError = null;
    
    if (categoriesExists) {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name, slug, description')
        .limit(5);
        
      categories = data || [];
      categoriesError = error ? error.message : null;
    }

    return NextResponse.json({
      status: 'Connected',
      tables: tables,
      categoriesExists,
      categorySample: categories,
      categoriesError,
      env: {
        url: supabaseUrl.substring(0, 10) + '...',
        key: supabaseKey.substring(0, 5) + '...'
      }
    }, { headers });
  } catch (error: any) {
    console.error('Error in debug API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        message: error.message,
        stack: error.stack
      },
      { status: 500, headers }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders() });
}
