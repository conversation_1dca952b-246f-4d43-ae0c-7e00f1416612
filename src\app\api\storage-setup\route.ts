import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

// Create a Supabase client with the service role key
// This bypasses RLS policies but should only be used in secure server contexts
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

export async function GET() {
  try {
    // Use direct client with service role key instead of cookie-based auth
    // This is safe in route handlers that run on the server
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Check if the media bucket exists
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

    if (bucketsError) {
      return NextResponse.json({ error: bucketsError.message }, { status: 500 });
    }

    const mediaBucketExists = buckets.some(bucket => bucket.name === 'media');

    if (!mediaBucketExists) {
      // Create the media bucket
      const { error: createError } = await supabase.storage.createBucket('media', {
        public: true,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      });

      if (createError) {
        return NextResponse.json({ error: createError.message }, { status: 500 });
      }

      return NextResponse.json({ message: 'Media bucket created successfully' });
    }

    return NextResponse.json({ message: 'Media bucket already exists' });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
