-- Comprehensive fix for forum_topics RLS issues

-- Step 1: Temporarily disable <PERSON><PERSON> on the forum_topics table
ALTER TABLE public.forum_topics DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop all existing policies on the table
DROP POLICY IF EXISTS "Authenticated users can create forum topics" ON public.forum_topics;
DROP POLICY IF EXISTS "Forum topics are viewable by everyone" ON public.forum_topics;
DROP POLICY IF EXISTS "Users can update their own forum topics" ON public.forum_topics;
DROP POLICY IF EXISTS "Users can delete their own forum topics" ON public.forum_topics;
DROP POLICY IF EXISTS "Moderators can update any forum topic" ON public.forum_topics;
DROP POLICY IF EXISTS "Moderators can delete any forum topic" ON public.forum_topics;

-- Step 3: Re-enable RLS
ALTER TABLE public.forum_topics ENABLE ROW LEVEL SECURITY;

-- Step 4: Create new policies with correct permissions

-- Policy for viewing topics (everyone can view)
CREATE POLICY "Forum topics are viewable by everyone"
    ON public.forum_topics FOR SELECT
    USING (true);

-- Policy for creating topics (any authenticated user can create)
CREATE POLICY "Authenticated users can create forum topics"
    ON public.forum_topics FOR INSERT
    WITH CHECK (auth.uid() IS NOT NULL);

-- Policy for updating topics (owners and moderators/admins can update)
CREATE POLICY "Users can update their own forum topics"
    ON public.forum_topics FOR UPDATE
    USING (
        auth.uid() = author_id 
        OR 
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
        )
    );

-- Policy for deleting topics (owners and moderators/admins can delete)
CREATE POLICY "Users can delete their own forum topics"
    ON public.forum_topics FOR DELETE
    USING (
        auth.uid() = author_id 
        OR 
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
        )
    );

-- Step 5: Verify the policies are correctly set
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check 
FROM pg_policies 
WHERE tablename = 'forum_topics';
