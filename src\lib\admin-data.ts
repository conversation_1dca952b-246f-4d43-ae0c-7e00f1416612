import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getRecentForumActivity } from './forums';

// Simple function to get basic admin stats
export async function getBasicAdminStats() {
  const supabase = createClientComponentClient();

  // Get profiles count (total users)
  const { count: usersCount, error: usersError } = await supabase
    .from('profiles')
    .select('*', { count: 'exact', head: true });

  // Get admin/moderator count
  const { count: adminCount, error: adminError } = await supabase
    .from('profiles')
    .select('*', { count: 'exact', head: true })
    .or('role.eq.admin,role.eq.moderator');

  // Get new users in the last week
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  const { count: newUsersCount, error: newUsersError } = await supabase
    .from('profiles')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', oneWeekAgo.toISOString());

  // Get forum stats
  const { count: topicsCount, error: topicsError } = await supabase
    .from('forum_topics')
    .select('*', { count: 'exact', head: true });

  const { count: postsCount, error: postsError } = await supabase
    .from('forum_posts')
    .select('*', { count: 'exact', head: true });

  // Get pending moderation count
  const { count: pendingModerationCount, error: pendingModerationError } = await supabase
    .from('articles')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending_review');

  // Get reported content count
  const { count: reportedContentCount, error: reportedContentError } = await supabase
    .from('reported_content')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending');

  return {
    totalUsers: usersCount || 0,
    adminUsers: adminCount || 0,
    newUsers: newUsersCount || 0,
    forumTopics: topicsCount || 0,
    forumPosts: postsCount || 0,
    pendingModeration: pendingModerationCount || 0,
    reportedContent: reportedContentCount || 0,
    errors: [usersError, adminError, newUsersError, topicsError, postsError, pendingModerationError, reportedContentError].filter(Boolean)
  };
}

// Function to get recent user signups
export async function getRecentUsers(limit = 5) {
  const supabase = createClientComponentClient();

  const { data, error } = await supabase
    .from('profiles')
    .select('id, username, full_name, role, created_at')
    .order('created_at', { ascending: false })
    .limit(limit);

  return {
    users: data || [],
    error
  };
}

// Function to get recent activity for admin dashboard
export async function getRecentActivity(limit = 10) {
  try {
    // Get recent forum activity
    let forumActivity = [];
    try {
      forumActivity = await getRecentForumActivity(limit);
      if (!Array.isArray(forumActivity)) {
        console.error('Invalid forum activity data format:', forumActivity);
        forumActivity = [];
      }
    } catch (forumError) {
      console.error('Error fetching forum activity:', forumError);
      forumActivity = [];
    }

    // Get recent user registrations
    let recentUsers = [];
    try {
      const result = await getRecentUsers(limit);
      recentUsers = result.users || [];
      if (result.error) {
        console.error('Error in user registration data:', result.error);
      }
    } catch (userError) {
      console.error('Exception fetching recent users:', userError);
      recentUsers = [];
    }

    // Get recent moderation actions
    let moderationActions = [];
    try {
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('moderation_actions')
        .select(`
          *,
          moderator:moderator_id (username, full_name, avatar_url)
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error && error.code !== 'PGRST204') { // PGRST204 means table is empty, not an error
        console.error('Error fetching moderation actions:', error);
      } else {
        moderationActions = data || [];
      }
    } catch (moderationError) {
      console.error('Exception fetching moderation actions:', moderationError);
    }

    // Combine all activities and sort by date
    try {
      const allActivity = [
        ...forumActivity.map(item => ({
          ...item,
          activityType: 'forum'
        })),
        ...recentUsers.map(user => ({
          type: 'user_registration',
          id: user.id,
          username: user.username || 'Unknown',
          full_name: user.full_name || '',
          role: user.role || 'user',
          created_at: user.created_at || new Date().toISOString(),
          activityType: 'user'
        })),
        ...moderationActions.map(action => ({
          type: action.action_type || action.action || 'unknown', // Use action_type if available, otherwise use action
          id: action.id || `mod-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          content_id: action.content_id,
          content_type: action.content_type,
          reason: action.reason,
          moderator: action.moderator,
          created_at: action.created_at || new Date().toISOString(),
          activityType: 'moderation'
        }))
      ]
      .filter(item => item && item.created_at) // Filter out items without created_at
      .sort((a, b) => {
        try {
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        } catch (dateError) {
          console.error('Error sorting by date:', dateError, { a, b });
          return 0;
        }
      })
      .slice(0, limit);

      return allActivity;
    } catch (combinationError) {
      console.error('Error combining activity data:', combinationError);
      return [];
    }
  } catch (error) {
    console.error('Error getting recent activity:', error);
    return [];
  }
}
