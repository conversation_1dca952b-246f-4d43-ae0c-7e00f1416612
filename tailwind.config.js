/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'nature-green': {
          light: '#4caf50',
          DEFAULT: '#2e7d32',
          dark: '#1b5e20',
        },
        'nature-green-dark': '#1b5e20',
        'nature-brown': {
          light: '#8d6e63',
          DEFAULT: '#5d4037',
          dark: '#3e2723',
        },
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            // Set default heading colors for prose to black for better readability
            h1: {
              color: '#000000',
            },
            h2: {
              color: '#000000',
            },
            h3: {
              color: '#000000',
            },
            h4: {
              color: '#000000',
            },
            // Ensure links within prose use the site's theme color
            a: {
              color: theme('colors.nature-green.DEFAULT'),
              '&:hover': {
                color: theme('colors.nature-green.dark'),
              },
              textDecoration: 'underline',
            },
            // Ensure body text within prose uses the site's default foreground color
            // This matches --foreground: #171717 from globals.css
            color: theme('colors.neutral.900'),
          },
        },
      }),
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
};
