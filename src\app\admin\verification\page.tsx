'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { getAllVerificationRequests, updateVerificationStatus } from '@/lib/verification';
import Link from 'next/link';

export default function AdminVerificationPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [verificationRequests, setVerificationRequests] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'rejected'>('pending');
  const [selectedRequest, setSelectedRequest] = useState<any | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  
  useEffect(() => {
    async function checkAuth() {
      if (loading) return;
      
      if (!user) {
        router.push('/auth/signin?redirect=/admin/verification');
        return;
      }
      
      if (user.role !== 'admin' && user.role !== 'moderator') {
        router.push('/');
        return;
      }
      
      loadVerificationRequests();
    }
    
    checkAuth();
  }, [user, loading, router, activeTab]);
  
  async function loadVerificationRequests() {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await getAllVerificationRequests({
        status: activeTab,
        limit: 50
      });
      
      setVerificationRequests(data || []);
    } catch (err: any) {
      setError(err.message || 'Failed to load verification requests');
    } finally {
      setIsLoading(false);
    }
  }
  
  const handleApprove = async (requestId: string) => {
    if (!user) return;
    
    try {
      await updateVerificationStatus({
        requestId,
        status: 'approved',
        reviewerId: user.id,
        reviewNotes: reviewNotes
      });
      
      setSuccess('Verification request approved successfully');
      setSelectedRequest(null);
      setReviewNotes('');
      loadVerificationRequests();
    } catch (err: any) {
      setError(err.message || 'Failed to approve verification request');
    }
  };
  
  const handleReject = async (requestId: string) => {
    if (!user) return;
    
    if (!reviewNotes) {
      setError('Please provide review notes explaining why the request was rejected');
      return;
    }
    
    try {
      await updateVerificationStatus({
        requestId,
        status: 'rejected',
        reviewerId: user.id,
        reviewNotes: reviewNotes
      });
      
      setSuccess('Verification request rejected successfully');
      setSelectedRequest(null);
      setReviewNotes('');
      loadVerificationRequests();
    } catch (err: any) {
      setError(err.message || 'Failed to reject verification request');
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Expert Verification Requests</h1>
        
        {error && (
          <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mb-6 p-3 bg-green-50 text-green-700 rounded-md">
            {success}
          </div>
        )}
        
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="border-b border-gray-200">
            <nav className="flex -mb-px">
              <button
                onClick={() => setActiveTab('pending')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'pending'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Pending
              </button>
              <button
                onClick={() => setActiveTab('approved')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'approved'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Approved
              </button>
              <button
                onClick={() => setActiveTab('rejected')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'rejected'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Rejected
              </button>
            </nav>
          </div>
          
          {isLoading ? (
            <div className="p-6 text-center">
              <p className="text-gray-500">Loading verification requests...</p>
            </div>
          ) : verificationRequests.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-gray-500">No {activeTab} verification requests found.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Specialty
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {verificationRequests.map((request) => (
                    <tr key={request.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {request.user?.avatar_url ? (
                              <img
                                className="h-10 w-10 rounded-full"
                                src={request.user.avatar_url}
                                alt={request.user.username}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-500 font-medium">
                                  {request.user?.username?.charAt(0).toUpperCase() || '?'}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              <Link href={`/profile/${request.user?.username}`} className="hover:underline">
                                {request.user?.full_name || request.user?.username}
                              </Link>
                            </div>
                            <div className="text-sm text-gray-500">
                              @{request.user?.username}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{request.specialty}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(request.created_at).toLocaleDateString()}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(request.created_at).toLocaleTimeString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          request.status === 'approved' ? 'bg-green-100 text-green-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => setSelectedRequest(request)}
                          className="text-nature-green hover:text-green-700"
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
        
        {/* Request Details Modal */}
        {selectedRequest && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Verification Request Details</h2>
                  <button
                    onClick={() => setSelectedRequest(null)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="mb-6">
                  <div className="flex items-center mb-4">
                    {selectedRequest.user?.avatar_url ? (
                      <img
                        className="h-12 w-12 rounded-full mr-4"
                        src={selectedRequest.user.avatar_url}
                        alt={selectedRequest.user.username}
                      />
                    ) : (
                      <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                        <span className="text-gray-500 font-medium">
                          {selectedRequest.user?.username?.charAt(0).toUpperCase() || '?'}
                        </span>
                      </div>
                    )}
                    <div>
                      <h3 className="text-lg font-medium">
                        {selectedRequest.user?.full_name || selectedRequest.user?.username}
                      </h3>
                      <p className="text-gray-500">@{selectedRequest.user?.username}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Specialty</h4>
                      <p className="text-gray-900">{selectedRequest.specialty}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Submitted</h4>
                      <p className="text-gray-900">
                        {new Date(selectedRequest.created_at).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Status</h4>
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        selectedRequest.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        selectedRequest.status === 'approved' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {selectedRequest.status.charAt(0).toUpperCase() + selectedRequest.status.slice(1)}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Expertise Level</h4>
                      <p className="text-gray-900">
                        {selectedRequest.user?.expertise_level?.charAt(0).toUpperCase() + 
                          selectedRequest.user?.expertise_level?.slice(1) || 'Not specified'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Credentials</h4>
                    <div className="p-3 bg-gray-50 rounded-md">
                      <p className="text-gray-900 whitespace-pre-line">{selectedRequest.credentials}</p>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Verification Documents</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {selectedRequest.verification_documents.map((doc: string, index: number) => (
                        <a
                          key={index}
                          href={doc}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center p-2 bg-gray-50 rounded-md hover:bg-gray-100"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <span className="text-sm text-nature-green">Document {index + 1}</span>
                        </a>
                      ))}
                    </div>
                  </div>
                  
                  {(selectedRequest.status === 'approved' || selectedRequest.status === 'rejected') && selectedRequest.review_notes && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Review Notes</h4>
                      <div className="p-3 bg-gray-50 rounded-md">
                        <p className="text-gray-900 whitespace-pre-line">{selectedRequest.review_notes}</p>
                      </div>
                    </div>
                  )}
                  
                  {selectedRequest.status === 'pending' && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Review Notes</h4>
                      <textarea
                        value={reviewNotes}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                        rows={3}
                        placeholder="Add notes about your decision (required for rejections)"
                      />
                    </div>
                  )}
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setSelectedRequest(null)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Close
                  </button>
                  
                  {selectedRequest.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleReject(selectedRequest.id)}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                      >
                        Reject
                      </button>
                      <button
                        onClick={() => handleApprove(selectedRequest.id)}
                        className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700"
                      >
                        Approve
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
