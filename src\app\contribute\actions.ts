'use server';

import { createActionClient } from '@/lib/auth.server';
import { redirect } from 'next/navigation';
import { getSession } from '@/lib/auth.server';
import { verifyRecaptcha } from '@/lib/recaptcha';

type ArticleData = {
  title: string;
  content: string;
  excerpt?: string;
  categoryId: string;
  tags?: string[];
  recaptchaToken?: string;
};

export async function submitArticle(data: ArticleData) {
  // Ensure user is authenticated
  const session = await getSession();
  if (!session) {
    return { error: { message: 'User not authenticated' } };
  }

  // Verify reCAPTCHA token if provided
  if (data.recaptchaToken) {
    const isValid = await verifyRecaptcha(data.recaptchaToken);
    if (!isValid) {
      return { error: { message: 'reCAPTCHA verification failed. Please try again.' } };
    }
  }

  const supabase = createActionClient();

  // Generate a slug from the title
  const slug = data.title
    .toLowerCase()
    .replace(/[^\w\s]/gi, '')
    .replace(/\s+/g, '-');

  // Check if slug already exists
  const { data: existingArticle } = await supabase
    .from('articles')
    .select('id')
    .eq('slug', slug)
    .single();

  if (existingArticle) {
    return {
      error: {
        message: 'An article with a similar title already exists',
      },
    };
  }

  // Insert the article
  const { data: article, error } = await supabase
    .from('articles')
    .insert({
      title: data.title,
      slug,
      content: data.content,
      excerpt: data.excerpt || data.content.substring(0, 150) + '...',
      author_id: session.user.id,
      category_id: data.categoryId,
      status: 'pending_review',
    })
    .select()
    .single();

  if (error) {
    return { error };
  }

  // If tags are provided, insert them
  if (data.tags && data.tags.length > 0 && article) {
    // First, ensure all tags exist
    for (const tagName of data.tags) {
      const tagSlug = tagName.toLowerCase().replace(/[^\w\s]/gi, '').replace(/\s+/g, '-');

      // Check if tag exists
      const { data: existingTag } = await supabase
        .from('tags')
        .select('id')
        .eq('slug', tagSlug)
        .single();

      if (!existingTag) {
        // Create the tag
        await supabase.from('tags').insert({
          name: tagName,
          slug: tagSlug,
        });
      }
    }

    // Then get all the tag IDs
    const { data: tagData } = await supabase
      .from('tags')
      .select('id, slug')
      .in(
        'slug',
        data.tags.map(tag => tag.toLowerCase().replace(/[^\w\s]/gi, '').replace(/\s+/g, '-'))
      );

    if (tagData && tagData.length > 0) {
      // Insert article-tag relationships
      const articleTags = tagData.map(tag => ({
        article_id: article.id,
        tag_id: tag.id,
      }));

      await supabase.from('article_tags').insert(articleTags);
    }
  }

  // Record the contribution
  await supabase.from('contributions').insert({
    user_id: session.user.id,
    content_type: 'article',
    content_id: article.id,
    contribution_type: 'create',
  });

  return { data: article };
}

export async function getCategories() {
  const supabase = createActionClient();

  const { data, error } = await supabase
    .from('categories')
    .select('id, name, slug, description, parent_id')
    .order('name');

  if (error) {
    return { error };
  }

  return { data };
}

export async function createCategory(name: string, description: string, parentId?: string) {
  // Ensure user is an admin
  const session = await getSession();
  if (!session) {
    return { error: { message: 'User not authenticated' } };
  }

  const supabase = createActionClient();

  // Generate a slug from the name
  const slug = name
    .toLowerCase()
    .replace(/[^\w\s]/gi, '')
    .replace(/\s+/g, '-');

  // Check if slug already exists
  const { data: existingCategory } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', slug)
    .single();

  if (existingCategory) {
    return {
      error: {
        message: 'A category with this name already exists',
      },
    };
  }

  // Insert the category
  const { data, error } = await supabase
    .from('categories')
    .insert({
      name,
      slug,
      description,
      parent_id: parentId || null,
    })
    .select()
    .single();

  if (error) {
    return { error };
  }

  return { data };
}