'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';

interface UserActivity {
  id: string;
  user_id: string;
  activity_type: string;
  content_type: string;
  content_id: string;
  metadata: any;
  created_at: string;
  profiles: {
    username: string;
    full_name?: string;
    avatar_url?: string;
  };
  article_title?: string;
  article_slug?: string;
}

interface UserActivityFeedProps {
  userId?: string;
  limit?: number;
  showTitle?: boolean;
  className?: string;
}

export default function UserActivityFeed({
  userId,
  limit = 5,
  showTitle = true,
  className = ''
}: UserActivityFeedProps) {
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchActivities() {
      setLoading(true);
      setError(null);

      try {
        let query = supabase
          .from('user_activities')
          .select(`
            *,
            profiles:user_id (username, full_name, avatar_url)
          `)
          .order('created_at', { ascending: false })
          .limit(limit);

        // Filter by user if userId is provided
        if (userId) {
          query = query.eq('user_id', userId);
        }

        const { data, error } = await query;

        if (error) {
          throw error;
        }

        // Fetch additional data for activities related to articles
        const activitiesWithArticles = await Promise.all(
          data.map(async (activity) => {
            if (activity.content_type === 'article') {
              const { data: articleData } = await supabase
                .from('articles')
                .select('title, slug')
                .eq('id', activity.content_id)
                .single();

              return {
                ...activity,
                article_title: articleData?.title,
                article_slug: articleData?.slug
              };
            }
            return activity;
          })
        );

        setActivities(activitiesWithArticles);
      } catch (err: any) {
        console.error('Error fetching user activities:', err);
        setError(err.message || 'Failed to fetch activities');
      } finally {
        setLoading(false);
      }
    }

    fetchActivities();
  }, [userId, limit]);

  // Get activity description based on type
  const getActivityDescription = (activity: UserActivity) => {
    const userName = activity.profiles.full_name || activity.profiles.username;
    
    switch (activity.activity_type) {
      case 'article_create':
        return (
          <>
            <span className="font-medium">{userName}</span> published a new article{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'article_edit':
        return (
          <>
            <span className="font-medium">{userName}</span> updated{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'comment':
        return (
          <>
            <span className="font-medium">{userName}</span> commented on{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'bookmark':
        return (
          <>
            <span className="font-medium">{userName}</span> bookmarked{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'social_share':
        return (
          <>
            <span className="font-medium">{userName}</span> shared{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}{' '}
            on {activity.metadata?.platform || 'social media'}
          </>
        );
      default:
        return (
          <>
            <span className="font-medium">{userName}</span> performed an activity
          </>
        );
    }
  };

  // Get icon based on activity type
  const getActivityIcon = (activity: UserActivity) => {
    switch (activity.activity_type) {
      case 'article_create':
        return (
          <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'article_edit':
        return (
          <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </div>
        );
      case 'comment':
        return (
          <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'bookmark':
        return (
          <div className="bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
            </svg>
          </div>
        );
      case 'social_share':
        return (
          <div className="bg-red-100 dark:bg-red-900/30 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  return (
    <div className={className}>
      {showTitle && (
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {userId ? 'User Activity' : 'Community Activity'}
        </h2>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-md">
          {error}
        </div>
      ) : activities.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <p className="text-gray-500 dark:text-gray-400">No activity yet</p>
        </div>
      ) : (
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex-shrink-0">
                {getActivityIcon(activity)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-gray-800 dark:text-gray-200">
                  {getActivityDescription(activity)}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}

      {activities.length > 0 && (
        <div className="mt-4 text-center">
          <Link
            href="/activity"
            className="text-nature-green hover:text-nature-green-dark dark:hover:text-green-400 font-medium text-sm"
          >
            View all activity
          </Link>
        </div>
      )}
    </div>
  );
}
