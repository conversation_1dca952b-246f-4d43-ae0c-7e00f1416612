import { supabase } from '@/lib/supabase';

export interface TableStatus {
  name: string;
  exists: boolean;
}

/**
 * Checks if required database tables exist
 * @returns An array of table statuses
 */
export async function checkRequiredTables(): Promise<TableStatus[]> {
  const requiredTables = [
    'profiles',
    'articles',
    'categories',
    'tags',
    'article_tags',
    'comments',
    'forum_categories',
    'forum_topics',
    'forum_posts',
    'forum_post_reactions',
    'reported_content',
    'rate_limits',
    'activities',
    'notifications',
    'notification_preferences'
  ];
  
  const results: TableStatus[] = [];
  
  for (const tableName of requiredTables) {
    try {
      // Try to select a single row from the table
      const { count, error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      results.push({
        name: tableName,
        exists: !error
      });
    } catch (error) {
      results.push({
        name: tableName,
        exists: false
      });
    }
  }
  
  return results;
}

/**
 * Gets a list of missing tables
 * @returns An array of missing table names
 */
export async function getMissingTables(): Promise<string[]> {
  const tableStatuses = await checkRequiredTables();
  return tableStatuses
    .filter(table => !table.exists)
    .map(table => table.name);
}

/**
 * Checks if all required tables exist
 * @returns True if all required tables exist, false otherwise
 */
export async function allTablesExist(): Promise<boolean> {
  const missingTables = await getMissingTables();
  return missingTables.length === 0;
}
