-- Add missing columns to tables if they don't exist

-- Add moderator_notes column to reported_content if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'reported_content' 
        AND column_name = 'moderator_notes'
    ) THEN
        ALTER TABLE public.reported_content ADD COLUMN moderator_notes TEXT;
        RAISE NOTICE 'Added moderator_notes column to reported_content table';
    ELSE
        RAISE NOTICE 'moderator_notes column already exists in reported_content table';
    END IF;
END $$;

-- Add action_type column to moderation_actions if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'moderation_actions' 
        AND column_name = 'action_type'
    ) THEN
        ALTER TABLE public.moderation_actions ADD COLUMN action_type TEXT;
        RAISE NOTICE 'Added action_type column to moderation_actions table';
    ELSE
        RAISE NOTICE 'action_type column already exists in moderation_actions table';
    END IF;
END $$;

-- Add previous_content column to moderation_actions if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'moderation_actions' 
        AND column_name = 'previous_content'
    ) THEN
        ALTER TABLE public.moderation_actions ADD COLUMN previous_content TEXT;
        RAISE NOTICE 'Added previous_content column to moderation_actions table';
    ELSE
        RAISE NOTICE 'previous_content column already exists in moderation_actions table';
    END IF;
END $$;

-- Add new_content column to moderation_actions if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'moderation_actions' 
        AND column_name = 'new_content'
    ) THEN
        ALTER TABLE public.moderation_actions ADD COLUMN new_content TEXT;
        RAISE NOTICE 'Added new_content column to moderation_actions table';
    ELSE
        RAISE NOTICE 'new_content column already exists in moderation_actions table';
    END IF;
END $$;

-- Add additional_data column to moderation_actions if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'moderation_actions' 
        AND column_name = 'additional_data'
    ) THEN
        ALTER TABLE public.moderation_actions ADD COLUMN additional_data JSONB;
        RAISE NOTICE 'Added additional_data column to moderation_actions table';
    ELSE
        RAISE NOTICE 'additional_data column already exists in moderation_actions table';
    END IF;
END $$;
