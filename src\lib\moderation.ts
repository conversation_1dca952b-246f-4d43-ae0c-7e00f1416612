import { createClient } from '@/lib/supabase';
import { createModerationNotification, createReportResolvedNotification } from '@/lib/notifications';

// Helper function to log moderation actions
export async function logModerationAction({
  actionType,
  moderatorId,
  contentId,
  contentType,
  reason,
  previousContent,
  newContent,
  additionalData
}: {
  actionType: string;
  moderatorId: string;
  contentId: string;
  contentType: string;
  reason?: string;
  previousContent?: string;
  newContent?: string;
  additionalData?: any;
}) {
  const supabase = createClient();
  let actionData = null;

  try {
    console.log('Logging moderation action:', { actionType, contentId, contentType });

    // Create the action data object
    const actionRecord = {
      action: actionType, // Set both action and action_type fields
      action_type: actionType,
      moderator_id: moderatorId,
      content_id: contentId,
      content_type: contentType,
      reason,
      previous_content: previousContent,
      new_content: newContent,
      additional_data: additionalData,
      created_at: new Date().toISOString()
    };

    // Insert the action without returning data
    const { error: insertError } = await supabase
      .from('moderation_actions')
      .insert(actionRecord);

    if (insertError) {
      console.error('Error inserting moderation action:', insertError);
      return null; // Return null instead of throwing
    }

    // For notification purposes, we'll just use the data we already have
    // No need to query the database again
    actionData = { ...actionRecord, id: 'unknown' };
    console.log('Moderation action logged successfully');

    // We don't need to return the actual database record
    // Just return success
    return actionData;
  } catch (err) {
    console.error('Exception in logModerationAction:', err);
    return null; // Return null instead of throwing
  }


}

// Get moderation actions
export async function getModerationActions({
  limit = 20,
  offset = 0,
  contentType,
  actionType,
  moderatorId,
  startDate,
  endDate
}: {
  limit?: number;
  offset?: number;
  contentType?: string;
  actionType?: string;
  moderatorId?: string;
  startDate?: string;
  endDate?: string;
}) {
  const supabase = createClient();

  try {
    let query = supabase
      .from('moderation_actions')
      .select(`
        *,
        moderator:moderator_id (username, full_name, avatar_url)
      `, { count: 'exact' });

    // Apply filters if provided
    if (contentType) {
      query = query.eq('content_type', contentType);
    }

    if (actionType) {
      // Check both action and action_type fields
      query = query.or(`action.eq.${actionType},action_type.eq.${actionType}`);
    }

    if (moderatorId) {
      query = query.eq('moderator_id', moderatorId);
    }

    if (startDate) {
      query = query.gte('created_at', startDate);
    }

    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching moderation actions:', error);
      return { data: [], count: 0, error: error.message };
    }

    return { data: data || [], count: count || 0 };
  } catch (err) {
    console.error('Exception fetching moderation actions:', err);
    return { data: [], count: 0, error: err instanceof Error ? err.message : 'Unknown error' };
  }
}

// Get moderation action details
export async function getModerationActionDetails(actionId: string) {
  const supabase = createClient();

  try {
    console.log('Fetching moderation action details for ID:', actionId);

    const { data, error } = await supabase
      .from('moderation_actions')
      .select(`
        *,
        moderator:moderator_id (id, username, full_name, avatar_url, role)
      `)
      .eq('id', actionId)
      .maybeSingle(); // Use maybeSingle instead of single to handle case where action doesn't exist

    if (error) {
      console.error('Error fetching moderation action details:', error);
      throw error;
    }

    if (!data) {
      console.log('No moderation action found with ID:', actionId);
      return null; // Return null if no action found
    }

    console.log('Found moderation action:', data.id);

    // Get additional content details based on content type
    let contentDetails = null;

    try {
      if (data.content_type === 'forum_post') {
        const { data: post } = await supabase
          .from('forum_posts')
          .select(`
            *,
            author:author_id (username, full_name, avatar_url),
            topic:topic_id (title, slug)
          `)
          .eq('id', data.content_id)
          .maybeSingle();

        contentDetails = post;
      } else if (data.content_type === 'forum_topic') {
        const { data: topic } = await supabase
          .from('forum_topics')
          .select(`
            *,
            author:author_id (username, full_name, avatar_url),
            category:category_id (name, slug)
          `)
          .eq('id', data.content_id)
          .maybeSingle();

        contentDetails = topic;
      } else if (data.content_type === 'user') {
        const { data: user } = await supabase
          .from('profiles')
          .select('id, username, full_name, avatar_url, email, role, is_banned, ban_expires_at')
          .eq('id', data.content_id)
          .maybeSingle();

        contentDetails = user;
      } else if (data.content_type === 'forum_category') {
        const { data: category } = await supabase
          .from('forum_categories')
          .select('*')
          .eq('id', data.content_id)
          .maybeSingle();

        contentDetails = category;
      }
    } catch (err) {
      console.error('Error fetching content details:', err);
      // Don't throw, just return the action without content details
    }

    return { ...data, contentDetails };
  } catch (err) {
    console.error('Exception in getModerationActionDetails:', err);
    throw err;
  }
}
