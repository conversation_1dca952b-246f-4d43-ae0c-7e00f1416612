import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Helper function to handle CORS
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}

// Check if user is admin
async function isAdmin(supabase: any) {
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error getting user or user not found:', userError);
      return false;
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      console.error('Error getting profile or profile not found:', profileError);
      return false;
    }

    return profile.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// POST /api/admin/category-manager - Create, update, or delete a category
export async function POST(request: NextRequest) {
  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json(
        { error: 'Missing Supabase environment variables' },
        { status: 500, headers: corsHeaders() }
      );
    }

    // Create authenticated Supabase client
    const cookieStore = cookies();
    const supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false,
        cookies: {
          get(key) {
            return cookieStore.get(key)?.value;
          }
        }
      }
    });

    // Check if user is admin
    const admin = await isAdmin(supabase);
    if (!admin) {
      return NextResponse.json(
        { error: 'Unauthorized: Only admins can manage categories' },
        { status: 401, headers: corsHeaders() }
      );
    }

    // Get request body
    const body = await request.json();
    const { action, id, name, description, parentId, icon } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400, headers: corsHeaders() }
      );
    }

    // Use the admin_manage_category function to bypass RLS
    const { data, error } = await supabase.rpc('admin_manage_category', {
      p_action: action,
      p_id: id,
      p_name: name,
      p_description: description,
      p_parent_id: parentId,
      p_icon: icon
    });

    if (error) {
      console.error(`Error ${action} category:`, error);
      // If RPC fails, return the error. The RPC is designed to handle admin operations.
      return NextResponse.json(
        { error: `Failed to ${action} category using RPC: ` + error.message },
        { status: 500, headers: corsHeaders() }
      );
    }

    // Add cache control headers to prevent caching
    const headers = {
      ...corsHeaders(),
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Surrogate-Control': 'no-store',
    };

    return NextResponse.json(data, { headers });
  } catch (error: any) {
    console.error('Error in category manager API:', error);
    return NextResponse.json(
      { error: 'Internal server error: ' + error.message },
      { status: 500, headers: corsHeaders() }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders() });
}
