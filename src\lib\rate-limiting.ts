import { createClient } from '@/lib/supabase';

// Rate limiting configuration
const RATE_LIMITS = {
  // Forum actions
  'forum_post_create': { window: 60, max: 10 }, // 10 posts per minute
  'forum_topic_create': { window: 300, max: 5 }, // 5 topics per 5 minutes
  'report_content': { window: 600, max: 5 }, // 5 reports per 10 minutes
  
  // Profile actions
  'verification_request': { window: 86400, max: 1 }, // 1 verification request per day
  
  // Search actions
  'search': { window: 60, max: 10 }, // 10 searches per minute
};

type ActionType = keyof typeof RATE_LIMITS;

/**
 * Check if a user has exceeded their rate limit for a specific action
 * @param userId The user's ID
 * @param action The action being performed
 * @returns An object with isLimited (boolean) and resetTime (Date) if limited
 */
export async function checkRateLimit(userId: string, action: ActionType): Promise<{ isLimited: boolean; resetTime?: Date }> {
  const supabase = createClient();
  
  // Get the rate limit configuration for this action
  const config = RATE_LIMITS[action];
  if (!config) {
    // If no configuration exists, don't rate limit
    return { isLimited: false };
  }
  
  // Get current timestamp
  const now = Math.floor(Date.now() / 1000);
  
  // Calculate the start of the current window
  const windowStart = now - config.window;
  
  try {
    // Check if a rate_limits table exists, if not create it
    const { error: tableCheckError } = await supabase
      .from('rate_limits')
      .select('count', { count: 'exact', head: true });
    
    if (tableCheckError) {
      // Table doesn't exist, create it
      await supabase.rpc('create_rate_limits_table');
    }
    
    // Get count of actions in the current window
    const { count, error } = await supabase
      .from('rate_limits')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .eq('action', action)
      .gte('timestamp', windowStart);
    
    if (error) {
      console.error('Error checking rate limit:', error);
      // On error, allow the action to proceed
      return { isLimited: false };
    }
    
    // Check if the user has exceeded their limit
    if ((count || 0) >= config.max) {
      // Calculate when the rate limit will reset
      const oldestActionTimestamp = await getOldestActionTimestamp(userId, action);
      const resetTime = oldestActionTimestamp 
        ? new Date((oldestActionTimestamp + config.window) * 1000)
        : new Date((now + config.window) * 1000);
      
      return { isLimited: true, resetTime };
    }
    
    // Record this action
    await recordAction(userId, action);
    
    return { isLimited: false };
  } catch (err) {
    console.error('Rate limiting error:', err);
    // On error, allow the action to proceed
    return { isLimited: false };
  }
}

/**
 * Record an action for rate limiting purposes
 */
async function recordAction(userId: string, action: ActionType): Promise<void> {
  const supabase = createClient();
  
  const timestamp = Math.floor(Date.now() / 1000);
  
  await supabase
    .from('rate_limits')
    .insert({
      user_id: userId,
      action,
      timestamp
    });
}

/**
 * Get the timestamp of the oldest action in the current window
 */
async function getOldestActionTimestamp(userId: string, action: ActionType): Promise<number | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('rate_limits')
    .select('timestamp')
    .eq('user_id', userId)
    .eq('action', action)
    .order('timestamp', { ascending: true })
    .limit(1)
    .single();
  
  if (error || !data) {
    return null;
  }
  
  return data.timestamp;
}

/**
 * Format a rate limit error message
 */
export function formatRateLimitMessage(action: ActionType, resetTime?: Date): string {
  const config = RATE_LIMITS[action];
  if (!config) return 'Too many requests. Please try again later.';
  
  let timeRemaining = '';
  if (resetTime) {
    const seconds = Math.ceil((resetTime.getTime() - Date.now()) / 1000);
    if (seconds < 60) {
      timeRemaining = `${seconds} second${seconds !== 1 ? 's' : ''}`;
    } else if (seconds < 3600) {
      const minutes = Math.ceil(seconds / 60);
      timeRemaining = `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    } else {
      const hours = Math.ceil(seconds / 3600);
      timeRemaining = `${hours} hour${hours !== 1 ? 's' : ''}`;
    }
  }
  
  const actionMessages = {
    'forum_post_create': `You can only create ${config.max} posts per ${config.window / 60} minutes.`,
    'forum_topic_create': `You can only create ${config.max} topics per ${config.window / 60} minutes.`,
    'report_content': `You can only submit ${config.max} reports per ${config.window / 60} minutes.`,
    'verification_request': `You can only submit ${config.max} verification request per day.`,
    'search': `You can only perform ${config.max} searches per minute.`
  };
  
  const message = actionMessages[action] || `Rate limit exceeded for ${action}.`;
  
  return timeRemaining 
    ? `${message} Please try again in ${timeRemaining}.`
    : `${message} Please try again later.`;
}
