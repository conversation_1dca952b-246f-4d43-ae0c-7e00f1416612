# Updated Moderation System Database Migrations

This directory contains updated SQL migrations for setting up the moderation system database tables and policies. These scripts have been modified to handle existing tables and avoid conflicts.

## Migration Files

1. `20240101000000_check_existing_tables.sql` - Checks the structure of existing tables (optional, for troubleshooting)
2. `20240101000001_create_moderation_tables.sql` - Creates the necessary tables for the moderation system if they don't exist
3. `20240101000002_create_moderation_rls_policies.sql` - Sets up Row Level Security policies for the tables
4. `20240101000003_add_sample_moderation_data.sql` - Adds sample data for testing (optional)
5. `20240101000004_update_profiles_for_bans.sql` - Updates the profiles table to add ban-related fields

## How to Apply Migrations

### Option 1: Using Supabase CLI

If you have the Supabase CLI installed, you can apply these migrations with:

```bash
supabase db push
```

### Option 2: Using Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of each migration file
4. Execute them in order (1-5)

## Troubleshooting

If you encounter any issues during migration:

1. Run the `20240101000000_check_existing_tables.sql` script to check the structure of existing tables
2. Look for any error messages in the SQL output
3. Check if the tables already exist and have a different structure
4. If needed, manually adjust the table structures to match the expected schema

## Tables Created or Modified

1. `user_bans` - Stores information about banned users
2. `reported_content` - Stores reports of inappropriate content
3. `moderation_actions` - Logs all moderation actions taken by moderators
4. `auto_moderation_logs` - Logs automatic content filtering actions
5. `user_notifications` - Stores notifications for users
6. `profiles` - Updated to add ban-related fields

## Changes Made to Original Scripts

The scripts have been updated to:

1. Check if tables already exist before creating them
2. Check if columns already exist before adding them
3. Check if policies already exist before creating them
4. Handle existing tables with different structures
5. Use DO blocks to make the scripts more robust

These changes ensure that the migrations can be applied safely to an existing database without causing conflicts or errors.
