'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/components/AuthProvider';

export default function AdminUnauthorizedPage() {
  const { user } = useAuth();

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="bg-red-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Access Denied</h1>
        
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 text-left">
          <p className="text-red-700">
            You don't have permission to access the admin area. This area is restricted to administrators and moderators only.
          </p>
        </div>
        
        {user ? (
          <div className="mb-6 text-gray-600">
            <p>You are currently signed in as:</p>
            <p className="font-medium">{user.email}</p>
            <p className="text-sm mt-2">
              If you believe you should have access to this area, please contact the site administrator.
            </p>
          </div>
        ) : (
          <div className="mb-6 text-gray-600">
            <p>You are not currently signed in.</p>
            <p className="text-sm mt-2">
              If you have an administrator account, please sign in to access this area.
            </p>
          </div>
        )}
        
        <div className="flex flex-col sm:flex-row justify-center gap-4 mt-8">
          <Link 
            href="/"
            className="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-6 rounded-md transition-colors"
          >
            Return to Homepage
          </Link>
          
          {!user && (
            <Link 
              href="/auth/signin"
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-md transition-colors"
            >
              Sign In
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
