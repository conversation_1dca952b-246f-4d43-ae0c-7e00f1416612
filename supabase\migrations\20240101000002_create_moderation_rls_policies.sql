-- Enable Row Level Security on all tables if they exist
DO $$
BEGIN
    -- Enable RLS on user_bans if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_bans') THEN
        ALTER TABLE public.user_bans ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on reported_content if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'reported_content') THEN
        ALTER TABLE public.reported_content ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on moderation_actions if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'moderation_actions') THEN
        ALTER TABLE public.moderation_actions ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on auto_moderation_logs if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'auto_moderation_logs') THEN
        ALTER TABLE public.auto_moderation_logs ENABLE ROW LEVEL SECURITY;
    END IF;

    -- Enable RLS on user_notifications if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_notifications') THEN
        ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Create policies for user_bans table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_bans') THEN
        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_bans' AND policyname = 'Admins and moderators can view all bans') THEN
            -- Admins and moderators can view all bans
            CREATE POLICY "Admins and moderators can view all bans"
            ON public.user_bans FOR SELECT
            TO authenticated
            USING (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_bans' AND policyname = 'Users can view their own bans') THEN
            -- Users can view their own bans
            CREATE POLICY "Users can view their own bans"
            ON public.user_bans FOR SELECT
            TO authenticated
            USING (user_id = auth.uid());
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_bans' AND policyname = 'Only admins and moderators can create bans') THEN
            -- Only admins and moderators can create bans
            CREATE POLICY "Only admins and moderators can create bans"
            ON public.user_bans FOR INSERT
            TO authenticated
            WITH CHECK (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_bans' AND policyname = 'Only admins and moderators can update bans') THEN
            -- Only admins and moderators can update bans
            CREATE POLICY "Only admins and moderators can update bans"
            ON public.user_bans FOR UPDATE
            TO authenticated
            USING (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;
    END IF;
END $$;

-- Create policies for reported_content table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'reported_content') THEN
        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'reported_content' AND policyname = 'Admins and moderators can view all reports') THEN
            -- Admins and moderators can view all reports
            CREATE POLICY "Admins and moderators can view all reports"
            ON public.reported_content FOR SELECT
            TO authenticated
            USING (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'reported_content' AND policyname = 'Users can view their own reports') THEN
            -- Users can view their own reports
            CREATE POLICY "Users can view their own reports"
            ON public.reported_content FOR SELECT
            TO authenticated
            USING (reporter_id = auth.uid());
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'reported_content' AND policyname = 'Any authenticated user can create reports') THEN
            -- Any authenticated user can create reports
            CREATE POLICY "Any authenticated user can create reports"
            ON public.reported_content FOR INSERT
            TO authenticated
            WITH CHECK (reporter_id = auth.uid());
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'reported_content' AND policyname = 'Only admins and moderators can update reports') THEN
            -- Only admins and moderators can update reports
            CREATE POLICY "Only admins and moderators can update reports"
            ON public.reported_content FOR UPDATE
            TO authenticated
            USING (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;
    END IF;
END $$;

-- Create policies for moderation_actions table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'moderation_actions') THEN
        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'moderation_actions' AND policyname = 'Admins and moderators can view all moderation actions') THEN
            -- Admins and moderators can view all moderation actions
            CREATE POLICY "Admins and moderators can view all moderation actions"
            ON public.moderation_actions FOR SELECT
            TO authenticated
            USING (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'moderation_actions' AND policyname = 'Only admins and moderators can create moderation actions') THEN
            -- Only admins and moderators can create moderation actions
            CREATE POLICY "Only admins and moderators can create moderation actions"
            ON public.moderation_actions FOR INSERT
            TO authenticated
            WITH CHECK (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;
    END IF;
END $$;

-- Create policies for auto_moderation_logs table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'auto_moderation_logs') THEN
        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'auto_moderation_logs' AND policyname = 'Admins and moderators can view all auto moderation logs') THEN
            -- Admins and moderators can view all auto moderation logs
            CREATE POLICY "Admins and moderators can view all auto moderation logs"
            ON public.auto_moderation_logs FOR SELECT
            TO authenticated
            USING (
              EXISTS (
                SELECT 1 FROM public.profiles
                WHERE profiles.id = auth.uid()
                AND (profiles.role = 'admin' OR profiles.role = 'moderator')
              )
            );
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'auto_moderation_logs' AND policyname = 'System can create auto moderation logs') THEN
            -- System can create auto moderation logs (no RLS needed for insert as it's done by the system)
            CREATE POLICY "System can create auto moderation logs"
            ON public.auto_moderation_logs FOR INSERT
            TO authenticated;
        END IF;
    END IF;
END $$;

-- Create policies for user_notifications table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_notifications') THEN
        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_notifications' AND policyname = 'Users can view their own notifications') THEN
            -- Users can view their own notifications
            CREATE POLICY "Users can view their own notifications"
            ON public.user_notifications FOR SELECT
            TO authenticated
            USING (user_id = auth.uid());
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_notifications' AND policyname = 'System can create notifications') THEN
            -- System can create notifications (no RLS needed for insert as it's done by the system)
            CREATE POLICY "System can create notifications"
            ON public.user_notifications FOR INSERT
            TO authenticated;
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_notifications' AND policyname = 'Users can update their own notifications') THEN
            -- Users can update their own notifications (to mark as read)
            CREATE POLICY "Users can update their own notifications"
            ON public.user_notifications FOR UPDATE
            TO authenticated
            USING (user_id = auth.uid());
        END IF;

        -- Check if policy already exists
        IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'user_notifications' AND policyname = 'Users can delete their own notifications') THEN
            -- Users can delete their own notifications
            CREATE POLICY "Users can delete their own notifications"
            ON public.user_notifications FOR DELETE
            TO authenticated
            USING (user_id = auth.uid());
        END IF;
    END IF;
END $$;
