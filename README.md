# NatureHeals.info - Holistic Healing Wiki

A community-driven knowledge base for holistic healing, plant-based medicines, and natural treatments.

## Features

### Core Features
- Wiki-style knowledge base for holistic healing information
- User contribution system for articles, images, and videos
- Moderation tools for content quality control
- Responsive design with Tailwind CSS
- Authentication and user profiles
- Search functionality
- Categorization and tagging system

### New Features
- **Bookmarks**: Save articles to read later
- **Social Sharing**: Share articles on social media platforms
- **Notifications**: Get notified about interactions with your content
- **User Activity Feed**: See what's happening in the community
- **Analytics Dashboard**: Track site usage and performance (admin only)


## Tech Stack

- **Frontend**: Next.js with App Router, React, Tailwind CSS
- **Backend**: Supabase (PostgreSQL database, authentication, storage)
- **Styling**: Tailwind CSS with Typography plugin
- **Content**: Markdown support via react-markdown
- **Charts**: Chart.js with react-chartjs-2
- **Date Handling**: date-fns
- **Utilities**: remark, remark-html, unified

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account (free tier available)

### Setup

1. Clone the repository

```bash
git clone https://github.com/yourusername/natureheals.git
cd natureheals
```

2. Install dependencies

```bash
npm install
```

3. Set up Supabase

   - Create a new project on [Supabase](https://supabase.com)
   - Go to Project Settings > API to get your project URL, anon key, and service role key
   - Run the SQL from `supabase/schema.sql` in the Supabase SQL Editor
   - Apply database migrations by running `node scripts/apply-migrations.js`
   - Apply new features migrations by running `npm run apply-migrations`

4. Configure environment variables

   - Copy `.env.local.example` to `.env.local`
   - Update with your Supabase URL and anon key

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
```

5. Run the development server

```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) with your browser

## Project Structure

```
├── public/             # Static assets
│   ├── icons/          # App icons and PWA assets
│   ├── manifest.json   # PWA manifest
│   └── sw.js           # Service Worker for offline support
├── src/
│   ├── app/            # Next.js App Router pages
│   │   ├── admin/      # Admin dashboard and analytics
│   │   ├── auth/       # Authentication pages
│   │   ├── bookmarks/  # User bookmarks
│   │   ├── contribute/ # Contribution pages
│   │   ├── notifications/ # User notifications
│   │   ├── offline/    # Offline fallback page
│   │   ├── profile/    # User profiles
│   │   ├── settings/   # User settings
│   │   ├── wiki/       # Wiki pages
│   │   └── ...         # Other pages
│   ├── components/     # React components
│   │   ├── BookmarkButton.tsx      # Bookmark functionality
│   │   ├── NotificationsDropdown.tsx # Notifications UI
│   │   ├── SocialShareButtons.tsx  # Social sharing
│   │   ├── ThemeProvider.tsx       # Dark mode provider
│   │   ├── ThemeToggle.tsx         # Dark mode toggle
│   │   ├── UserActivityFeed.tsx    # Activity feed
│   │   └── ...                     # Other components
│   └── lib/            # Utility functions and API
│       ├── hooks.ts    # Custom React hooks
│       └── ...         # Other utilities
├── supabase/           # Supabase configuration
│   └── migrations/     # Database migrations
├── scripts/            # Utility scripts
└── ...                 # Config files
```

## Deployment

The easiest way to deploy this application is using Vercel:

1. Push your code to a GitHub repository
2. Import the project to Vercel
3. Add your environment variables
4. Deploy

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
