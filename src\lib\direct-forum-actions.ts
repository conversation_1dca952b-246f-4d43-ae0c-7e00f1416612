import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

/**
 * Directly pin or unpin a topic in the database
 */
export async function directPinTopic({
  topicId,
  isPinned,
  moderatorId,
  reason
}: {
  topicId: string;
  isPinned: boolean;
  moderatorId: string;
  reason?: string;
}) {
  try {
    console.log(`Directly ${isPinned ? 'pinning' : 'unpinning'} topic ${topicId}`);
    const supabase = createClientComponentClient();
    
    // First check if the topic exists
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('id, is_pinned')
      .eq('id', topicId)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`
      };
    }
    
    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`
      };
    }
    
    // If already in the desired state, just return success
    if (topic.is_pinned === isPinned) {
      console.log(`Topic ${topicId} is already ${isPinned ? 'pinned' : 'unpinned'}`);
      return {
        success: true,
        message: `Topic is already ${isPinned ? 'pinned' : 'unpinned'}`
      };
    }
    
    // Update the topic
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_pinned: isPinned,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);
    
    if (updateError) {
      console.error('Error updating topic pin status:', updateError);
      return {
        success: false,
        error: `Error updating topic pin status: ${updateError.message}`
      };
    }
    
    // Log the moderation action
    const { error: logError } = await supabase
      .from('moderation_actions')
      .insert({
        action: isPinned ? 'pin_topic' : 'unpin_topic',
        action_type: isPinned ? 'pin_topic' : 'unpin_topic',
        moderator_id: moderatorId,
        content_id: topicId,
        content_type: 'forum_topic',
        reason: reason || (isPinned ? 'Topic pinned by moderator' : 'Topic unpinned by moderator'),
        created_at: new Date().toISOString()
      });
    
    if (logError) {
      console.error('Error logging moderation action:', logError);
      // Don't fail the operation if logging fails
    }
    
    return {
      success: true,
      message: `Topic ${isPinned ? 'pinned' : 'unpinned'} successfully`
    };
  } catch (err) {
    console.error('Exception in directPinTopic:', err);
    return {
      success: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}

/**
 * Directly lock or unlock a topic in the database
 */
export async function directLockTopic({
  topicId,
  isLocked,
  moderatorId,
  reason
}: {
  topicId: string;
  isLocked: boolean;
  moderatorId: string;
  reason?: string;
}) {
  try {
    console.log(`Directly ${isLocked ? 'locking' : 'unlocking'} topic ${topicId}`);
    const supabase = createClientComponentClient();
    
    // First check if the topic exists
    const { data: topic, error: checkError } = await supabase
      .from('forum_topics')
      .select('id, is_locked')
      .eq('id', topicId)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking if topic exists:', checkError);
      return {
        success: false,
        error: `Error checking if topic exists: ${checkError.message}`
      };
    }
    
    if (!topic) {
      console.error(`Topic with ID ${topicId} not found`);
      return {
        success: false,
        error: `Topic with ID ${topicId} not found`
      };
    }
    
    // If already in the desired state, just return success
    if (topic.is_locked === isLocked) {
      console.log(`Topic ${topicId} is already ${isLocked ? 'locked' : 'unlocked'}`);
      return {
        success: true,
        message: `Topic is already ${isLocked ? 'locked' : 'unlocked'}`
      };
    }
    
    // Update the topic
    const { error: updateError } = await supabase
      .from('forum_topics')
      .update({
        is_locked: isLocked,
        updated_at: new Date().toISOString()
      })
      .eq('id', topicId);
    
    if (updateError) {
      console.error('Error updating topic lock status:', updateError);
      return {
        success: false,
        error: `Error updating topic lock status: ${updateError.message}`
      };
    }
    
    // Log the moderation action
    const { error: logError } = await supabase
      .from('moderation_actions')
      .insert({
        action: isLocked ? 'lock_topic' : 'unlock_topic',
        action_type: isLocked ? 'lock_topic' : 'unlock_topic',
        moderator_id: moderatorId,
        content_id: topicId,
        content_type: 'forum_topic',
        reason: reason || (isLocked ? 'Topic locked by moderator' : 'Topic unlocked by moderator'),
        created_at: new Date().toISOString()
      });
    
    if (logError) {
      console.error('Error logging moderation action:', logError);
      // Don't fail the operation if logging fails
    }
    
    return {
      success: true,
      message: `Topic ${isLocked ? 'locked' : 'unlocked'} successfully`
    };
  } catch (err) {
    console.error('Exception in directLockTopic:', err);
    return {
      success: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
}
