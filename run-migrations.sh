#!/bin/bash

# Make sure you have the Supabase CLI installed
# npm install -g supabase

# Set your Supabase project ID and database password
# You can find these in your Supabase dashboard
echo "Enter your Supabase project ID:"
read PROJECT_ID

echo "Enter your Supabase database password:"
read -s DB_PASSWORD

# Run the migrations
echo "Running migrations..."
supabase db push --project-ref $PROJECT_ID --password $DB_PASSWORD

echo "Migrations completed!"
