import { createClient } from '@supabase/supabase-js';

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    // Initialize Supabase client inside the handler
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase environment variables');
      return new Response(JSON.stringify({ error: 'Server configuration error' }), { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    const id = params.id;
    const { title, content, category } = await req.json();

    if (!title || !content || !category) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), { status: 400 });
    }

    const { data, error } = await supabase
      .from('posts')
      .update({
        title,
        content,
        category,
        // ... other fields
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error("Error updating post:", error);
      return new Response(JSON.stringify({ error: 'Failed to update post' }), { status: 500 });
    }

    return new Response(JSON.stringify({ data: data[0] }), { status: 200 });
  } catch (error) {
    console.error("Error in PUT handler:", error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), { status: 500 });
  }
}