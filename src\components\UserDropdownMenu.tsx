'use client'

import { useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useClickOutside } from '@/lib/hooks'

interface UserDropdownProps {
  user: any;
  supabase: any;
}

export default function UserDropdownMenu({ user, supabase }: UserDropdownProps) {
  const router = useRouter()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useClickOutside(menuRef, () => setIsMenuOpen(false))

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.refresh()
  }

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        className="flex items-center space-x-1 text-white hover:text-gray-200 transition-colors"
        aria-label="User menu"
      >
        <div className="w-8 h-8 rounded-full bg-white/30 flex items-center justify-center text-sm font-medium shadow-sm border border-white/20">
          {user.email?.charAt(0).toUpperCase() || 'U'}
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      {isMenuOpen && (
        <div
          className="user-dropdown-menu"
          style={{
            position: 'absolute',
            right: 0,
            marginTop: '8px',
            width: '12rem',
            backgroundColor: 'white',
            borderRadius: '6px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)',
            zIndex: 50,
            border: '1px solid rgba(229, 231, 235, 0.8)',
            overflow: 'hidden'
          }}
        >
          <div style={{ padding: '16px', borderBottom: '1px solid #e5e7eb' }}>
            <p className="header-text" style={{ fontSize: '14px', fontWeight: 500, color: '#666666', margin: 0 }}>Signed in as</p>
            <p className="email-text" style={{ fontWeight: 500, color: '#000000', margin: '4px 0 0 0', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{user.email}</p>
          </div>

          <Link
            href={`/profile/${user.username || user.id}`}
            onClick={() => setIsMenuOpen(false)}
            style={{
              display: 'block',
              padding: '8px 16px',
              fontSize: '14px',
              color: '#000000',
              fontWeight: 500,
              textDecoration: 'none'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '20px', width: '20px', marginRight: '8px', color: '#2e7d32' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span style={{ color: '#000000' }}>Profile</span>
            </div>
          </Link>

          <Link
            href="/bookmarks"
            onClick={() => setIsMenuOpen(false)}
            style={{
              display: 'block',
              padding: '8px 16px',
              fontSize: '14px',
              color: '#000000',
              fontWeight: 500,
              textDecoration: 'none'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '20px', width: '20px', marginRight: '8px', color: '#2e7d32' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
              </svg>
              <span style={{ color: '#000000' }}>Bookmarks</span>
            </div>
          </Link>

          <Link
            href="/notifications"
            onClick={() => setIsMenuOpen(false)}
            style={{
              display: 'block',
              padding: '8px 16px',
              fontSize: '14px',
              color: '#000000',
              fontWeight: 500,
              textDecoration: 'none'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '20px', width: '20px', marginRight: '8px', color: '#2e7d32' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              <span style={{ color: '#000000' }}>Notifications</span>
            </div>
          </Link>

          <Link
            href="/settings"
            onClick={() => setIsMenuOpen(false)}
            style={{
              display: 'block',
              padding: '8px 16px',
              fontSize: '14px',
              color: '#000000',
              fontWeight: 500,
              textDecoration: 'none'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '20px', width: '20px', marginRight: '8px', color: '#2e7d32' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span style={{ color: '#000000' }}>Settings</span>
            </div>
          </Link>

          {user.role === 'admin' && (
            <Link
              href="/admin"
              onClick={() => setIsMenuOpen(false)}
              style={{
                display: 'block',
                padding: '8px 16px',
                fontSize: '14px',
                color: '#000000',
                fontWeight: 500,
                textDecoration: 'none'
              }}
              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '20px', width: '20px', marginRight: '8px', color: '#2e7d32' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span style={{ color: '#000000' }}>Admin Dashboard</span>
              </div>
            </Link>
          )}

          <div style={{ borderTop: '1px solid #e5e7eb', paddingTop: '4px' }}>
            <button
              onClick={() => {
                handleSignOut();
                setIsMenuOpen(false);
              }}
              className="sign-out"
              style={{
                display: 'block',
                width: '100%',
                textAlign: 'left',
                padding: '8px 16px',
                fontSize: '14px',
                color: '#dc2626',
                fontWeight: 500,
                background: 'none',
                border: 'none',
                cursor: 'pointer'
              }}
              onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
              onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '20px', width: '20px', marginRight: '8px', color: '#dc2626' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span style={{ color: '#dc2626' }}>Sign Out</span>
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
