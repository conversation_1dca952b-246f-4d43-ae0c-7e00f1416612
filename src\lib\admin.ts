import { createClient } from '@supabase/supabase-js';

// Function to get admin dashboard stats
export async function getAdminStats() {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Get total articles count
  const { count: articlesCount, error: articlesError } = await supabase
    .from('articles')
    .select('*', { count: 'exact', head: true });

  // Get total users count
  const { count: usersCount, error: usersError } = await supabase
    .from('profiles')
    .select('*', { count: 'exact', head: true });

  // Get moderators count
  const { count: moderatorsCount, error: moderatorsError } = await supabase
    .from('profiles')
    .select('*', { count: 'exact', head: true })
    .eq('role', 'moderator');

  // Get pending reviews count
  const { count: pendingCount, error: pendingError } = await supabase
    .from('articles')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending');

  // Get new articles this week
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  const { count: newArticlesCount, error: newArticlesError } = await supabase
    .from('articles')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', oneWeekAgo.toISOString());

  // Get new users this week
  const { count: newUsersCount, error: newUsersError } = await supabase
    .from('profiles')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', oneWeekAgo.toISOString());

  return {
    articles: {
      total: articlesCount || 0,
      new: newArticlesCount || 0,
      error: articlesError || newArticlesError
    },
    users: {
      total: usersCount || 0,
      new: newUsersCount || 0,
      error: usersError || newUsersError
    },
    moderators: {
      total: moderatorsCount || 0,
      error: moderatorsError
    },
    pending: {
      total: pendingCount || 0,
      error: pendingError
    }
  };
}

// Function to get pending content for review
export async function getPendingContent(limit = 10, offset = 0, type = 'all') {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  let query = supabase
    .from('articles')
    .select(`
      id,
      title, // Keep title for display
      status,
      created_at,
      category,
      profiles(username, full_name)
    `)
    .eq('status', 'pending')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (type !== 'all' && type !== 'article') {
    // If specific type is requested and it's not 'article', return empty result
    // In a real app, you'd have different tables for different content types
    return { data: [], count: 0, error: null };
  }

  const { data, error } = await query;

  // Get total count for pagination
  const { count, error: countError } = await supabase
    .from('articles')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending');

  return {
    data: data || [],
    count: count || 0,
    error: error || countError
  };
}

// Function to get recent moderator activity
export async function getRecentActivity(limit = 5) {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const { data, error } = await supabase
    .from('activity_log')
    .select(`
      id,
      action,
      created_at,
      content_id,
      content_type,
      content_title,
      profiles(username, full_name, role)
    `)
    .order('created_at', { ascending: false })
    .limit(limit);

  return {
    data: data || [],
    error
  };
}
