'use client'

import AuthProvider from './AuthProvider'
import { User } from '@supabase/supabase-js'
import { Suspense } from 'react'

export default function ClientAuthProvider({
  children,
  initialUser
}: {
  children: React.ReactNode
  initialUser: User | null
}) {
  // Wrap in Suspense to prevent blocking rendering
  return (
    <Suspense fallback={null}>
      <AuthProvider initialUser={initialUser}>
        {children}
      </AuthProvider>
    </Suspense>
  )
}
