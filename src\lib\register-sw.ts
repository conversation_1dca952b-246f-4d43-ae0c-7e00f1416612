/**
 * Register the service worker for offline support and PWA functionality
 */
export function registerServiceWorker() {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('Service Worker registered with scope:', registration.scope);
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error);
        });
    });
  }
}

/**
 * Check if the app is installed (in standalone mode)
 */
export function isAppInstalled(): boolean {
  if (typeof window !== 'undefined') {
    // Check if the app is in standalone mode (installed)
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  }
  return false;
}

/**
 * Show install prompt for PWA
 */
export function showInstallPrompt() {
  if (typeof window !== 'undefined') {
    // Get the stored install prompt event
    const promptEvent = (window as any).deferredPrompt;
    
    if (promptEvent) {
      // Show the install prompt
      promptEvent.prompt();
      
      // Wait for the user to respond to the prompt
      promptEvent.userChoice.then((choiceResult: { outcome: string }) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('User accepted the install prompt');
        } else {
          console.log('User dismissed the install prompt');
        }
        
        // Clear the stored prompt event
        (window as any).deferredPrompt = null;
      });
    }
  }
}
