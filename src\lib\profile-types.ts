export type ProfileVisibility = 'public' | 'members' | 'private';

export type HerbalInterest =
  | 'medicinal_herbs'
  | 'aromatherapy'
  | 'traditional_medicine'
  | 'ayurveda'
  | 'chinese_medicine'
  | 'homeopathy'
  | 'naturopathy'
  | 'foraging'
  | 'gardening'
  | 'herbalism'
  | 'nutrition'
  | 'essential_oils'
  | 'holistic_health'
  | 'sustainable_living';

export type AvatarType = 'uploaded' | 'preset' | 'gravatar';

export interface UserProfile {
  id: string;
  username: string;
  full_name: string;
  email?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatar_url?: string;
  avatar_type?: AvatarType;
  preset_avatar?: string;
  visibility: ProfileVisibility;
  show_email: boolean;
  show_location: boolean;
  interests?: HerbalInterest[];
  expertise_level?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  healing_philosophy?: string;
  favorite_herbs?: string[];
  social_links?: {
    twitter?: string;
    instagram?: string;
    facebook?: string;
    pinterest?: string;
    youtube?: string;
  };
  spirit_plant?: string;
  created_at: string;
  updated_at: string;
  role: 'user' | 'moderator' | 'admin';
  contribution_stats?: {
    articles: number;
    comments: number;
    likes_received: number;
    forum_topics?: number;
    forum_posts?: number;
  };
  badges?: string[];
  reputation_points?: number;
  level?: number;
  is_verified_expert?: boolean;
}

export interface AvatarCategory {
  name: string;
  avatars: string[];
}

export const AVATAR_CATEGORIES: AvatarCategory[] = [
  {
    name: '3D Style',
    avatars: ['3d_1', '3d_2', '3d_3', '3d_4', '3d_5']
  },
  {
    name: 'Bluey',
    avatars: ['bluey_1', 'bluey_2', 'bluey_3', 'bluey_4', 'bluey_5', 'bluey_6', 'bluey_7', 'bluey_8', 'bluey_9', 'bluey_10']
  },
  {
    name: 'Memo',
    avatars: ['memo_1', 'memo_2', 'memo_3', 'memo_4', 'memo_5', 'memo_6', 'memo_7', 'memo_8', 'memo_9', 'memo_10']
  },
  {
    name: 'Notion',
    avatars: ['notion_1', 'notion_2', 'notion_3', 'notion_4', 'notion_5', 'notion_6', 'notion_7', 'notion_8', 'notion_9', 'notion_10']
  },
  {
    name: 'Teams',
    avatars: ['teams_1', 'teams_2', 'teams_3', 'teams_4', 'teams_5', 'teams_6', 'teams_7', 'teams_8', 'teams_9']
  },
  {
    name: 'Toon',
    avatars: ['toon_1', 'toon_2', 'toon_3', 'toon_4', 'toon_5', 'toon_6', 'toon_7', 'toon_8', 'toon_9', 'toon_10']
  },
  {
    name: 'Vibrent',
    avatars: ['vibrent_1', 'vibrent_2', 'vibrent_3', 'vibrent_4', 'vibrent_5', 'vibrent_6', 'vibrent_7', 'vibrent_8', 'vibrent_9', 'vibrent_10']
  }
];

// Flat list of all preset avatars for backward compatibility
export const PRESET_AVATARS = AVATAR_CATEGORIES.flatMap(category => category.avatars);

export const INTEREST_LABELS: Record<HerbalInterest, string> = {
  medicinal_herbs: 'Medicinal Herbs',
  aromatherapy: 'Aromatherapy',
  traditional_medicine: 'Traditional Medicine',
  ayurveda: 'Ayurveda',
  chinese_medicine: 'Chinese Medicine',
  homeopathy: 'Homeopathy',
  naturopathy: 'Naturopathy',
  foraging: 'Foraging',
  gardening: 'Gardening',
  herbalism: 'Herbalism',
  nutrition: 'Nutrition',
  essential_oils: 'Essential Oils',
  holistic_health: 'Holistic Health',
  sustainable_living: 'Sustainable Living'
};

export const BADGES = {
  new_member: {
    name: 'New Sprout',
    description: 'Recently joined our community',
    icon: '🌱'
  },
  contributor: {
    name: 'Knowledge Cultivator',
    description: 'Contributed valuable content to our knowledge base',
    icon: '📚'
  },
  commenter: {
    name: 'Community Gardener',
    description: 'Actively engages in discussions',
    icon: '💬'
  },
  herbalist: {
    name: 'Herbal Sage',
    description: 'Demonstrated extensive knowledge of herbs',
    icon: '🌿'
  },
  verified: {
    name: 'Verified Practitioner',
    description: 'Verified healthcare or herbal practitioner',
    icon: '✅'
  },
  top_contributor: {
    name: 'Master Herbalist',
    description: 'Among our top content contributors',
    icon: '🏆'
  },
  moderator: {
    name: 'Community Guardian',
    description: 'Helps maintain the quality of our community',
    icon: '🛡️'
  },
  level_5: {
    name: 'Healing Adept',
    description: 'Reached level 5 in the community',
    icon: '⭐'
  },
  level_10: {
    name: 'Healing Master',
    description: 'Reached level 10 in the community',
    icon: '🌟'
  },
  level_20: {
    name: 'Healing Sage',
    description: 'Reached level 20 in the community',
    icon: '✨'
  },
  forum_contributor: {
    name: 'Discussion Cultivator',
    description: 'Active participant in forum discussions',
    icon: '🗣️'
  },
  solution_provider: {
    name: 'Solution Provider',
    description: 'Provided helpful solutions in the forums',
    icon: '💡'
  }
};

export const LEVEL_THRESHOLDS = [
  { level: 1, points: 0, title: 'Seedling' },
  { level: 2, points: 10, title: 'Sprout' },
  { level: 3, points: 30, title: 'Sapling' },
  { level: 4, points: 60, title: 'Young Plant' },
  { level: 5, points: 100, title: 'Blooming Plant' },
  { level: 6, points: 150, title: 'Mature Plant' },
  { level: 7, points: 210, title: 'Herbal Novice' },
  { level: 8, points: 280, title: 'Herbal Student' },
  { level: 9, points: 360, title: 'Herbal Apprentice' },
  { level: 10, points: 450, title: 'Herbal Practitioner' },
  { level: 15, points: 1000, title: 'Herbal Healer' },
  { level: 20, points: 2000, title: 'Herbal Master' },
  { level: 30, points: 5000, title: 'Herbal Sage' },
  { level: 50, points: 10000, title: 'Legendary Herbalist' }
];

export interface ExpertVerification {
  id: string;
  user_id: string;
  credentials: string;
  specialty: string;
  verification_documents: string[];
  status: 'pending' | 'approved' | 'rejected';
  reviewer_id?: string;
  review_notes?: string;
  created_at: string;
  updated_at: string;
}
