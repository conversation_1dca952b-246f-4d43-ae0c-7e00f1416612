'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { getVerificationRequestByUserId, uploadVerificationDocument, createVerificationRequest } from '@/lib/verification';
import { ExpertVerification } from '@/lib/profile-types';

export default function VerificationRequestPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [verificationRequest, setVerificationRequest] = useState<ExpertVerification | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form state
  const [credentials, setCredentials] = useState('');
  const [specialty, setSpecialty] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);
  
  useEffect(() => {
    async function loadVerificationRequest() {
      if (loading) return;
      
      if (!user) {
        router.push('/auth/signin?redirect=/profile/verification');
        return;
      }
      
      try {
        const request = await getVerificationRequestByUserId(user.id);
        setVerificationRequest(request);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    }
    
    loadVerificationRequest();
  }, [user, loading, router]);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const fileList = Array.from(e.target.files);
      setFiles(prev => [...prev, ...fileList]);
    }
  };
  
  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('You must be logged in to submit a verification request');
      return;
    }
    
    if (!credentials || !specialty || files.length === 0) {
      setError('Please fill in all required fields and upload at least one document');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Upload files
      const urls = [];
      for (const file of files) {
        const url = await uploadVerificationDocument(file, user.id);
        urls.push(url);
      }
      
      setUploadedUrls(urls);
      
      // Create verification request
      await createVerificationRequest({
        userId: user.id,
        credentials,
        specialty,
        documents: urls
      });
      
      setSuccess('Your verification request has been submitted successfully. We will review it and get back to you soon.');
      
      // Reload the verification request
      const request = await getVerificationRequestByUserId(user.id);
      setVerificationRequest(request);
      
      // Reset form
      setCredentials('');
      setSpecialty('');
      setFiles([]);
      setUploadedUrls([]);
    } catch (err: any) {
      setError(err.message || 'An error occurred while submitting your verification request');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Expert Verification</h1>
          <div className="bg-white rounded-xl shadow-md p-6">
            <p className="text-gray-500">Loading...</p>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Expert Verification</h1>
        
        {verificationRequest ? (
          <div className="bg-white rounded-xl shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Verification Status</h2>
            
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <span className="font-medium text-gray-700">Status:</span>
                <span className={`ml-2 px-2 py-1 rounded-full text-sm ${
                  verificationRequest.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  verificationRequest.status === 'approved' ? 'bg-green-100 text-green-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {verificationRequest.status.charAt(0).toUpperCase() + verificationRequest.status.slice(1)}
                </span>
              </div>
              
              <div className="mb-2">
                <span className="font-medium text-gray-700">Credentials:</span>
                <p className="mt-1 text-gray-600">{verificationRequest.credentials}</p>
              </div>
              
              <div className="mb-2">
                <span className="font-medium text-gray-700">Specialty:</span>
                <p className="mt-1 text-gray-600">{verificationRequest.specialty}</p>
              </div>
              
              <div className="mb-2">
                <span className="font-medium text-gray-700">Submitted:</span>
                <p className="mt-1 text-gray-600">
                  {new Date(verificationRequest.created_at).toLocaleDateString()}
                </p>
              </div>
              
              {verificationRequest.review_notes && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                  <span className="font-medium text-gray-700">Reviewer Notes:</span>
                  <p className="mt-1 text-gray-600">{verificationRequest.review_notes}</p>
                </div>
              )}
            </div>
            
            {verificationRequest.status === 'rejected' && (
              <div className="mt-4">
                <p className="text-gray-700 mb-4">
                  Your verification request was rejected. You can submit a new request with updated information.
                </p>
                <button
                  onClick={() => setVerificationRequest(null)}
                  className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Submit New Request
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Request Expert Verification</h2>
            
            <p className="text-gray-700 mb-6">
              Expert verification allows you to showcase your credentials and expertise in holistic healing.
              Verified experts receive a special badge and their contributions are highlighted in the community.
            </p>
            
            {error && (
              <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md">
                {error}
              </div>
            )}
            
            {success && (
              <div className="mb-6 p-3 bg-green-50 text-green-700 rounded-md">
                {success}
              </div>
            )}
            
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label htmlFor="credentials" className="block text-sm font-medium text-gray-700 mb-1">
                  Professional Credentials*
                </label>
                <textarea
                  id="credentials"
                  value={credentials}
                  onChange={(e) => setCredentials(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  rows={3}
                  placeholder="Describe your professional background, certifications, education, etc."
                  required
                />
              </div>
              
              <div className="mb-4">
                <label htmlFor="specialty" className="block text-sm font-medium text-gray-700 mb-1">
                  Area of Specialty*
                </label>
                <input
                  type="text"
                  id="specialty"
                  value={specialty}
                  onChange={(e) => setSpecialty(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="e.g., Herbalism, Ayurveda, Traditional Chinese Medicine"
                  required
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Verification Documents*
                </label>
                <p className="text-sm text-gray-500 mb-2">
                  Please upload documents that verify your credentials (certificates, licenses, diplomas, etc.)
                </p>
                
                <div className="mb-3">
                  <label className="flex items-center justify-center w-full h-32 px-4 transition bg-white border-2 border-gray-300 border-dashed rounded-md appearance-none cursor-pointer hover:border-gray-400 focus:outline-none">
                    <span className="flex flex-col items-center space-y-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      <span className="font-medium text-gray-600">
                        Drop files to attach, or <span className="text-nature-green">browse</span>
                      </span>
                      <span className="text-xs text-gray-500">Supported formats: PDF, JPG, PNG (max 10MB)</span>
                    </span>
                    <input
                      type="file"
                      name="file_upload"
                      className="hidden"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleFileChange}
                      multiple
                    />
                  </label>
                </div>
                
                {files.length > 0 && (
                  <div className="mt-2">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Selected Files:</h4>
                    <ul className="space-y-2">
                      {files.map((file, index) => (
                        <li key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                          <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span className="text-sm text-gray-700">{file.name}</span>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
              
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Verification Request'}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
