'use client';

import { useState, useEffect } from 'react';

export default function DebugCategoriesPage() {
  const [status, setStatus] = useState('Starting...');
  const [categories, setCategories] = useState([]);
  const [error, setError] = useState(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    async function testCategoriesAPI() {
      try {
        addLog('Starting categories API test...');
        setStatus('Fetching categories...');
        
        addLog('Making fetch request to /api/categories');
        const response = await fetch('/api/categories', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        addLog(`Response status: ${response.status} ${response.statusText}`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        addLog('Parsing JSON response...');
        const data = await response.json();
        
        addLog(`Received ${data?.length || 0} categories`);
        setCategories(data || []);
        setStatus('Success!');
        
      } catch (err: any) {
        addLog(`Error: ${err.message}`);
        setError(err.message);
        setStatus('Failed');
      }
    }

    testCategoriesAPI();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Categories API Debug</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Status</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Current Status:</strong> {status}</p>
            <p><strong>Categories Count:</strong> {categories.length}</p>
            {error && (
              <p className="text-red-600"><strong>Error:</strong> {error}</p>
            )}
          </div>
          
          <h2 className="text-xl font-semibold mb-4 mt-6">Categories Data</h2>
          <div className="bg-gray-100 p-4 rounded max-h-64 overflow-auto">
            {categories.length > 0 ? (
              <ul className="space-y-2">
                {categories.slice(0, 10).map((cat: any, index) => (
                  <li key={index} className="text-sm">
                    <strong>{cat.name}</strong> ({cat.slug})
                  </li>
                ))}
                {categories.length > 10 && (
                  <li className="text-sm text-gray-600">
                    ... and {categories.length - 10} more
                  </li>
                )}
              </ul>
            ) : (
              <p className="text-gray-600">No categories loaded yet</p>
            )}
          </div>
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
          <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-auto">
            {logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="mt-8">
        <button
          onClick={() => window.location.reload()}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Reload Test
        </button>
        <a
          href="/"
          className="ml-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 inline-block"
        >
          Back to Home
        </a>
      </div>
    </div>
  );
}
