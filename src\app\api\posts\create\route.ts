import { createClient } from '@supabase/supabase-js';

export async function POST(req: Request) {
  try {
    // Initialize Supabase client inside the handler
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase environment variables');
      return new Response(JSON.stringify({ error: 'Server configuration error' }), { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    const { title, content, category } = await req.json(); // Assuming request body is JSON

    if (!title || !content || !category) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), { status: 400 });
    }

    const { data, error } = await supabase
      .from('posts')
      .insert({
        title,
        content,
        category,
        // ... other fields
      })
      .select(); // To return the created post

    if (error) {
      console.error("Error creating post:", error);
      return new Response(JSON.stringify({ error: 'Failed to create post' }), { status: 500 });
    }

    return new Response(JSON.stringify({ data: data[0] }), { status: 201 });
  } catch (error) {
    console.error("Error in POST handler:", error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), { status: 500 });
  }
}