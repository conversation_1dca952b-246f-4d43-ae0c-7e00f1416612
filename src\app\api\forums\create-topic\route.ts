import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { createSlug } from '@/lib/utils';

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { title, content, categoryId, authorId } = await request.json();

    // Validate required fields
    if (!title || !content || !categoryId || !authorId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    console.log('Creating topic with data:', {
      title,
      categoryId,
      authorId,
    });

    // Create a slug from the title
    const slug = createSlug(title);

    // Check if slug already exists
    const { data: existingTopic } = await supabaseAdmin
      .from('forum_topics')
      .select('id')
      .eq('slug', slug)
      .single();

    const finalSlug = existingTopic ? `${slug}-${Date.now()}` : slug;

    // Create the topic using the admin client (bypasses RLS)
    const { data: topic, error } = await supabaseAdmin
      .from('forum_topics')
      .insert({
        title,
        slug: finalSlug,
        category_id: categoryId,
        author_id: authorId,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating topic:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    // Create the initial post
    const { data: post, error: postError } = await supabaseAdmin
      .from('forum_posts')
      .insert({
        topic_id: topic.id,
        author_id: authorId,
        content,
      })
      .select()
      .single();

    if (postError) {
      console.error('Error creating post:', postError);
      // If post creation fails, delete the topic to avoid orphaned topics
      await supabaseAdmin
        .from('forum_topics')
        .delete()
        .eq('id', topic.id);

      return NextResponse.json(
        { error: postError.message },
        { status: 500 }
      );
    }

    // Return the created topic and post
    return NextResponse.json({ topic, post });
  } catch (error: any) {
    console.error('Unexpected error in create-topic API:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
