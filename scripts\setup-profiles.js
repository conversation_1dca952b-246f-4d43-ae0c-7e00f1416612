// This script creates or updates the profiles table and sets a user as admin
// Run with: node scripts/setup-profiles.js <email>

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const email = process.argv[2];

if (!email) {
  console.error('Please provide an email address');
  console.error('Usage: node scripts/setup-profiles.js <email>');
  process.exit(1);
}

async function setupProfilesAndAdmin(email) {
  console.log(`Setting up profiles table and setting ${email} as admin...`);

  // Create Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Try to get the user by email
  const { data: { users }, error: userError } = await supabase.auth.admin.listUsers();

  if (userError) {
    console.error('Error fetching users:', userError.message);
    return;
  }

  const user = users.find(u => u.email === email);

  if (!user) {
    console.error(`User with email ${email} not found`);
    return;
  }

  console.log(`Found user: ${user.id}`);

  // Try to query the profiles table to see if it exists
  const { data: profileData, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .limit(1);

  // If the table doesn't exist, create it
  if (profileError && profileError.code === 'PGRST204') {
    console.log('Profiles table does not exist, creating it...');

    // Create the profiles table using SQL
    const { error: sqlError } = await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.profiles (
          id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
          username TEXT UNIQUE,
          full_name TEXT,
          avatar_url TEXT,
          role TEXT DEFAULT 'user',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Set up Row Level Security
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

        -- Create policies
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT FROM pg_policies
            WHERE tablename = 'profiles' AND policyname = 'Public profiles are viewable by everyone'
          ) THEN
            CREATE POLICY "Public profiles are viewable by everyone"
              ON public.profiles
              FOR SELECT USING (true);
          END IF;

          IF NOT EXISTS (
            SELECT FROM pg_policies
            WHERE tablename = 'profiles' AND policyname = 'Users can update their own profile'
          ) THEN
            CREATE POLICY "Users can update their own profile"
              ON public.profiles
              FOR UPDATE USING (auth.uid() = id);
          END IF;
        END $$;
      `
    });

    if (sqlError) {
      console.error('Error creating profiles table:', sqlError.message);
      return;
    }

    console.log('Profiles table created successfully');
  } else if (profileError) {
    console.error('Error checking profiles table:', profileError.message);
    return;
  } else {
    console.log('Profiles table already exists');
  }

  // Check if the user already has a profile
  const { data: userProfile, error: userProfileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  if (userProfileError && userProfileError.code !== 'PGRST116') {
    console.error('Error checking user profile:', userProfileError.message);
    return;
  }

  if (userProfile) {
    // Update existing profile to admin
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ role: 'admin' })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error updating profile:', updateError.message);
      return;
    }

    console.log(`Updated ${email} to admin role`);
  } else {
    // Create new profile with admin role
    const { error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        username: email.split('@')[0],
        full_name: 'Admin User',
        role: 'admin'
      });

    if (insertError) {
      console.error('Error creating profile:', insertError.message);
      return;
    }

    console.log(`Created admin profile for ${email}`);
  }

  console.log('Setup completed successfully!');
}

setupProfilesAndAdmin(email)
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
