// This is a simplified version that can be run directly with Node.js
// without TypeScript compilation

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local if available
try {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const match = line.match(/^([^=]+)=(.*)$/);
      if (match) {
        process.env[match[1]] = match[2];
      }
    });
  }
} catch (error) {
  console.warn('Could not load .env.local file:', error);
}

// Get Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function seedCategories() {
  try {
    // Read categories from JSON file
    const categoriesPath = path.join(process.cwd(), 'src', 'data', 'categories.json');
    const categoriesData = JSON.parse(fs.readFileSync(categoriesPath, 'utf8'));
    
    console.log(`Found ${categoriesData.length} categories to seed`);
    
    // Insert categories into the database
    const { data, error } = await supabase
      .from('categories')
      .upsert(
        categoriesData.map(category => ({
          name: category.name,
          slug: category.slug,
          description: category.description || null
        })),
        { onConflict: 'slug' }
      );
    
    if (error) {
      console.error('Error seeding categories:', error);
      return;
    }
    
    console.log('Categories seeded successfully!');
  } catch (error) {
    console.error('Unexpected error seeding categories:', error);
  }
}

// Run the seed function
seedCategories()
  .then(() => {
    console.log('Seeding process completed');
  })
  .catch(error => {
    console.error('Seeding process failed:', error);
  });
