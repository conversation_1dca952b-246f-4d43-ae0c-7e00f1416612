const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function testModerationActions() {
  console.log('Testing moderation_actions table...');
  
  // Check if the table exists
  const { data: tables, error: tablesError } = await supabase
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public')
    .eq('table_name', 'moderation_actions');
  
  if (tablesError) {
    console.error('Error checking table existence:', tablesError);
    return;
  }
  
  if (!tables || tables.length === 0) {
    console.log('moderation_actions table does not exist!');
    return;
  }
  
  console.log('moderation_actions table exists.');
  
  // Check the structure of the table
  const { data: columns, error: columnsError } = await supabase
    .from('information_schema.columns')
    .select('column_name, data_type')
    .eq('table_schema', 'public')
    .eq('table_name', 'moderation_actions');
  
  if (columnsError) {
    console.error('Error checking table structure:', columnsError);
    return;
  }
  
  console.log('Table structure:');
  columns.forEach(column => {
    console.log(`- ${column.column_name}: ${column.data_type}`);
  });
  
  // Count the number of rows
  const { count, error: countError } = await supabase
    .from('moderation_actions')
    .select('*', { count: 'exact', head: true });
  
  if (countError) {
    console.error('Error counting rows:', countError);
    return;
  }
  
  console.log(`Total rows: ${count}`);
  
  // Get a few rows
  const { data: rows, error: rowsError } = await supabase
    .from('moderation_actions')
    .select('*')
    .limit(5);
  
  if (rowsError) {
    console.error('Error fetching rows:', rowsError);
    return;
  }
  
  console.log('Sample rows:');
  rows.forEach(row => {
    console.log(JSON.stringify(row, null, 2));
  });
}

testModerationActions();
