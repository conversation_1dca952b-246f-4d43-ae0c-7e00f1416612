'use client';

import React from 'react';
import { LEVEL_THRESHOLDS } from '@/lib/profile-types';
import { Tooltip } from '@/components/Tooltip';

interface UserLevelDisplayProps {
  level: number;
  points?: number;
  showTitle?: boolean;
  showProgress?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function UserLevelDisplay({
  level,
  points,
  showTitle = false,
  showProgress = false,
  size = 'md',
  className = ''
}: UserLevelDisplayProps) {
  // Find the current level threshold
  const currentThreshold = LEVEL_THRESHOLDS.find(t => t.level === level) || 
    LEVEL_THRESHOLDS.find(t => t.level > level)?.level ? 
    LEVEL_THRESHOLDS[LEVEL_THRESHOLDS.findIndex(t => t.level > level) - 1] : 
    LEVEL_THRESHOLDS[LEVEL_THRESHOLDS.length - 1];

  // Find the next level threshold
  const nextThreshold = LEVEL_THRESHOLDS.find(t => t.level > level);

  // Calculate progress percentage if points are provided
  let progressPercentage = 0;
  if (points !== undefined && nextThreshold) {
    const currentPoints = currentThreshold.points;
    const nextPoints = nextThreshold.points;
    const pointsRange = nextPoints - currentPoints;
    const userProgress = points - currentPoints;
    progressPercentage = Math.min(100, Math.max(0, (userProgress / pointsRange) * 100));
  }

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const levelBadgeClasses = {
    sm: 'w-5 h-5 text-xs',
    md: 'w-6 h-6 text-sm',
    lg: 'w-8 h-8 text-base'
  };

  return (
    <div className={`flex items-center ${className}`}>
      <Tooltip content={`Level ${level}: ${currentThreshold.title}`}>
        <div className={`flex items-center justify-center rounded-full bg-green-100 text-green-800 font-semibold ${levelBadgeClasses[size]}`}>
          {level}
        </div>
      </Tooltip>
      
      {showTitle && (
        <span className={`ml-2 text-gray-700 ${sizeClasses[size]}`}>
          {currentThreshold.title}
        </span>
      )}
      
      {showProgress && points !== undefined && nextThreshold && (
        <div className="ml-3 flex-1">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full" 
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <div className={`flex justify-between mt-1 ${sizeClasses.sm}`}>
            <span className="text-gray-600">{points} pts</span>
            <span className="text-gray-600">{nextThreshold.points} pts</span>
          </div>
        </div>
      )}
    </div>
  );
}
