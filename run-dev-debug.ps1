# Run the development server with debug mode
Write-Host "Starting development server with debug mode..." -ForegroundColor Green
Write-Host "Visit http://localhost:3000/test-categories-direct to check database connection" -ForegroundColor Yellow
Write-Host "Visit http://localhost:3000/api/debug to check API connection" -ForegroundColor Yellow

# Set environment variables
$env:NODE_ENV = "development"
$env:DEBUG = "true"

# Run the development server
npm run dev
