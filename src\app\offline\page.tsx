'use client';

import Link from 'next/link';
import Image from 'next/image';

export default function OfflinePage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-4 py-12 bg-gray-50">
      <div className="text-center max-w-md">
        <div className="mb-6 relative w-32 h-32 mx-auto">
          <Image
            src="/images/nature-heals-logo.png"
            alt="NatureHeals.info Logo"
            width={128}
            height={128}
            className="object-contain"
            priority
          />
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-4">You're Offline</h1>

        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <p className="text-gray-700 mb-4">
            It looks like you're currently offline. Some features may not be available until you reconnect to the internet.
          </p>

          <p className="text-gray-700 mb-6">
            You can still access previously visited pages that have been cached.
          </p>

          <div className="flex flex-col space-y-3">
            <Link
              href="/"
              className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
            >
              Go to Homepage
            </Link>

            <button
              onClick={() => {
                if (typeof window !== 'undefined') {
                  window.location.reload();
                }
              }}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>

        <div className="text-sm text-gray-500">
          <h2 className="font-medium mb-2">Offline Tips:</h2>
          <ul className="list-disc text-left pl-5 space-y-1">
            <li>Check your internet connection</li>
            <li>Try turning on mobile data or Wi-Fi</li>
            <li>Check if airplane mode is turned off</li>
            <li>Try visiting other websites</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
