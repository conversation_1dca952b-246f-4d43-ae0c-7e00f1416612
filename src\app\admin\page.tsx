'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getBasicAdminStats, getRecentUsers } from '@/lib/admin-data';
import { getPendingContent as getPendingAdminContent } from '@/lib/admin'; // Use getPendingContent from lib/admin.ts
import StorageSetup from '@/components/StorageSetup';
import RecentActivityFeed from '@/components/admin/RecentActivityFeed';
import Link from 'next/link';

export default function AdminDashboard() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = React.useState(false);
  const [unauthorized, setUnauthorized] = React.useState(false);
  const [stats, setStats] = React.useState<any>(null);
  const [recentUsers, setRecentUsers] = React.useState<any[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [pendingReviews, setPendingReviews] = React.useState<any[]>([]);

  const { user, loading } = useAuth();

  React.useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        // Redirect to unauthorized page if not admin/moderator
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Fetch admin data once authenticated
      const fetchAdminData = async () => {
        try {
          setIsLoading(true);
          const [statsData, recentUsersData, pendingReviewsResult] = await Promise.all([
            getBasicAdminStats(),
            getRecentUsers(5),
            getPendingAdminContent(5) // Fetch 5 pending reviews
          ]);

          setStats(statsData);
          setRecentUsers(recentUsersData.users);
          setPendingReviews(pendingReviewsResult.data || []);
        } catch (error) {
          console.error('Error fetching admin data:', error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchAdminData();
    }

    checkAuth();
  }, [router, user, loading]);

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading dashboard...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6">Admin Dashboard</h1>

      {/* Dashboard Stats */}
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
        {isLoading ? (
          // Loading skeleton
          <>
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <div key={i} className="bg-white p-4 sm:p-6 rounded-lg shadow-md animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-3 sm:mb-4"></div>
                <div className="h-6 sm:h-8 bg-gray-200 rounded w-1/2 mb-3 sm:mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            ))}
          </>
        ) : (
          <>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">Total Users</h3>
              <p className="text-2xl sm:text-3xl font-bold">{stats?.totalUsers || 0}</p>
              <p className="text-xs sm:text-sm text-green-500 mt-1 sm:mt-2">+{stats?.newUsers || 0} this week</p>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">Admin Users</h3>
              <p className="text-2xl sm:text-3xl font-bold">{stats?.adminUsers || 0}</p>
              <p className="text-xs sm:text-sm text-blue-500 mt-1 sm:mt-2">Admins & Moderators</p>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">New Users</h3>
              <p className="text-2xl sm:text-3xl font-bold">{stats?.newUsers || 0}</p>
              <p className="text-xs sm:text-sm text-green-500 mt-1 sm:mt-2">In the last 7 days</p>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">Forum Topics</h3>
              <p className="text-2xl sm:text-3xl font-bold">{stats?.forumTopics || 0}</p>
              <p className="text-xs sm:text-sm text-purple-500 mt-1 sm:mt-2">Total discussion topics</p>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">Forum Posts</h3>
              <p className="text-2xl sm:text-3xl font-bold">{stats?.forumPosts || 0}</p>
              <p className="text-xs sm:text-sm text-green-500 mt-1 sm:mt-2">Total forum replies</p>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">Pending Moderation</h3>
              <p className="text-2xl sm:text-3xl font-bold">{stats?.pendingModeration || 0}</p>
              <div className="flex items-center mt-1 sm:mt-2">
                <span className="text-xs sm:text-sm text-yellow-500">Needs review</span>
                <Link href="/admin/moderation-queue" className="ml-1 sm:ml-2 text-xs text-blue-600 hover:underline">View</Link>
              </div>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">Reported Content</h3>
              <p className="text-2xl sm:text-3xl font-bold">{stats?.reportedContent || 0}</p>
              <div className="flex items-center mt-1 sm:mt-2">
                <span className="text-xs sm:text-sm text-red-500">Flagged by users</span>
                <Link href="/admin/moderation" className="ml-1 sm:ml-2 text-xs text-blue-600 hover:underline">Review</Link>
              </div>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg shadow-md">
              <h3 className="text-sm sm:text-lg font-semibold text-gray-500 mb-1 sm:mb-2">System Status</h3>
              <p className="text-2xl sm:text-3xl font-bold text-green-500">Active</p>
              <p className="text-xs sm:text-sm text-gray-500 mt-1 sm:mt-2">All systems operational</p>
            </div>
          </>
        )}
      </div>

      {/* Pending Reviews */}
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6">
          <h2 className="text-lg sm:text-xl font-bold mb-3 sm:mb-0">Content Management</h2>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => router.push('/admin/moderation')}
              className="px-2 sm:px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs sm:text-sm hover:bg-gray-300"
            >
              All
            </button>
            <button
              onClick={() => router.push('/admin/moderation?type=article')}
              className="px-2 sm:px-3 py-1 bg-nature-green text-white rounded-md text-xs sm:text-sm"
            >
              Articles
            </button>
            <button
              onClick={() => router.push('/admin/moderation?type=media')}
              className="px-2 sm:px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs sm:text-sm hover:bg-gray-300"
            >
              Media
            </button>
            <button
              onClick={() => router.push('/admin/moderation?type=comment')}
              className="px-2 sm:px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs sm:text-sm hover:bg-gray-300"
            >
              Comments
            </button>
          </div>
        </div>

        {isLoading && !pendingReviews.length ? (
          <div className="animate-pulse">
            <div className="h-8 sm:h-10 bg-gray-200 rounded mb-3 sm:mb-4"></div>
            <div className="h-32 sm:h-40 bg-gray-200 rounded"></div>
          </div>
        ) : pendingReviews.length > 0 ? (
          <div className="space-y-3">
            {pendingReviews.map((item: any) => (
              <div key={item.id} className="p-3 border border-gray-200 rounded-md hover:shadow-sm transition-shadow">
                <Link href={`/admin/content/edit/${item.id}`} className="font-medium text-nature-green hover:underline">
                  {item.title}
                </Link>
                <p className="text-xs text-gray-500">
                  By {item.profiles?.username || 'Unknown'} in {item.category || 'Uncategorized'}
                </p>
                <p className="text-xs text-gray-500">
                  Submitted: {new Date(item.created_at).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 sm:py-12 border-2 border-dashed border-gray-300 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 sm:h-12 sm:w-12 mx-auto text-gray-400 mb-3 sm:mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-1">All Caught Up!</h3>
            <p className="text-sm text-gray-500 mb-4 px-4">No content is currently pending review.</p>
            <button
              onClick={() => router.push('/admin/content/manage')}
              className="inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-transparent rounded-md shadow-sm text-xs sm:text-sm font-medium text-white bg-nature-green hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-nature-green"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="-ml-1 mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              Manage All Content
            </button>
          </div>
        )}
      </div>

      {/* Recent Activity Feed */}
      <RecentActivityFeed />

      {/* Recent Users */}
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md mb-6 sm:mb-8">
        <h2 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">Recent User Registrations</h2>
        {isLoading ? (
          // Loading skeleton
          <div className="space-y-3 sm:space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-start animate-pulse">
                <div className="bg-gray-200 p-2 rounded-full mr-3 sm:mr-4 h-8 w-8 sm:h-9 sm:w-9"></div>
                <div className="flex-1">
                  <div className="h-3 sm:h-4 bg-gray-200 rounded w-1/3 mb-1 sm:mb-2"></div>
                  <div className="h-2 sm:h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-3 sm:space-y-4">
            {recentUsers.length > 0 ? (
              recentUsers.map((user) => (
                <div key={user.id} className="flex items-start">
                  <div className="bg-blue-100 p-1.5 sm:p-2 rounded-full mr-3 sm:mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs sm:text-sm">
                      <span className="font-medium">{user.full_name || user.username}</span>
                      <span className="text-gray-500">({user.role || 'user'})</span>
                    </p>
                    <p className="text-xs text-gray-500 mt-0.5 sm:mt-1">
                      {new Date(user.created_at).toLocaleDateString()} at {new Date(user.created_at).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center">No recent user registrations</p>
            )}
          </div>
        )}
        <div className="mt-4 text-center">
          <button
            onClick={() => router.push('/admin/users')}
            className="text-nature-green text-sm sm:text-base font-medium hover:underline"
          >
            View All Users
          </button>
        </div>
      </div>

      {/* Storage Setup */}
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md mb-6 sm:mb-8">
        <h2 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">Storage Configuration</h2>
        <StorageSetup />
      </div>

      {/* Admin Tools */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
          <div className="flex items-center mb-3 sm:mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <h3 className="text-base sm:text-lg font-semibold">User Management</h3>
          </div>
          <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/users')} className="text-gray-700 hover:text-nature-green transition-colors">Manage Users ({stats?.totalUsers || 0})</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/moderators')} className="text-gray-700 hover:text-nature-green transition-colors">Manage Moderators ({stats?.adminUsers || 0})</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/users/bans')} className="text-gray-700 hover:text-nature-green transition-colors">User Bans</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/permissions')} className="text-gray-700 hover:text-nature-green transition-colors">User Permissions</button>
            </li>
          </ul>
        </div>
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
          <div className="flex items-center mb-3 sm:mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h3 className="text-base sm:text-lg font-semibold">Content Management</h3>
          </div>
          <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/content/create')} className="text-gray-700 hover:text-nature-green transition-colors">Create New Content</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/content/manage')} className="text-gray-700 hover:text-nature-green transition-colors">Manage Content</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/categories-new')} className="text-gray-700 hover:text-nature-green transition-colors">Manage Categories (New)</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/featured')} className="text-gray-700 hover:text-nature-green transition-colors">Featured Content</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/forums')} className="text-gray-700 hover:text-nature-green transition-colors">Forum Management</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/forums/categories')} className="text-gray-700 hover:text-nature-green transition-colors">Forum Categories</button>
            </li>
          </ul>
        </div>
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
          <div className="flex items-center mb-3 sm:mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-base sm:text-lg font-semibold">Moderation</h3>
          </div>
          <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/moderation-queue')} className="text-gray-700 hover:text-nature-green transition-colors">Moderation Queue</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/moderation-logs')} className="text-gray-700 hover:text-nature-green transition-colors">Moderation Logs</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/users/bans/create')} className="text-gray-700 hover:text-nature-green transition-colors">Ban User</button>
            </li>
          </ul>
        </div>
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md">
          <div className="flex items-center mb-3 sm:mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <h3 className="text-base sm:text-lg font-semibold">System Settings</h3>
          </div>
          <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/settings')} className="text-gray-700 hover:text-nature-green transition-colors">Site Configuration</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/logs')} className="text-gray-700 hover:text-nature-green transition-colors">System Logs</button>
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
              <button onClick={() => router.push('/admin/backup')} className="text-gray-700 hover:text-nature-green transition-colors">Database Management</button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
