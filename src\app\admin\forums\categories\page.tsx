'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getForumCategories } from '@/lib/forums';
import { reorderForumCategories } from '@/lib/category-management';
import Link from 'next/link';
import {
  FaEdit, FaTrash, FaArrowUp, FaArrowDown, FaPlus,
  FaArrowLeft, FaEye, FaSort
} from 'react-icons/fa';
// Using native HTML5 drag and drop instead of react-beautiful-dnd

export default function CategoryManagementPage() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [draggedItem, setDraggedItem] = useState(null);
  const [draggedOverItem, setDraggedOverItem] = useState(null);

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || profile.role !== 'admin') {
        // Redirect to unauthorized page if not admin
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Load categories
      loadCategories();
    }

    checkAuth();
  }, [router, user, loading]);

  async function loadCategories() {
    try {
      setIsLoading(true);
      const data = await getForumCategories();

      // Sort by display_order
      const sortedCategories = [...data].sort((a, b) =>
        (a.display_order || 0) - (b.display_order || 0)
      );

      setCategories(sortedCategories);
    } catch (err) {
      console.error('Error loading categories:', err);
      setError('Failed to load forum categories');
    } finally {
      setIsLoading(false);
    }
  }

  async function handleReorder(result) {
    if (!result.destination) return;

    const startIndex = result.source.index;
    const endIndex = result.destination.index;

    if (startIndex === endIndex) return;

    // Reorder the categories array
    const reorderedCategories = Array.from(categories);
    const [removed] = reorderedCategories.splice(startIndex, 1);
    reorderedCategories.splice(endIndex, 0, removed);

    // Update the display_order values
    const updatedCategories = reorderedCategories.map((category, index) => ({
      ...category,
      display_order: index + 1
    }));

    setCategories(updatedCategories);

    // Save the new order to the database
    try {
      setIsLoading(true);

      const categoryOrders = updatedCategories.map(category => ({
        id: category.id,
        order: category.display_order
      }));

      const result = await reorderForumCategories({
        categoryOrders,
        updatedBy: user.id
      });

      if (result.failed.length > 0) {
        setError(`Failed to update ${result.failed.length} categories`);
      } else {
        setSuccess('Categories reordered successfully');

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      }
    } catch (err) {
      console.error('Error reordering categories:', err);
      setError('Failed to save category order');

      // Reload categories to reset the order
      loadCategories();
    } finally {
      setIsLoading(false);
    }
  }

  function handleDragStart(e, index) {
    setIsDragging(true);
    setDraggedItem(index);
    e.dataTransfer.effectAllowed = 'move';
  }

  function handleDragOver(e, index) {
    e.preventDefault();
    setDraggedOverItem(index);
    e.dataTransfer.dropEffect = 'move';
  }

  function handleDrop(e) {
    e.preventDefault();
    const result = {
      source: { index: draggedItem },
      destination: { index: draggedOverItem }
    };
    handleReorder(result);
  }

  function handleDragEnd() {
    setIsDragging(false);
    setDraggedItem(null);
    setDraggedOverItem(null);
  }

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64 px-4 text-center">
        <div className="text-red-600 font-bold text-lg sm:text-xl mb-3 sm:mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4 text-sm sm:text-base">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors w-full sm:w-auto"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex flex-col justify-center items-center h-64 px-4 text-center">
        <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-t-2 border-b-2 border-green-600 mb-3"></div>
        <span className="text-base sm:text-lg text-gray-600">Loading categories...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 space-y-4 sm:space-y-0">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
          <button
            onClick={() => router.push('/admin/forums')}
            className="self-start sm:mr-4 text-nature-green hover:underline flex items-center"
          >
            <FaArrowLeft className="mr-1" />
            Back to Forums
          </button>
          <h1 className="text-2xl sm:text-3xl font-bold">Category Management</h1>
        </div>
        <Link
          href="/admin/forums/categories/create"
          className="self-start sm:self-auto px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors flex items-center w-full sm:w-auto justify-center sm:justify-start"
        >
          <FaPlus className="mr-2" />
          Add Category
        </Link>
      </div>

      {error && (
        <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
          <p className="font-medium text-sm sm:text-base">Error</p>
          <p className="text-sm sm:text-base">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-green-50 text-green-700 rounded-lg border border-green-100">
          <p className="font-medium text-sm sm:text-base">Success</p>
          <p className="text-sm sm:text-base">{success}</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-6">
        <div className="flex items-center mb-3">
          <FaSort className="text-gray-500 mr-2 flex-shrink-0" />
          <h2 className="text-base sm:text-lg font-semibold">Drag and drop to reorder categories</h2>
        </div>
        <p className="text-gray-500 mb-2 text-sm sm:text-base">
          Changes are saved automatically when you reorder categories.
        </p>
        <p className="text-gray-500 text-xs sm:text-sm italic">
          On mobile: Touch and hold an item, then drag to reorder.
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {categories.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {categories.map((category, index) => (
              <div
                key={category.id}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragOver={(e) => handleDragOver(e, index)}
                onDrop={handleDrop}
                onDragEnd={handleDragEnd}
                className={`p-4 ${draggedItem === index ? 'bg-blue-50' : 'hover:bg-gray-50'}`}
              >
                <div className="flex flex-col md:flex-row md:items-center justify-between space-y-4 md:space-y-0">
                  <div className="flex items-start md:items-center">
                    <div className="mr-3 p-2 rounded-md hover:bg-gray-200 cursor-grab self-start">
                      <FaSort className="text-gray-400" />
                    </div>
                    <div>
                      <h3 className="font-medium text-lg">{category.name}</h3>
                      <p className="text-sm text-gray-500">
                        {category.description || 'No description'}
                      </p>
                      <div className="flex flex-wrap items-center mt-1 gap-2">
                        <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
                          {category.slug}
                        </span>
                        <span className="text-xs text-gray-500">
                          Order: {category.display_order || index + 1}
                        </span>
                        {category.is_active === false && (
                          <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                            Inactive
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Link
                      href={`/forums/${category.slug}`}
                      className="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors"
                      target="_blank"
                      title="View category"
                    >
                      <FaEye className="mr-1" />
                      View
                    </Link>
                    <Link
                      href={`/admin/forums/categories/edit/${category.id}`}
                      className="inline-flex items-center px-3 py-1.5 bg-nature-green/10 text-nature-green rounded-md hover:bg-nature-green/20 transition-colors"
                      title="Edit category"
                    >
                      <FaEdit className="mr-1" />
                      Edit
                    </Link>
                    <Link
                      href={`/admin/forums/categories/delete/${category.id}`}
                      className="inline-flex items-center px-3 py-1.5 bg-red-100 text-red-600 rounded-md hover:bg-red-200 transition-colors"
                      title="Delete category"
                    >
                      <FaTrash className="mr-1" />
                      Delete
                    </Link>
                  </div>
                </div>

                {/* Stats */}
                <div className="mt-3 flex flex-wrap gap-4 text-sm text-gray-500">
                  <div className="bg-gray-50 px-2 py-1 rounded">
                    <span className="font-medium">{category.topic_count || 0}</span> topics
                  </div>
                  <div className="bg-gray-50 px-2 py-1 rounded">
                    <span className="font-medium">{category.post_count || 0}</span> posts
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-4 sm:p-6 text-center text-gray-500">
            <p className="text-sm sm:text-base mb-3">No categories found.</p>
            <p className="text-xs sm:text-sm">Create your first category to get started.</p>
          </div>
        )}
      </div>
    </div>
  );
}
