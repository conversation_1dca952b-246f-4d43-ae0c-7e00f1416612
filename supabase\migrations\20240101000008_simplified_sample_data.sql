-- Simplified sample data script that checks table structure before inserting

DO $$
DECLARE
    admin_id UUID;
    moderator_id UUID;
    user1_id UUID;
    user2_id UUID;
    topic1_id UUID;
    post1_id UUID;
    has_user_bans BOOLEAN := FALSE;
    has_reported_content BOOLEAN := FALSE;
    has_moderation_actions BOOLEAN := FALSE;
    has_auto_moderation_logs BOOLEAN := FALSE;
    has_user_notifications BOOLEAN := FALSE;
BEGIN
    -- Check if tables exist with required columns
    has_user_bans := EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'user_bans'
    );
    
    has_reported_content := EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'reported_content'
    );
    
    has_moderation_actions := EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'moderation_actions'
    );
    
    has_auto_moderation_logs := EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'auto_moderation_logs'
    );
    
    has_user_notifications := EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'user_notifications'
    );
    
    -- Get user IDs for sample data
    SELECT id INTO admin_id FROM public.profiles WHERE role = 'admin' LIMIT 1;
    SELECT id INTO moderator_id FROM public.profiles WHERE role = 'moderator' LIMIT 1;
    
    -- If no moderator, use admin
    IF moderator_id IS NULL THEN
        moderator_id := admin_id;
    END IF;
    
    -- Get regular users
    SELECT id INTO user1_id FROM public.profiles WHERE role = 'user' LIMIT 1;
    SELECT id INTO user2_id FROM public.profiles WHERE role = 'user' OFFSET 1 LIMIT 1;
    
    -- If no regular users, use admin/moderator
    IF user1_id IS NULL THEN
        user1_id := admin_id;
    END IF;
    
    IF user2_id IS NULL THEN
        user2_id := moderator_id;
    END IF;
    
    -- Get a topic and post ID
    BEGIN
        SELECT id INTO topic1_id FROM public.forum_topics LIMIT 1;
    EXCEPTION WHEN OTHERS THEN
        topic1_id := uuid_generate_v4();
    END;
    
    BEGIN
        SELECT id INTO post1_id FROM public.forum_posts LIMIT 1;
    EXCEPTION WHEN OTHERS THEN
        post1_id := uuid_generate_v4();
    END;
    
    -- Insert sample data if tables exist
    
    -- 1. User bans
    IF has_user_bans THEN
        BEGIN
            INSERT INTO public.user_bans (
                user_id, 
                moderator_id, 
                ban_type, 
                reason, 
                expires_at
            ) VALUES (
                user1_id, 
                moderator_id, 
                'post_only', 
                'Repeated violations of community guidelines', 
                NOW() + INTERVAL '7 days'
            );
            
            RAISE NOTICE 'Inserted sample user ban';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting sample user ban: %', SQLERRM;
        END;
    END IF;
    
    -- 2. Reported content
    IF has_reported_content THEN
        BEGIN
            INSERT INTO public.reported_content (
                content_type,
                content_id,
                reporter_id,
                reason,
                status,
                created_at
            ) VALUES (
                'post',
                post1_id,
                user1_id,
                'This post contains inappropriate language',
                'pending',
                NOW() - INTERVAL '2 days'
            );
            
            RAISE NOTICE 'Inserted sample reported content';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting sample reported content: %', SQLERRM;
        END;
    END IF;
    
    -- 3. Moderation actions
    IF has_moderation_actions THEN
        BEGIN
            -- Check if action_type column exists
            IF EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_schema = 'public' 
                      AND table_name = 'moderation_actions' 
                      AND column_name = 'action_type') THEN
                
                INSERT INTO public.moderation_actions (
                    action_type,
                    moderator_id,
                    content_id,
                    content_type,
                    reason,
                    created_at
                ) VALUES (
                    'lock_topic',
                    moderator_id,
                    topic1_id::text,
                    'forum_topic',
                    'Topic has gone off-topic',
                    NOW() - INTERVAL '3 days'
                );
            ELSE
                INSERT INTO public.moderation_actions (
                    moderator_id,
                    content_id,
                    content_type,
                    reason,
                    created_at
                ) VALUES (
                    moderator_id,
                    topic1_id::text,
                    'forum_topic',
                    'Topic has gone off-topic',
                    NOW() - INTERVAL '3 days'
                );
            END IF;
            
            RAISE NOTICE 'Inserted sample moderation action';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting sample moderation action: %', SQLERRM;
        END;
    END IF;
    
    -- 4. Auto moderation logs
    IF has_auto_moderation_logs THEN
        BEGIN
            INSERT INTO public.auto_moderation_logs (
                user_id,
                content_type,
                original_content,
                filtered_content,
                matched_words,
                created_at
            ) VALUES (
                user1_id,
                'post',
                'This post contains badword1 and offensive1 language',
                'This post contains ******** and ********** language',
                ARRAY['badword1', 'offensive1'],
                NOW() - INTERVAL '6 hours'
            );
            
            RAISE NOTICE 'Inserted sample auto moderation log';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting sample auto moderation log: %', SQLERRM;
        END;
    END IF;
    
    -- 5. User notifications
    IF has_user_notifications THEN
        BEGIN
            INSERT INTO public.user_notifications (
                user_id,
                type,
                title,
                message,
                link_url,
                created_at
            ) VALUES (
                user1_id,
                'content_moderated',
                'Your post has been edited',
                'A moderator has edited your post. Reason: Removed inappropriate language',
                '/forums/post/' || post1_id,
                NOW() - INTERVAL '1 day'
            );
            
            RAISE NOTICE 'Inserted sample user notification';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting sample user notification: %', SQLERRM;
        END;
    END IF;
    
    RAISE NOTICE 'Sample data insertion complete';
END $$;
