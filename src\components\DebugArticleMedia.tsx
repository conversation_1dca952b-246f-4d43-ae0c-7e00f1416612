'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Image from 'next/image';

const DebugArticleMedia = ({ articleId }: { articleId: string }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [articleMedia, setArticleMedia] = useState<any[]>([]);
  const [mediaItems, setMediaItems] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const supabase = createClientComponentClient();

        // First check the article_media table
        console.log('Checking article_media table for article ID:', articleId);
        const { data: articleMediaData, error: articleMediaError } = await supabase
          .from('article_media')
          .select('*')
          .eq('article_id', articleId);

        if (articleMediaError) {
          throw new Error(`Error fetching article_media: ${articleMediaError.message}`);
        }

        setArticleMedia(articleMediaData || []);
        console.log('article_media data:', articleMediaData);

        // Then check the media table for all media items
        if (articleMediaData && articleMediaData.length > 0) {
          const mediaIds = articleMediaData.map(item => item.media_id);
          console.log('Looking up media IDs:', mediaIds);

          const { data: mediaData, error: mediaError } = await supabase
            .from('media')
            .select('*')
            .in('id', mediaIds);

          if (mediaError) {
            throw new Error(`Error fetching media: ${mediaError.message}`);
          }

          setMediaItems(mediaData || []);
          console.log('media data:', mediaData);
        }
      } catch (err: any) {
        console.error('Debug error:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (articleId) {
      fetchData();
    }
  }, [articleId]);

  if (loading) {
    return <div>Loading debug data...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 my-4">
      <h3 className="text-lg font-bold mb-2">Debug Article Media</h3>
      
      <div className="mb-4">
        <h4 className="font-medium">Article Media Entries ({articleMedia.length})</h4>
        {articleMedia.length === 0 ? (
          <p className="text-red-500">No article_media entries found for this article!</p>
        ) : (
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
            {JSON.stringify(articleMedia, null, 2)}
          </pre>
        )}
      </div>
      
      <div>
        <h4 className="font-medium">Media Items ({mediaItems.length})</h4>
        {mediaItems.length === 0 ? (
          <p className="text-red-500">No media items found!</p>
        ) : (
          <div>
            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40 mb-2">
              {JSON.stringify(mediaItems, null, 2)}
            </pre>
            
            <div className="grid grid-cols-2 gap-2">
              {mediaItems.map(item => (
                <div key={item.id} className="border p-2 rounded">
                  <p className="text-xs mb-1">{item.title}</p>
                  {item.file_url && (
                    <Image 
                      src={item.file_url} 
                      alt={item.title} 
                      width={200} 
                      height={150}
                      className="object-contain"
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DebugArticleMedia;
