'use server';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { createServerClient } from '@supabase/ssr';

// Create a Supabase client for server actions
async function createActionClient() {
  // Use cookies asynchronously
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          try {
            // Use optional chaining to safely access cookie value
            const cookie = cookieStore.get(name);
            return cookie?.value;
          } catch (error) {
            console.error('Error getting cookie:', error);
            return undefined;
          }
        },
        set(name: string, value: string, options: Record<string, unknown>) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            console.error('Error setting cookie:', error);
          }
        },
        remove(name: string, options: Record<string, unknown>) {
          try {
            cookieStore.set(name, '', { ...options, maxAge: 0 });
          } catch (error) {
            console.error('Error removing cookie:', error);
          }
        },
      },
    }
  );
}

export async function signIn(formData: FormData) {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  const supabase = await createActionClient();

  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return { error: error.message };
  }

  redirect('/');
}

export async function signUp(formData: FormData) {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const name = formData.get('name') as string;

  const supabase = await createActionClient();

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        name,
      },
    },
  });

  if (error) {
    return { error: error.message };
  }

  return { success: 'Check your email to confirm your account' };
}

export async function signOut() {
  const supabase = await createActionClient();
  await supabase.auth.signOut();
  redirect('/');
}

export async function getRecentArticles(limit = 3) {
  const supabase = await createActionClient();

  // Get recent published articles
  const { data: articles, error } = await supabase
    .from('articles')
    .select(`
      id,
      title,
      slug,
      excerpt,
      created_at,
      author_id,
      profiles(username, full_name, avatar_url, avatar_type, preset_avatar),
      categories(name, slug)
    `)
    .eq('status', 'published')
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching recent articles:', error);
    return { error };
  }

  // If we have articles, fetch their featured images
  if (articles && articles.length > 0) {
    for (const article of articles) {
      // First try to get images from the article_media table
      const { data: articleMedia, error: articleMediaError } = await supabase
        .from('article_media')
        .select(`
          media_id,
          display_order,
          media(id, file_url)
        `)
        .eq('article_id', article.id)
        .order('display_order', { ascending: true })
        .limit(1);

      if (!articleMediaError && articleMedia && articleMedia.length > 0 && articleMedia[0].media?.file_url) {
        article.media_url = articleMedia[0].media.file_url;
        continue; // Skip to the next article if we found an image
      }

      // If no image found in article_media, try to get any image from the media table
      const { data: mediaItems, error: mediaError } = await supabase
        .from('media')
        .select('file_url')
        .eq('status', 'published')
        .limit(1);

      if (!mediaError && mediaItems && mediaItems.length > 0) {
        article.media_url = mediaItems[0].file_url;
        continue; // Skip to the next article if we found an image
      }

      // If still no image, use a placeholder based on the category
      article.media_url = 'https://placehold.co/600x400/e6f7e6/2e7d32?text=NatureHeals';
      if (article.categories?.name) {
        article.media_url = `https://placehold.co/600x400/e6f7e6/2e7d32?text=${encodeURIComponent(article.categories.name)}`;
      }
    }
  }

  return { data: articles };
}
