'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { SupabaseClient, User } from '@supabase/supabase-js'

// Extended user type with profile data
type ExtendedUser = User & {
  role?: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  level?: number;
  is_verified_expert?: boolean;
}

type AuthContextType = {
  user: ExtendedUser | null
  supabase: SupabaseClient // Keep Supabase client in context
  loading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default function AuthProvider({
  children,
  initialUser = null
}: {
  children: React.ReactNode
  initialUser?: User | null
}) {
  const [user, setUser] = useState<ExtendedUser | null>(initialUser)
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient({
    options: {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        storageKey: 'nature-heals-auth-token'
      }
    }
  })

  // Function to fetch user profile data
  const fetchUserProfile = async (userId: string) => {
    console.log('AuthProvider: Fetching profile for user ID:', userId);
    try {
      // Try to fetch from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('role, username, full_name, avatar_url, avatar_type, preset_avatar, level, is_verified_expert')
        .eq('id', userId)
        .single()

      if (profileError) {
        console.error('Error fetching profile:', profileError)

        // If the table doesn't exist, try to create a basic profile
        if (profileError.message.includes('does not exist')) {
          console.log('Profiles table may not exist, attempting to create a basic profile')
          // Return a default user structure, but don't assume role 'user' yet.
          // The auth.user object might have role from metadata if set during signup.
          return {}
        }
        // For other errors, log and return empty object to avoid breaking auth flow
        // if the auth session itself is valid.
        return {};
      }

      console.log('Fetched profile data:', profileData)
      return profileData || {}
    } catch (error) {
      console.error('Exception in fetchUserProfile:', error)
      return {}
    }
  }

  // Function to refresh the session
  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession()
      if (error) {
        console.error('Error refreshing session:', error)
        return false
      }
      if (data.session) { // Check if data.session is not null
        console.log('Session refreshed successfully')
        return true
      }
    } catch (error) {
      console.error('Exception refreshing session:', error)
    }
    return false
  }

  useEffect(() => {
    let isMounted = true;

    // Set a timeout to ensure we don't stay in loading state forever
    const loadingTimeout = setTimeout(() => {
      if (isMounted && loading) {
        console.log('Auth loading timeout reached, setting loading to false');
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    const setupAuth = async () => {
      try {
        // Get the current session
        console.log('AuthProvider: Setting up auth, getting session...');
        const { data: { session: currentSession } } = await supabase.auth.getSession();

        if (!isMounted) return;

        if (currentSession?.user) {
          // User is logged in, fetch profile data
          console.log('AuthProvider: User found in session, fetching profile...', currentSession.user.id);
          try {
            const profileData = await fetchUserProfile(currentSession.user.id);

            if (!isMounted) return;

            // Combine auth user with profile data
            const extendedUser = {
              ...currentSession.user,
              ...profileData
            };

            setUser(extendedUser);
          } catch (error) {
            console.error('AuthProvider: Error fetching profile during setupAuth, setting basic user:', error);
            // Still set the user even if profile fetch fails
            setUser(currentSession.user);
          }
        } else {
          console.log('AuthProvider: No active session found.');
          // No session, user is not logged in
          setUser(null);
        }
      } catch (error) {
        console.error('Error in setupAuth:', error);
        setUser(null);
      } finally {
        console.log('AuthProvider: setupAuth finished.');
        if (isMounted) {
          setLoading(false);
          clearTimeout(loadingTimeout);
        }
      }
    };

    // Run setup immediately
    setupAuth();

    // Set up periodic session refresh (every 15 minutes)
    const refreshInterval = setInterval(async () => {
      await refreshSession();
    }, 15 * 60 * 1000);

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!isMounted) return;

        if (event === 'SIGNED_OUT') {
          setUser(null);
          console.log('AuthProvider: User SIGNED_OUT');
          return;
        }

        if (session?.user) {
          console.log('AuthProvider: Auth state changed, user found, fetching profile...', session.user.id, event);
          try {
            const profileData = await fetchUserProfile(session.user.id);

            if (!isMounted) return;

            // Combine auth user with profile data
            const extendedUser = {
              ...session.user,
              ...profileData
            };

            setUser(extendedUser);
          } catch (error) {
            console.error('AuthProvider: Error fetching profile onAuthStateChange, setting basic user:', error);
            // Still set the user even if profile fetch fails
            setUser(session.user);
          }
        } else {
          console.log('AuthProvider: Auth state changed, no user in session.');
          setUser(null);
        }
      }
    );

    return () => {
      isMounted = false;
      clearTimeout(loadingTimeout);
      subscription.unsubscribe();
      clearInterval(refreshInterval);
    };
  }, [supabase]);

  return (
    <AuthContext.Provider value={{ user, supabase, loading }}>
      {children}
    </AuthContext.Provider>
  )
}
