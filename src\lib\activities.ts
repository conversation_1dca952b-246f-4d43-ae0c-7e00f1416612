import { createClient } from '@/lib/supabase';
import { UserActivity } from '@/lib/activity-types';

// Get user activities
export async function getUserActivities({
  userId,
  limit = 10,
  offset = 0
}: {
  userId: string;
  limit?: number;
  offset?: number;
}) {
  const supabase = createClient();
  
  const { data, error, count } = await supabase
    .from('user_activities')
    .select(`
      *,
      user:user_id (
        username,
        full_name,
        avatar_url
      )
    `, { count: 'exact' })
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
    
  if (error) {
    console.error('Error fetching user activities:', error);
    throw error;
  }
  
  return { data: data as UserActivity[], count };
}

// Get global activity feed
export async function getGlobalActivityFeed({
  limit = 20,
  offset = 0,
  activityTypes = []
}: {
  limit?: number;
  offset?: number;
  activityTypes?: string[];
}) {
  const supabase = createClient();
  
  let query = supabase
    .from('user_activities')
    .select(`
      *,
      user:user_id (
        username,
        full_name,
        avatar_url
      )
    `, { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
    
  if (activityTypes.length > 0) {
    query = query.in('activity_type', activityTypes);
  }
  
  const { data, error, count } = await query;
  
  if (error) {
    console.error('Error fetching global activity feed:', error);
    throw error;
  }
  
  return { data: data as UserActivity[], count };
}

// Get forum activity feed
export async function getForumActivityFeed({
  limit = 20,
  offset = 0
}: {
  limit?: number;
  offset?: number;
}) {
  return getGlobalActivityFeed({
    limit,
    offset,
    activityTypes: [
      'forum_topic_create',
      'forum_post_create',
      'forum_reply_create',
      'forum_solution_marked'
    ]
  });
}

// Get wiki activity feed
export async function getWikiActivityFeed({
  limit = 20,
  offset = 0
}: {
  limit?: number;
  offset?: number;
}) {
  return getGlobalActivityFeed({
    limit,
    offset,
    activityTypes: [
      'article_create',
      'article_edit',
      'article_comment'
    ]
  });
}

// Create a user activity (for client-side use)
export async function createUserActivity({
  userId,
  activityType,
  contentType,
  contentId,
  metadata
}: {
  userId: string;
  activityType: string;
  contentType: string;
  contentId: string;
  metadata?: any;
}) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('user_activities')
    .insert({
      user_id: userId,
      activity_type: activityType,
      content_type: contentType,
      content_id: contentId,
      metadata
    })
    .select()
    .single();
    
  if (error) {
    console.error('Error creating user activity:', error);
    throw error;
  }
  
  return data as UserActivity;
}

// Get activity details based on type and content ID
export async function getActivityDetails(activity: UserActivity) {
  const supabase = createClient();
  
  try {
    switch (activity.content_type) {
      case 'forum_topic': {
        const { data } = await supabase
          .from('forum_topics')
          .select(`
            id,
            title,
            slug,
            category:category_id (
              name,
              slug
            )
          `)
          .eq('id', activity.content_id)
          .single();
          
        return data;
      }
      
      case 'forum_post': {
        const { data: post } = await supabase
          .from('forum_posts')
          .select(`
            id,
            content,
            topic:topic_id (
              id,
              title,
              slug,
              category:category_id (
                name,
                slug
              )
            )
          `)
          .eq('id', activity.content_id)
          .single();
          
        return post;
      }
      
      case 'article': {
        const { data } = await supabase
          .from('articles')
          .select(`
            id,
            title,
            slug,
            excerpt,
            category:category_id (
              name,
              slug
            )
          `)
          .eq('id', activity.content_id)
          .single();
          
        return data;
      }
      
      case 'comment': {
        const { data: comment } = await supabase
          .from('comments')
          .select(`
            id,
            content,
            article:article_id (
              id,
              title,
              slug
            )
          `)
          .eq('id', activity.content_id)
          .single();
          
        return comment;
      }
      
      default:
        return null;
    }
  } catch (error) {
    console.error('Error fetching activity details:', error);
    return null;
  }
}
