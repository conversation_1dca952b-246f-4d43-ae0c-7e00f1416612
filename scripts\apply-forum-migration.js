// <PERSON><PERSON>t to apply forum system migration
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Environment variables loaded:');
console.log('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✓ Found' : '✗ Missing');
console.log('- SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✓ Found' : '✗ Missing');
console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓ Found' : '✗ Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('\nMissing required Supabase credentials. Please check your .env.local file.');
  console.error('Make sure you have the following variables defined:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY (preferred) or NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250601_forum_system.sql');
    const sql = fs.readFileSync(migrationPath, 'utf8');

    console.log('Applying forum system migration...');

    // Split the SQL into individual statements
    const statements = sql.split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement directly using the SQL API
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        // Use the Supabase SQL API to execute SQL
        const response = await axios({
          method: 'POST',
          url: `${supabaseUrl}/rest/v1/sql`,
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseServiceKey,
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'Prefer': 'resolution=ignore-duplicates,return=minimal'
          },
          data: { query: statement }
        });

        console.log(`✅ Statement ${i + 1}/${statements.length} executed successfully`);
      } catch (error) {
        // If there's an error, log it but continue with the next statement
        console.warn(`⚠️ Statement ${i + 1}/${statements.length} failed:`, error.message);
        if (error.response && error.response.data) {
          console.warn('Error details:', error.response.data);
        }
        console.warn('Statement:', statement);
      }
    }

    console.log('✅ Forum system migration applied successfully!');

    // Verify tables were created
    const tables = [
      'forum_categories',
      'forum_topics',
      'forum_posts',
      'forum_post_reactions',
      'reported_content'
    ];

    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('id')
        .limit(1);

      if (error && !error.message.includes('does not exist')) {
        console.error(`Error verifying table ${table}:`, error);
      } else {
        console.log(`✅ Table ${table} verified`);
      }
    }

    console.log('Migration complete!');
  } catch (err) {
    console.error('Unexpected error:', err);
    process.exit(1);
  }
}

// Run the migration
applyMigration();
