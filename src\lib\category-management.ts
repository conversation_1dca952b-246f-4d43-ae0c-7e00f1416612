import { createClient } from '@/lib/supabase';
import { ForumCategory } from '@/lib/forum-types';
import { logModerationAction } from '@/lib/moderation';

// Create a new forum category
export async function createForumCategory({
  name,
  slug,
  description,
  icon,
  color,
  displayOrder,
  isActive = true,
  createdBy
}: {
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  displayOrder?: number;
  isActive?: boolean;
  createdBy: string;
}) {
  const supabase = createClient();
  
  // Check if a category with this slug already exists
  const { data: existingCategory, error: checkError } = await supabase
    .from('forum_categories')
    .select('id')
    .eq('slug', slug)
    .maybeSingle();
  
  if (checkError) {
    console.error('Error checking existing category:', checkError);
    throw checkError;
  }
  
  if (existingCategory) {
    throw new Error(`A category with the slug "${slug}" already exists`);
  }
  
  // If no display order is provided, get the highest current order and add 1
  if (displayOrder === undefined) {
    const { data: categories, error: orderError } = await supabase
      .from('forum_categories')
      .select('display_order')
      .order('display_order', { ascending: false })
      .limit(1);
    
    if (orderError) {
      console.error('Error getting highest display order:', orderError);
      throw orderError;
    }
    
    displayOrder = categories && categories.length > 0 ? (categories[0].display_order + 1) : 1;
  }
  
  // Create the category
  const { data, error } = await supabase
    .from('forum_categories')
    .insert({
      name,
      slug,
      description,
      icon,
      color,
      display_order: displayOrder,
      is_active: isActive,
      created_by: createdBy,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single();
  
  if (error) {
    console.error('Error creating forum category:', error);
    throw error;
  }
  
  // Log the action
  await logModerationAction({
    actionType: 'create_category',
    moderatorId: createdBy,
    contentId: data.id,
    contentType: 'forum_category',
    reason: `Created new forum category: ${name}`
  });
  
  return data as ForumCategory;
}

// Update a forum category
export async function updateForumCategory({
  categoryId,
  updates,
  updatedBy
}: {
  categoryId: string;
  updates: Partial<{
    name: string;
    slug: string;
    description: string;
    icon: string;
    color: string;
    displayOrder: number;
    isActive: boolean;
  }>;
  updatedBy: string;
}) {
  const supabase = createClient();
  
  // Get the current category data for logging
  const { data: currentCategory, error: fetchError } = await supabase
    .from('forum_categories')
    .select('*')
    .eq('id', categoryId)
    .single();
  
  if (fetchError) {
    console.error('Error fetching current category:', fetchError);
    throw fetchError;
  }
  
  // If slug is being updated, check if it already exists
  if (updates.slug && updates.slug !== currentCategory.slug) {
    const { data: existingCategory, error: checkError } = await supabase
      .from('forum_categories')
      .select('id')
      .eq('slug', updates.slug)
      .neq('id', categoryId)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking existing category:', checkError);
      throw checkError;
    }
    
    if (existingCategory) {
      throw new Error(`A category with the slug "${updates.slug}" already exists`);
    }
  }
  
  // Prepare the update data
  const updateData: any = {
    updated_at: new Date().toISOString(),
    updated_by: updatedBy
  };
  
  if (updates.name !== undefined) updateData.name = updates.name;
  if (updates.slug !== undefined) updateData.slug = updates.slug;
  if (updates.description !== undefined) updateData.description = updates.description;
  if (updates.icon !== undefined) updateData.icon = updates.icon;
  if (updates.color !== undefined) updateData.color = updates.color;
  if (updates.displayOrder !== undefined) updateData.display_order = updates.displayOrder;
  if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
  
  // Update the category
  const { data, error } = await supabase
    .from('forum_categories')
    .update(updateData)
    .eq('id', categoryId)
    .select()
    .single();
  
  if (error) {
    console.error('Error updating forum category:', error);
    throw error;
  }
  
  // Log the action
  await logModerationAction({
    actionType: 'update_category',
    moderatorId: updatedBy,
    contentId: categoryId,
    contentType: 'forum_category',
    reason: `Updated forum category: ${currentCategory.name}`,
    previousContent: JSON.stringify(currentCategory),
    newContent: JSON.stringify(data)
  });
  
  return data as ForumCategory;
}

// Delete a forum category
export async function deleteForumCategory({
  categoryId,
  deletedBy,
  moveTopicsTo
}: {
  categoryId: string;
  deletedBy: string;
  moveTopicsTo?: string; // ID of category to move topics to, if not provided topics will be deleted
}) {
  const supabase = createClient();
  
  // Get the current category data for logging
  const { data: category, error: fetchError } = await supabase
    .from('forum_categories')
    .select('*')
    .eq('id', categoryId)
    .single();
  
  if (fetchError) {
    console.error('Error fetching category:', fetchError);
    throw fetchError;
  }
  
  // If moveTopicsTo is provided, move all topics to that category
  if (moveTopicsTo) {
    const { error: moveError } = await supabase
      .from('forum_topics')
      .update({ 
        category_id: moveTopicsTo,
        updated_at: new Date().toISOString()
      })
      .eq('category_id', categoryId);
    
    if (moveError) {
      console.error('Error moving topics:', moveError);
      throw moveError;
    }
  } else {
    // Otherwise, delete all topics and their posts
    // First get all topic IDs in this category
    const { data: topics, error: topicsError } = await supabase
      .from('forum_topics')
      .select('id')
      .eq('category_id', categoryId);
    
    if (topicsError) {
      console.error('Error fetching topics:', topicsError);
      throw topicsError;
    }
    
    if (topics && topics.length > 0) {
      const topicIds = topics.map(topic => topic.id);
      
      // Delete all posts in these topics
      const { error: postsError } = await supabase
        .from('forum_posts')
        .delete()
        .in('topic_id', topicIds);
      
      if (postsError) {
        console.error('Error deleting posts:', postsError);
        throw postsError;
      }
      
      // Delete all topics
      const { error: deleteTopicsError } = await supabase
        .from('forum_topics')
        .delete()
        .eq('category_id', categoryId);
      
      if (deleteTopicsError) {
        console.error('Error deleting topics:', deleteTopicsError);
        throw deleteTopicsError;
      }
    }
  }
  
  // Delete the category
  const { error: deleteError } = await supabase
    .from('forum_categories')
    .delete()
    .eq('id', categoryId);
  
  if (deleteError) {
    console.error('Error deleting category:', deleteError);
    throw deleteError;
  }
  
  // Log the action
  await logModerationAction({
    actionType: 'delete_category',
    moderatorId: deletedBy,
    contentId: categoryId,
    contentType: 'forum_category',
    reason: `Deleted forum category: ${category.name}`,
    previousContent: JSON.stringify(category),
    additionalData: moveTopicsTo ? { moved_topics_to: moveTopicsTo } : { deleted_topics: true }
  });
  
  return { success: true, message: `Category "${category.name}" deleted successfully` };
}

// Reorder forum categories
export async function reorderForumCategories({
  categoryOrders,
  updatedBy
}: {
  categoryOrders: { id: string; order: number }[];
  updatedBy: string;
}) {
  const supabase = createClient();
  const results = { success: [], failed: [] };
  
  // Process each category
  for (const { id, order } of categoryOrders) {
    try {
      const { error } = await supabase
        .from('forum_categories')
        .update({ 
          display_order: order,
          updated_at: new Date().toISOString(),
          updated_by: updatedBy
        })
        .eq('id', id);
      
      if (error) {
        console.error(`Error updating order for category ${id}:`, error);
        results.failed.push({ id, error: error.message });
        continue;
      }
      
      results.success.push(id);
    } catch (err: any) {
      console.error(`Error processing category ${id}:`, err);
      results.failed.push({ id, error: err.message });
    }
  }
  
  // Log the action if at least one category was updated
  if (results.success.length > 0) {
    await logModerationAction({
      actionType: 'reorder_categories',
      moderatorId: updatedBy,
      contentId: 'multiple',
      contentType: 'forum_category',
      reason: `Reordered ${results.success.length} forum categories`,
      additionalData: { categoryOrders }
    });
  }
  
  return results;
}
