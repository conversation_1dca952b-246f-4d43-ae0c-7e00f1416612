'use server';

import { createActionClient } from '@/lib/auth.server';
import { createClient } from '@supabase/supabase-js';

// Helper: Check if current user is admin
export async function requireAdminRole() {
  const supabase = createActionClient();
  const { data, error } = await supabase.auth.getUser();
  if (error || !data?.user) throw new Error('Not authenticated');
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', data.user.id)
    .single();
  if (!profile || profile.role !== 'admin') throw new Error('Not authorized');
  return data.user;
}

// Helper: Check if current user is moderator or admin
export async function requireModeratorRole() {
  const supabase = createActionClient();
  const { data, error } = await supabase.auth.getUser();
  if (error || !data?.user) throw new Error('Not authenticated');
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', data.user.id)
    .single();
  if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) throw new Error('Not authorized');
  return data.user;
}

// Get paginated users (admin only)
export async function getUsers(
  page: number = 1,
  limit: number = 20,
  role?: 'user' | 'moderator' | 'admin'
) {
  await requireAdminRole();
  const supabase = createActionClient();
  const offset = (page - 1) * limit;
  let query = supabase
    .from('profiles')
    .select('id, username, full_name, email, role, created_at', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
  if (role) query = query.eq('role', role);
  const { data, error, count } = await query;
  if (error) return { error };
  return {
    data,
    pagination: {
      page,
      limit,
      total: count || 0,
      pages: count ? Math.ceil(count / limit) : 0
    }
  };
}

// Update a user's role (admin only)
export async function updateUserRole(
  userId: string,
  role: 'user' | 'moderator' | 'admin'
) {
  await requireAdminRole();
  const supabase = createActionClient();
  const { data, error } = await supabase
    .from('profiles')
    .update({ role })
    .eq('id', userId)
    .select()
    .single();
  if (error) return { error };
  return { data };
}

// Get pending content for moderation
export async function getPendingContent(
  contentType?: 'article' | 'media' | 'comment',
  page: number = 1,
  limit: number = 10
) {
  try {
    // Initialize Supabase client directly since this is a server action
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const offset = (page - 1) * limit;

    // Base query for articles
    let articlesQuery = supabase
      .from('articles')
      .select('id, title, created_at, status, profiles!inner(username, full_name), categories!inner(name, slug)')
      .eq('status', 'pending_review')
      .order('created_at', { ascending: false });

    // Base query for media
    let mediaQuery = supabase
      .from('media')
      .select('id, title, description, file_url, file_type, created_at, status, profiles!inner(username, full_name)')
      .eq('status', 'pending_review')
      .order('created_at', { ascending: false });

    // Base query for comments
    // Note: The comments table doesn't have a status column in the schema
    // For now, we'll just get recent comments for moderation
    let commentsQuery = supabase
      .from('comments')
      .select('id, content, created_at, profiles!inner(username, full_name)')
      .order('created_at', { ascending: false })
      .limit(20); // Limit to recent comments

    let data = [];
    let total = 0;

    // Filter by content type if specified
    if (contentType === 'article') {
      const { data: articles, error, count } = await articlesQuery
        .range(offset, offset + limit - 1);

      if (error) throw error;

      data = articles.map(article => ({
        ...article,
        contentType: 'article',
        excerpt: article.excerpt || ''
      }));

      total = count || 0;
    } else if (contentType === 'media') {
      const { data: media, error, count } = await mediaQuery
        .range(offset, offset + limit - 1);

      if (error) throw error;

      data = media.map(item => ({
        ...item,
        contentType: 'media'
      }));

      total = count || 0;
    } else if (contentType === 'comment') {
      const { data: comments, error } = await commentsQuery;

      if (error) throw error;

      // Apply pagination manually since we're not using range
      const paginatedComments = comments.slice(offset, offset + limit);

      data = paginatedComments.map(comment => ({
        ...comment,
        contentType: 'comment',
        title: `Comment on article`,
        description: comment.content,
        status: 'pending_review' // Add a virtual status for UI consistency
      }));

      total = comments.length;
    } else {
      // Get all content types with pagination applied after combining
      const [articlesResult, mediaResult, commentsResult] = await Promise.all([
        articlesQuery,
        mediaQuery,
        commentsQuery
      ]);

      if (articlesResult.error) throw articlesResult.error;
      if (mediaResult.error) throw mediaResult.error;
      if (commentsResult.error) throw commentsResult.error;

      const allArticles = articlesResult.data.map(article => ({
        ...article,
        contentType: 'article',
        excerpt: article.excerpt || ''
      }));

      const allMedia = mediaResult.data.map(item => ({
        ...item,
        contentType: 'media'
      }));

      const allComments = commentsResult.data.map(comment => ({
        ...comment,
        contentType: 'comment',
        title: `Comment on article`,
        description: comment.content,
        status: 'pending_review' // Add a virtual status for UI consistency
      }));

      // Combine all content and sort by created_at
      const allContent = [...allArticles, ...allMedia, ...allComments]
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      total = allContent.length;

      // Apply pagination manually
      data = allContent.slice(offset, offset + limit);
    }

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error: any) {
    console.error('Error getting pending content:', error);
    return { error: { message: error.message } };
  }
}

// Review content (approve or reject)
export async function reviewContent({
  contentId,
  contentType,
  status,
  reason
}: {
  contentId: string;
  contentType: 'article' | 'media' | 'comment';
  status: 'published' | 'rejected';
  reason?: string;
}) {
  try {
    // Initialize Supabase client directly since this is a server action
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Determine which table to update based on content type
    let table;
    switch (contentType) {
      case 'article':
        table = 'articles';
        break;
      case 'media':
        table = 'media';
        break;
      case 'comment':
        table = 'comments';
        break;
      default:
        throw new Error('Invalid content type');
    }

    // Update the content status
    let data, error;

    if (contentType === 'comment') {
      // For comments, we don't update status (it doesn't exist)
      // Instead, we either keep or delete the comment based on the decision
      if (status === 'rejected') {
        // Delete rejected comments
        const result = await supabase
          .from(table)
          .delete()
          .eq('id', contentId)
          .select()
          .single();

        data = result.data;
        error = result.error;
      } else {
        // For approved comments, just fetch the data
        const result = await supabase
          .from(table)
          .select()
          .eq('id', contentId)
          .single();

        data = result.data;
        error = result.error;
      }
    } else {
      // For articles and media, update the status
      const result = await supabase
        .from(table)
        .update({
          status,
          moderation_notes: reason || null,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', contentId)
        .select()
        .single();

      data = result.data;
      error = result.error;
    }

    if (error) throw error;

    // Log the moderation action
    await supabase
      .from('activity_log')
      .insert({
        action: status === 'published' ? 'content_approved' : 'content_rejected',
        content_id: contentId,
        content_type: contentType,
        content_title: data.title || 'Untitled',
        notes: reason
      });

    return { data };
  } catch (error: any) {
    console.error('Error reviewing content:', error);
    return { error: { message: error.message } };
  }
}