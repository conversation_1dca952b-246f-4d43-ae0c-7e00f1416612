'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getRecentActivity } from '@/lib/admin-data';
import { getModerationActions } from '@/lib/forums';
import { formatDistanceToNow, format } from 'date-fns';
import Link from 'next/link';
import {
  FaUser, FaComments, FaEdit, FaTrash, FaLock, FaUnlock,
  FaThumbtack, FaExchangeAlt, FaCheck, FaExclamationTriangle,
  FaFilter, FaSearch
} from 'react-icons/fa';

export default function AdminActivityPage() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [activity, setActivity] = useState([]);
  const [moderationActions, setModerationActions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'forum', 'user', 'moderation'
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');
  const itemsPerPage = 20;

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        // Redirect to unauthorized page if not admin/moderator
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Load activity data
      loadActivity();
    }

    checkAuth();
  }, [router, user, loading, activeTab, page]);

  async function loadActivity() {
    try {
      setIsLoading(true);

      if (activeTab === 'moderation') {
        try {
          console.log('Loading moderation actions...');
          // Load moderation actions with pagination
          const { data, count, error } = await getModerationActions({
            limit: itemsPerPage,
            offset: (page - 1) * itemsPerPage
          });

          if (error) {
            console.error('Error from getModerationActions:', error);
            setError(`Failed to load moderation actions: ${error}`);
            setModerationActions([]);
            setTotalPages(1);
            return;
          }

          console.log(`Loaded ${data?.length || 0} moderation actions`);
          setModerationActions(data || []);
          setTotalPages(Math.ceil((count || 0) / itemsPerPage));
        } catch (err) {
          console.error('Exception loading moderation actions:', err);
          setError('Failed to load moderation actions. Please try again.');
          setModerationActions([]);
          setTotalPages(1);
        }
      } else {
        // Load general activity
        const data = await getRecentActivity(50);

        // Filter based on activeTab
        let filteredData = data;
        if (activeTab === 'forum') {
          filteredData = data.filter(item => item.activityType === 'forum');
        } else if (activeTab === 'user') {
          filteredData = data.filter(item => item.activityType === 'user');
        }

        // Apply search filter if provided
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          filteredData = filteredData.filter(item => {
            // Search in different fields based on activity type
            if (item.activityType === 'forum') {
              return (
                (item.title && item.title.toLowerCase().includes(query)) ||
                (item.author?.username && item.author.username.toLowerCase().includes(query)) ||
                (item.author?.full_name && item.author.full_name.toLowerCase().includes(query)) ||
                (item.content && item.content.toLowerCase().includes(query))
              );
            } else if (item.activityType === 'user') {
              return (
                (item.username && item.username.toLowerCase().includes(query)) ||
                (item.full_name && item.full_name.toLowerCase().includes(query))
              );
            } else if (item.activityType === 'moderation') {
              return (
                (item.moderator?.username && item.moderator.username.toLowerCase().includes(query)) ||
                (item.reason && item.reason.toLowerCase().includes(query))
              );
            }
            return false;
          });
        }

        // Apply type filter if provided
        if (filterType) {
          filteredData = filteredData.filter(item => item.type === filterType);
        }

        // Paginate the results
        const totalItems = filteredData.length;
        setTotalPages(Math.ceil(totalItems / itemsPerPage));

        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        filteredData = filteredData.slice(startIndex, endIndex);

        setActivity(filteredData);
      }
    } catch (err) {
      console.error('Error loading activity:', err);
      setError('Failed to load activity data');
    } finally {
      setIsLoading(false);
    }
  }

  // Helper function to get icon for activity type
  const getActivityIcon = (item) => {
    if (item.activityType === 'user') {
      return <FaUser className="text-blue-500" />;
    }

    if (item.activityType === 'forum') {
      if (item.type === 'post') {
        return <FaComments className="text-green-500" />;
      }
      return <FaComments className="text-purple-500" />;
    }

    if (item.activityType === 'moderation') {
      switch (item.type) {
        case 'edit_post':
          return <FaEdit className="text-yellow-500" />;
        case 'soft_delete_post':
        case 'hard_delete_post':
        case 'soft_delete_topic':
        case 'hard_delete_topic':
          return <FaTrash className="text-red-500" />;
        case 'lock_topic':
          return <FaLock className="text-gray-500" />;
        case 'unlock_topic':
          return <FaUnlock className="text-green-500" />;
        case 'pin_topic':
        case 'unpin_topic':
          return <FaThumbtack className="text-blue-500" />;
        case 'move_topic':
          return <FaExchangeAlt className="text-purple-500" />;
        case 'mark_solution':
          return <FaCheck className="text-green-500" />;
        default:
          return <FaExclamationTriangle className="text-orange-500" />;
      }
    }

    return <FaExclamationTriangle className="text-gray-500" />;
  };

  // Helper function to get activity description
  const getActivityDescription = (item) => {
    if (item.activityType === 'user') {
      return (
        <span>
          New user <span className="font-medium">{item.username || item.full_name}</span> registered
        </span>
      );
    }

    if (item.activityType === 'forum') {
      if (item.type === 'post') {
        return (
          <span>
            <span className="font-medium">{item.author?.username || 'Anonymous'}</span> replied to{' '}
            <Link href={`/forums/${item.category?.slug}/${item.topic?.slug}`} className="text-nature-green hover:underline">
              {item.topic?.title || 'a topic'}
            </Link>
          </span>
        );
      }
      return (
        <span>
          <span className="font-medium">{item.author?.username || 'Anonymous'}</span> created a new topic{' '}
          <Link href={`/forums/${item.category?.slug}/${item.slug}`} className="text-nature-green hover:underline">
            {item.title}
          </Link>
        </span>
      );
    }

    if (item.activityType === 'moderation') {
      const moderatorName = item.moderator?.username || 'A moderator';
      const actionType = item.type || ''; // Use type which could be from action_type or action

      switch (actionType) {
        case 'edit_post':
          return <span><span className="font-medium">{moderatorName}</span> edited a post</span>;
        case 'soft_delete_post':
          return <span><span className="font-medium">{moderatorName}</span> removed a post</span>;
        case 'hard_delete_post':
          return <span><span className="font-medium">{moderatorName}</span> permanently deleted a post</span>;
        case 'soft_delete_topic':
          return <span><span className="font-medium">{moderatorName}</span> removed a topic</span>;
        case 'hard_delete_topic':
          return <span><span className="font-medium">{moderatorName}</span> permanently deleted a topic</span>;
        case 'lock_topic':
          return <span><span className="font-medium">{moderatorName}</span> locked a topic</span>;
        case 'unlock_topic':
          return <span><span className="font-medium">{moderatorName}</span> unlocked a topic</span>;
        case 'pin_topic':
          return <span><span className="font-medium">{moderatorName}</span> pinned a topic</span>;
        case 'unpin_topic':
          return <span><span className="font-medium">{moderatorName}</span> unpinned a topic</span>;
        case 'move_topic':
          return <span><span className="font-medium">{moderatorName}</span> moved a topic to another category</span>;
        default:
          return <span><span className="font-medium">{moderatorName}</span> performed a moderation action</span>;
      }
    }

    return 'Unknown activity';
  };

  // Helper function to render moderation action details
  const renderModerationActionDetails = (action) => {
    return (
      <div className="mt-2 text-sm">
        <div className="flex items-center text-gray-500">
          <span className="font-medium">Action:</span>
          <span className="ml-2">{((action.action_type || action.action || '') + '').replace(/_/g, ' ')}</span>
        </div>

        {action.reason && (
          <div className="flex items-start mt-1 text-gray-500">
            <span className="font-medium">Reason:</span>
            <span className="ml-2">{action.reason}</span>
          </div>
        )}

        {action.content_type && (
          <div className="flex items-center mt-1 text-gray-500">
            <span className="font-medium">Content:</span>
            <span className="ml-2">{action.content_type}</span>
          </div>
        )}

        {action.previous_content && (
          <div className="mt-2 p-2 bg-gray-50 rounded-md text-xs text-gray-600">
            <div className="font-medium mb-1">Previous content:</div>
            <div className="whitespace-pre-line">{action.previous_content.substring(0, 100)}...</div>
          </div>
        )}

        {action.new_content && (
          <div className="mt-2 p-2 bg-gray-50 rounded-md text-xs text-gray-600">
            <div className="font-medium mb-1">New content:</div>
            <div className="whitespace-pre-line">{action.new_content.substring(0, 100)}...</div>
          </div>
        )}
      </div>
    );
  };

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading activity data...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Activity Log</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => {
                setActiveTab('all');
                setPage(1);
              }}
              className={`px-3 py-1 rounded-md ${
                activeTab === 'all'
                  ? 'bg-nature-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All Activity
            </button>
            <button
              onClick={() => {
                setActiveTab('forum');
                setPage(1);
              }}
              className={`px-3 py-1 rounded-md ${
                activeTab === 'forum'
                  ? 'bg-nature-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Forum Activity
            </button>
            <button
              onClick={() => {
                setActiveTab('user');
                setPage(1);
              }}
              className={`px-3 py-1 rounded-md ${
                activeTab === 'user'
                  ? 'bg-nature-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              User Activity
            </button>
            <button
              onClick={() => {
                setActiveTab('moderation');
                setPage(1);
              }}
              className={`px-3 py-1 rounded-md ${
                activeTab === 'moderation'
                  ? 'bg-nature-green text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Moderation Actions
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              />
              <FaSearch className="absolute left-3 top-3 text-gray-400" />
            </div>

            <div className="relative">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green appearance-none"
              >
                <option value="">All Types</option>
                <option value="post">Forum Posts</option>
                <option value="topic">Forum Topics</option>
                <option value="user_registration">User Registrations</option>
                <option value="edit_post">Edit Actions</option>
                <option value="soft_delete_post">Delete Actions</option>
                <option value="lock_topic">Lock Actions</option>
              </select>
              <FaFilter className="absolute left-3 top-3 text-gray-400" />
            </div>

            <button
              onClick={() => {
                setSearchQuery('');
                setFilterType('');
                loadActivity();
              }}
              className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
            >
              Reset
            </button>
          </div>
        </div>
      </div>

      {/* Activity List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {activeTab === 'moderation' ? (
          // Moderation Actions Table
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Moderator
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Content Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {moderationActions.length > 0 ? (
                moderationActions.map((action) => (
                  <tr key={action.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">
                        {(action.action_type || action.action || '').replace(/_/g, ' ')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {action.moderator?.full_name || action.moderator?.username || 'Unknown'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {action.content_type}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500">
                        {action.reason || 'No reason provided'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {formatDistanceToNow(new Date(action.created_at), { addSuffix: true })}
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                    No moderation actions found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        ) : (
          // General Activity List
          <div className="divide-y divide-gray-200">
            {activity.length > 0 ? (
              activity.map((item) => (
                <div key={item.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start">
                    <div className="bg-gray-100 p-2 rounded-full mr-4 flex-shrink-0">
                      {getActivityIcon(item)}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm">{getActivityDescription(item)}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDistanceToNow(new Date(item.created_at), { addSuffix: true })} •
                        {format(new Date(item.created_at), ' MMM d, yyyy h:mm a')}
                      </p>

                      {item.activityType === 'moderation' && renderModerationActionDetails(item)}

                      {item.activityType === 'forum' && item.type === 'post' && item.content && (
                        <div className="mt-2 p-3 bg-gray-50 rounded-md text-sm text-gray-700">
                          <p className="whitespace-pre-line">{item.content.substring(0, 150)}...</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-6 text-center text-gray-500">
                No activity found
              </div>
            )}
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="flex items-center space-x-2">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setPage(pageNum)}
                  className={`px-3 py-1 rounded-md ${
                    page === pageNum
                      ? 'bg-nature-green text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-3 py-1 rounded-md ${
                page === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Next
            </button>
          </nav>
        </div>
      )}
    </div>
  );
}
