'use client';

import React from 'react';
import { LEVEL_THRESHOLDS } from '@/lib/profile-types';
import { Tooltip } from '@/components/Tooltip';

interface UserLevelProgressProps {
  level: number;
  points: number;
  className?: string;
}

export default function UserLevelProgress({
  level,
  points,
  className = ''
}: UserLevelProgressProps) {
  // Find the current level threshold
  const currentThreshold = LEVEL_THRESHOLDS.find(t => t.level === level) || 
    LEVEL_THRESHOLDS.find(t => t.level > level)?.level ? 
    LEVEL_THRESHOLDS[LEVEL_THRESHOLDS.findIndex(t => t.level > level) - 1] : 
    LEVEL_THRESHOLDS[LEVEL_THRESHOLDS.length - 1];

  // Find the next level threshold
  const nextThreshold = LEVEL_THRESHOLDS.find(t => t.level > level);

  // Calculate progress percentage
  let progressPercentage = 0;
  let pointsToNextLevel = 0;
  
  if (nextThreshold) {
    const currentPoints = currentThreshold.points;
    const nextPoints = nextThreshold.points;
    const pointsRange = nextPoints - currentPoints;
    const userProgress = points - currentPoints;
    pointsToNextLevel = nextPoints - points;
    progressPercentage = Math.min(100, Math.max(0, (userProgress / pointsRange) * 100));
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-100 p-4 ${className}`}>
      <div className="flex items-center mb-3">
        <div className="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 text-green-800 font-bold text-xl mr-3">
          {level}
        </div>
        <div>
          <h3 className="font-semibold text-lg">{currentThreshold.title}</h3>
          <p className="text-gray-600 text-sm">Reputation: {points} points</p>
        </div>
      </div>
      
      {nextThreshold ? (
        <div className="mt-3">
          <div className="flex justify-between text-sm mb-1">
            <span className="text-gray-600">Progress to Level {nextThreshold.level}</span>
            <span className="text-gray-600">{Math.round(progressPercentage)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <Tooltip content={`${pointsToNextLevel} points needed for next level`}>
              <div 
                className="bg-green-500 h-2.5 rounded-full transition-all duration-500" 
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </Tooltip>
          </div>
          <div className="flex justify-between mt-1 text-xs">
            <span className="text-gray-500">{currentThreshold.points} pts</span>
            <span className="text-gray-500">{nextThreshold.points} pts</span>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            <span className="font-medium">{pointsToNextLevel} more points</span> needed to reach <span className="font-medium">Level {nextThreshold.level}: {nextThreshold.title}</span>
          </p>
        </div>
      ) : (
        <p className="text-sm text-gray-600 mt-2">
          You've reached the maximum level! Congratulations!
        </p>
      )}
    </div>
  );
}
