'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getCategories, submitArticle } from '../actions';
import MarkdownEditor from '@/components/MarkdownEditor';
import ReCaptchaComponent from '@/components/ReCaptcha';

type Category = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  parent_id: string | null;
};

export default function CreateArticlePage() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    categoryId: '',
    tags: ''
  });

  useEffect(() => {
    async function loadCategories() {
      try {
        const { data, error } = await getCategories();
        if (error) {
          console.error('Error loading categories:', error);
          return;
        }
        if (data) {
          setCategories(data);
        }
      } catch (err) {
        console.error('Failed to load categories:', err);
      }
    }

    loadCategories();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess(false);

    if (!formData.title || !formData.content || !formData.categoryId) {
      setError('Title, content, and category are required');
      return;
    }

    if (!recaptchaToken) {
      setError('Please complete the reCAPTCHA verification');
      return;
    }

    try {
      setLoading(true);

      // Process tags if provided
      const tags = formData.tags
        ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
        : [];

      const result = await submitArticle({
        title: formData.title,
        content: formData.content,
        excerpt: formData.excerpt,
        categoryId: formData.categoryId,
        tags,
        recaptchaToken
      });

      if (result.error) {
        setError(result.error.message);
        return;
      }

      setSuccess(true);
      // Reset form
      setFormData({
        title: '',
        content: '',
        excerpt: '',
        categoryId: '',
        tags: ''
      });

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/contribute?submitted=true');
      }, 2000);

    } catch (err) {
      console.error('Error submitting article:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Create a New Article</h1>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          Your article has been submitted for review. Thank you for your contribution!
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-gray-700 font-medium mb-2">
            Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
            placeholder="Enter article title"
            required
          />
        </div>

        <div>
          <label htmlFor="category" className="block text-gray-900 font-medium mb-2">
            Category *
          </label>
          <select
            id="categoryId"
            name="categoryId"
            value={formData.categoryId}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent text-gray-900 bg-white"
            required
          >
            <option value="" className="text-gray-900 bg-white">Select a category</option>
            {categories.map(category => (
              <option key={category.id} value={category.id} className="text-gray-900 bg-white">
                {category.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="excerpt" className="block text-gray-700 font-medium mb-2">
            Excerpt (optional)
          </label>
          <input
            type="text"
            id="excerpt"
            name="excerpt"
            value={formData.excerpt}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
            placeholder="Brief summary of the article (will be auto-generated if left empty)"
          />
        </div>

        <div>
          <label htmlFor="tags" className="block text-gray-700 font-medium mb-2">
            Tags (optional)
          </label>
          <input
            type="text"
            id="tags"
            name="tags"
            value={formData.tags}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
            placeholder="Enter tags separated by commas (e.g., herbal, remedy, natural)"
          />
        </div>

        <div>
          <MarkdownEditor
            label="Content *"
            id="content"
            name="content"
            initialValue={formData.content}
            onChange={(value) => setFormData(prev => ({ ...prev, content: value }))}
            placeholder="Write your article content here. Markdown formatting is supported."
            minHeight="400px"
            required
          />
        </div>

        <div className="mb-6">
          <label className="block text-gray-700 font-medium mb-2">
            Verify you're human *
          </label>
          <ReCaptchaComponent
            onChange={setRecaptchaToken}
            onExpired={() => setRecaptchaToken(null)}
          />
        </div>

        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {loading ? 'Submitting...' : 'Submit for Review'}
          </button>
        </div>
      </form>
    </div>
  );
}