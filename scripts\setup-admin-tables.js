// This script sets up the necessary tables for the admin dashboard
// Run with: node scripts/setup-admin-tables.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

async function setupAdminTables() {
  console.log('Setting up admin tables...');

  // Create Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Create articles table
  console.log('Creating articles table...');

  // Check if articles table exists
  const { data: articlesExists, error: checkArticlesError } = await supabase
    .from('articles')
    .select('id')
    .limit(1);

  let articlesError = null;

  if (checkArticlesError && checkArticlesError.code === 'PGRST204') {
    // Table doesn't exist, create it
    const { error } = await supabase
      .from('articles')
      .insert({
        id: '00000000-0000-0000-0000-000000000000',
        title: 'Temporary Row',
        user_id: '00000000-0000-0000-0000-000000000000',
        content: '',
        status: 'draft'
      });

    if (error && error.code !== '23505') { // Ignore duplicate key errors
      articlesError = error;
    }
  } else if (checkArticlesError) {
    articlesError = checkArticlesError;
  }

  if (articlesError) {
    console.error('Error creating articles table:', articlesError.message);
    return;
  }

  // Create activity_log table
  console.log('Creating activity_log table...');

  // Check if activity_log table exists
  const { data: activityExists, error: checkActivityError } = await supabase
    .from('activity_log')
    .select('id')
    .limit(1);

  let activityError = null;

  if (checkActivityError && checkActivityError.code === 'PGRST204') {
    // Table doesn't exist, create it
    const { error } = await supabase
      .from('activity_log')
      .insert({
        id: '00000000-0000-0000-0000-000000000000',
        user_id: '00000000-0000-0000-0000-000000000000',
        action: 'temporary',
        content_title: 'Temporary Row'
      });

    if (error && error.code !== '23505') { // Ignore duplicate key errors
      activityError = error;
    }
  } else if (checkActivityError) {
    activityError = checkActivityError;
  }

  if (activityError) {
    console.error('Error creating activity_log table:', activityError.message);
    return;
  }

  // Insert sample data
  console.log('Inserting sample data...');

  // Get admin user
  const { data: { users }, error: userError } = await supabase.auth.admin.listUsers();

  if (userError) {
    console.error('Error fetching users:', userError.message);
    return;
  }

  const adminUser = users.find(user => {
    return user.user_metadata?.role === 'admin' || user.email?.includes('admin');
  });

  if (!adminUser) {
    console.error('No admin user found');
    return;
  }

  // Insert sample articles
  const sampleArticles = [
    {
      title: 'The Benefits of Ashwagandha',
      content: 'Ashwagandha is an adaptogenic herb that has been used in Ayurvedic medicine for centuries...',
      user_id: adminUser.id,
      category: 'Herbal Remedies',
      tags: ['herbs', 'adaptogens', 'stress-relief'],
      status: 'pending'
    },
    {
      title: 'Acupuncture for Back Pain',
      content: 'Acupuncture is a traditional Chinese medicine practice that involves inserting thin needles into specific points on the body...',
      user_id: adminUser.id,
      category: 'Alternative Therapies',
      tags: ['acupuncture', 'pain-relief', 'traditional-medicine'],
      status: 'pending'
    },
    {
      title: 'The Health Benefits of Ginger',
      content: 'Ginger is a flowering plant whose rhizome, ginger root or ginger, is widely used as a spice and a folk medicine...',
      user_id: adminUser.id,
      category: 'Herbal Remedies',
      tags: ['herbs', 'digestion', 'anti-inflammatory'],
      status: 'published'
    }
  ];

  for (const article of sampleArticles) {
    const { error: insertError } = await supabase
      .from('articles')
      .upsert(article, { onConflict: 'title' });

    if (insertError) {
      console.error(`Error inserting article "${article.title}":`, insertError.message);
    }
  }

  // Insert sample activity logs
  const { data: articles, error: articlesQueryError } = await supabase
    .from('articles')
    .select('id, title')
    .limit(3);

  if (articlesQueryError) {
    console.error('Error fetching articles:', articlesQueryError.message);
    return;
  }

  if (articles && articles.length > 0) {
    const sampleActivities = [
      {
        user_id: adminUser.id,
        action: 'approved',
        content_id: articles[2]?.id,
        content_type: 'article',
        content_title: articles[2]?.title
      },
      {
        user_id: adminUser.id,
        action: 'requested_changes',
        content_id: articles[1]?.id,
        content_type: 'article',
        content_title: articles[1]?.title
      },
      {
        user_id: adminUser.id,
        action: 'rejected',
        content_id: null,
        content_type: 'media',
        content_title: 'Unlicensed Plant Photos'
      }
    ];

    for (const activity of sampleActivities) {
      const { error: insertError } = await supabase
        .from('activity_log')
        .upsert(activity, { ignoreDuplicates: true });

      if (insertError) {
        console.error(`Error inserting activity:`, insertError.message);
      }
    }
  }

  console.log('Setup completed successfully!');
}

setupAdminTables()
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
