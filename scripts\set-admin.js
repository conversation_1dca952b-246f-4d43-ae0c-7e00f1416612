// This script sets a user as an admin in the Supabase database
// Run with: node scripts/set-admin.js <email>

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const email = process.argv[2];

if (!email) {
  console.error('Please provide an email address');
  console.error('Usage: node scripts/set-admin.js <email>');
  process.exit(1);
}

async function setUserAsAdmin(email) {
  console.log(`Setting user ${email} as admin...`);
  
  // Create Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  // Get user by email
  const { data: { users }, error: userError } = await supabase.auth.admin.listUsers();
  
  if (userError) {
    console.error('Error fetching users:', userError.message);
    return;
  }
  
  const user = users.find(u => u.email === email);
  
  if (!user) {
    console.error(`User with email ${email} not found`);
    return;
  }
  
  console.log(`Found user: ${user.id}`);
  
  // Check if profile exists
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();
  
  if (profileError && profileError.code !== 'PGRST116') {
    console.error('Error fetching profile:', profileError.message);
    return;
  }
  
  if (profile) {
    // Update existing profile
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ role: 'admin' })
      .eq('id', user.id);
    
    if (updateError) {
      console.error('Error updating profile:', updateError.message);
      return;
    }
  } else {
    // Create new profile
    const { error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        username: email.split('@')[0],
        full_name: 'Admin User',
        role: 'admin'
      });
    
    if (insertError) {
      console.error('Error creating profile:', insertError.message);
      return;
    }
  }
  
  console.log(`Successfully set ${email} as admin`);
}

setUserAsAdmin(email)
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
