'use client';

import React, { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';

type Heading = {
  id: string;
  text: string;
  level: number;
};

export default function TableOfContents() {
  const [headings, setHeadings] = useState<Heading[]>([]);
  const [activeId, setActiveId] = useState<string>('');

  // Set up intersection observer for each heading
  useEffect(() => {
    // Find all headings in the article content
    const articleContent = document.querySelector('.article-content');
    if (!articleContent) return;

    const headingElements = articleContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    // Process headings
    const headingsData: Heading[] = Array.from(headingElements).map((heading, index) => {
      // Create an ID if the heading doesn't have one
      if (!heading.id) {
        heading.id = `heading-${index}`;
      }
      
      return {
        id: heading.id,
        text: heading.textContent || '',
        level: parseInt(heading.tagName.substring(1)) // Extract level from tag name (H1 -> 1)
      };
    });
    
    setHeadings(headingsData);

    // Set up intersection observers for each heading
    const observers: IntersectionObserver[] = [];
    const observerOptions = {
      rootMargin: '-100px 0px -80% 0px',
      threshold: 1.0
    };

    headingElements.forEach((heading) => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      }, observerOptions);

      observer.observe(heading);
      observers.push(observer);
    });

    // Clean up observers
    return () => {
      observers.forEach(observer => observer.disconnect());
    };
  }, []);

  // If no headings found, don't render the component
  if (headings.length === 0) {
    return null;
  }

  return (
    <div className="toc-container sticky top-24 max-h-[calc(100vh-120px)] overflow-y-auto p-4 bg-gray-50 rounded-lg border border-gray-200">
      <h3 className="text-lg font-semibold mb-3 text-gray-900">Table of Contents</h3>
      <nav>
        <ul className="space-y-2">
          {headings.map((heading) => (
            <li 
              key={heading.id}
              style={{ 
                paddingLeft: `${(heading.level - 1) * 0.75}rem`,
                borderLeft: activeId === heading.id ? '2px solid #2e7d32' : '2px solid transparent',
              }}
              className="transition-all duration-200"
            >
              <a 
                href={`#${heading.id}`}
                className={`block py-1 px-2 text-sm hover:text-nature-green transition-colors ${
                  activeId === heading.id 
                    ? 'text-nature-green font-medium' 
                    : 'text-gray-700'
                }`}
                onClick={(e) => {
                  e.preventDefault();
                  document.getElementById(heading.id)?.scrollIntoView({
                    behavior: 'smooth'
                  });
                }}
              >
                {heading.text}
              </a>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
