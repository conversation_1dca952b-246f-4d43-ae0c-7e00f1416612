'use client';

import React from 'react';
import Link from 'next/link';

interface SiteLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export default function SiteLogo({
  size = 'md',
  className = ''
}: SiteLogoProps) {

  return (
    <Link href="/" className={`block hover:opacity-90 transition-all duration-300 transform hover:scale-105 ${className}`}>
      <div className="bg-white rounded-lg px-3 py-2 shadow-md border border-gray-200 inline-block">
        <img
          src="/images/nature-heals-logo.png"
          alt="NatureHeals.info Logo"
          className="w-auto object-contain"
          style={{
            height: size === 'sm' ? '45px' : size === 'md' ? '55px' : size === 'lg' ? '70px' : '90px',
            width: 'auto',
            maxWidth: '220px'
          }}
        />
      </div>
    </Link>
  );
}
