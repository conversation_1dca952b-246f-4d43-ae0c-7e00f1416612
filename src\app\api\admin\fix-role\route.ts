import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function GET(request: NextRequest) {
  try {
    const userId = '2ef29e7d-a677-4926-ab93-ff2e5a4c0436'; // Hardcoded your user ID
    
    // Check current role
    const { data: currentProfile, error: fetchError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (fetchError) {
      if (fetchError.message.includes('does not exist')) {
        // Create profiles table if it doesn't exist
        await supabaseAdmin.sql(`
          CREATE TABLE IF NOT EXISTS public.profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            username TEXT UNIQUE,
            full_name TEXT,
            avatar_url TEXT,
            website TEXT,
            bio TEXT,
            role TEXT DEFAULT 'user',
            level INTEGER DEFAULT 1,
            reputation INTEGER DEFAULT 0,
            is_verified_expert BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
          );
          
          -- Enable RLS on profiles table
          ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
          
          -- Create policies
          CREATE POLICY "Public profiles are viewable by everyone" 
          ON public.profiles 
          FOR SELECT 
          USING (true);
          
          CREATE POLICY "Users can update their own profiles" 
          ON public.profiles 
          FOR UPDATE 
          USING (auth.uid() = id);
        `);
        
        // Get user info from auth.users
        const { data: authUser } = await supabaseAdmin
          .from('auth.users')
          .select('email')
          .eq('id', userId)
          .single();
        
        // Insert profile with admin role
        const { error: insertError } = await supabaseAdmin
          .from('profiles')
          .insert({
            id: userId,
            username: authUser?.email?.split('@')[0] || 'admin',
            role: 'admin'
          });
        
        if (insertError) {
          return NextResponse.json({ 
            error: `Failed to create profile: ${insertError.message}`,
            action: 'create_profile_failed'
          }, { status: 500 });
        }
        
        return NextResponse.json({ 
          message: 'Created new profile with admin role',
          action: 'created_profile'
        });
      }
      
      return NextResponse.json({ 
        error: `Failed to fetch profile: ${fetchError.message}`,
        action: 'fetch_failed'
      }, { status: 500 });
    }
    
    // Update role to admin if not already
    if (currentProfile.role !== 'admin') {
      const { error: updateError } = await supabaseAdmin
        .from('profiles')
        .update({ role: 'admin' })
        .eq('id', userId);
      
      if (updateError) {
        return NextResponse.json({ 
          error: `Failed to update role: ${updateError.message}`,
          action: 'update_failed'
        }, { status: 500 });
      }
      
      return NextResponse.json({ 
        message: 'Updated role to admin',
        previousRole: currentProfile.role,
        action: 'updated_role'
      });
    }
    
    // If already admin, try to force a refresh of the auth session
    const { data: authUser } = await supabaseAdmin.auth.admin.getUserById(userId);
    
    return NextResponse.json({ 
      message: 'Role is already admin',
      profile: currentProfile,
      authUser: authUser?.user,
      action: 'already_admin'
    });
  } catch (error: any) {
    console.error('Error in fix-role API:', error);
    return NextResponse.json({ 
      error: error.message || 'An unknown error occurred',
      action: 'exception'
    }, { status: 500 });
  }
}
