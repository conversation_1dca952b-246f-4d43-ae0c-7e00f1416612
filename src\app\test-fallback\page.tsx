'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

// Sample categories that will always be available
const SAMPLE_CATEGORIES = [
  {
    id: '1',
    name: 'Herbal Remedies',
    slug: 'herbal-remedies',
    description: 'Natural plant-based medicines and treatments',
    icon: 'leaf'
  },
  {
    id: '2',
    name: 'Nutritional Healing',
    slug: 'nutritional-healing',
    description: 'Healing through food and dietary approaches',
    icon: 'cake'
  },
  {
    id: '3',
    name: 'Traditional Medicine',
    slug: 'traditional-medicine',
    description: 'Ancient healing practices from around the world',
    icon: 'academic-cap'
  }
];

export default function TestFallback() {
  const [apiCategories, setApiCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [apiError, setApiError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCategories() {
      try {
        setLoading(true);
        // Try to fetch from the API
        const response = await fetch('/api/categories?t=' + new Date().getTime());
        const data = await response.json();
        
        if (Array.isArray(data)) {
          setApiCategories(data);
        } else {
          setApiError('API did not return an array');
        }
      } catch (err: any) {
        console.error('Error fetching from API:', err);
        setApiError(err.message || 'Error fetching categories');
      } finally {
        setLoading(false);
      }
    }
    
    fetchCategories();
  }, []);

  // Function to render an icon based on its name
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'leaf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      case 'cake':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.701 2.701 0 01-1.5.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zm-3-9v-2a2 2 0 00-2-2H8a2 2 0 00-2 2v2h12z" />
          </svg>
        );
      case 'academic-cap':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path d="M12 14l9-5-9-5-9 5 9 5z" />
            <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
        );
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Fallback Categories Test</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Hardcoded Sample Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {SAMPLE_CATEGORIES.map((category) => (
            <div key={category.id} className="p-6 bg-white rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:border-green-200 group">
              <div className="bg-[#2e7d32]/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4 text-[#2e7d32] group-hover:bg-[#2e7d32]/20 transition-all duration-300 shadow-sm">
                {renderIcon(category.icon || 'bookmark')}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">{category.name}</h3>
              <div className="h-px w-16 bg-green-200 mb-4"></div>
              <p className="text-gray-700 mb-4">{category.description}</p>
              <a
                href={`/categories/${category.slug}`}
                className="text-[#2e7d32] font-medium hover:underline flex items-center transition-all duration-200 transform hover:translate-x-1"
              >
                Explore
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 ml-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Categories from API</h2>
        {loading ? (
          <div className="text-center py-6">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green mb-4"></div>
            <p className="text-gray-600">Loading categories from API...</p>
          </div>
        ) : apiError ? (
          <div className="bg-red-50 p-4 rounded-md text-red-700 mb-4">
            <p className="font-bold">Error loading categories from API:</p>
            <p>{apiError}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {apiCategories.map((category) => (
              <div key={category.id} className="p-6 bg-white rounded-lg shadow-md border border-gray-100">
                <div className="bg-[#2e7d32]/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4 text-[#2e7d32]">
                  {renderIcon(category.icon || 'bookmark')}
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-900">{category.name}</h3>
                <div className="h-px w-16 bg-green-200 mb-4"></div>
                <p className="text-gray-700 mb-4">{category.description}</p>
                <p className="text-sm text-gray-500">From API</p>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="mt-6">
        <Link href="/" className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
          Back to Home
        </Link>
      </div>
    </div>
  );
}
