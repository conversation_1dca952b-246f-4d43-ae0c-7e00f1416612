'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { ProfileVisibility } from '@/lib/profile-types';
import { revalidatePath } from 'next/cache';

interface ProfileUpdateData {
  id: string;
  username: string;
  full_name?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatar_url?: string;
  avatar_type?: string;
  preset_avatar?: string;
  visibility: ProfileVisibility;
  show_email: boolean;
  show_location: boolean;
  interests?: string[];
  expertise_level?: string;
  healing_philosophy?: string;
  favorite_herbs?: string[];
  social_links?: {
    twitter?: string;
    instagram?: string;
    facebook?: string;
    pinterest?: string;
    youtube?: string;
  };
  spirit_plant?: string;
}

export async function directUpdateProfile(profileData: ProfileUpdateData) {
  try {
    // Create a Supabase client with proper cookie handling for server actions
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            try {
              return cookieStore.get(name)?.value;
            } catch (error) {
              console.error('Error getting cookie:', error);
              return undefined;
            }
          },
          set(name: string, value: string, options: any) {
            try {
              cookieStore.set({ name, value, ...options });
            } catch (error) {
              console.error('Error setting cookie:', error);
            }
          },
          remove(name: string, options: any) {
            try {
              cookieStore.set({ name, value: '', ...options });
            } catch (error) {
              console.error('Error removing cookie:', error);
            }
          },
        },
      }
    );

    // Get the current session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    const session = sessionData.session;
    
    console.log('Session in direct update:', session ? 'Session found' : 'No session', 
                sessionError ? `Error: ${sessionError.message}` : 'No error');

    if (!session) {
      return { error: 'Unauthorized - No valid session found' };
    }

    // Make sure the user can only update their own profile
    console.log('Profile ID check:', profileData.id, session.user.id);
    if (profileData.id !== session.user.id) {
      return { error: 'You can only update your own profile' };
    }

    // Update the profile
    const { error } = await supabase
      .from('profiles')
      .upsert({
        ...profileData,
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error updating profile:', error);
      return {
        error: error.message || 'Failed to update profile',
        details: error.details,
        hint: error.hint,
        code: error.code
      };
    }

    // Revalidate the profile page
    revalidatePath('/profile');
    revalidatePath(`/profile/${profileData.username}`);

    return { success: true };
  } catch (error: any) {
    console.error('Error in directUpdateProfile action:', error);
    return {
      error: error.message || 'An unexpected error occurred',
      details: error.details,
      stack: error.stack
    };
  }
}
