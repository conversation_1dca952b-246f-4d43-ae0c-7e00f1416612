'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import Link from 'next/link';

export default function TestCategoriesDirect() {
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [tables, setTables] = useState<string[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<string>('Checking...');

  useEffect(() => {
    async function checkDatabase() {
      try {
        setLoading(true);
        
        // First try the debug endpoint
        const debugResponse = await fetch('/api/debug?' + new Date().getTime());
        const debugData = await debugResponse.json();
        setDebugInfo(debugData);
        
        if (debugData.status === 'Connected') {
          setConnectionStatus('Connected to Supabase');
          setTables(debugData.tables || []);
          
          if (debugData.categorySample && debugData.categorySample.length > 0) {
            setCategories(debugData.categorySample);
            setLoading(false);
            return;
          }
        } else {
          setConnectionStatus(`Connection issue: ${debugData.error || 'Unknown error'}`);
        }
        
        // Try direct Supabase connection
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseKey) {
          throw new Error('Missing Supabase environment variables');
        }
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Try to fetch categories directly
        const { data, error } = await supabase
          .from('categories')
          .select('*')
          .order('name');
          
        if (error) {
          throw new Error(`Supabase query error: ${error.message}`);
        }
        
        setCategories(data || []);
      } catch (err: any) {
        console.error('Error:', err);
        setError(err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    }

    checkDatabase();
  }, []);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Database Connection Test</h1>
      
      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Connection Status</h2>
        <p className={connectionStatus.includes('Connected') ? 'text-green-600' : 'text-red-600'}>
          {connectionStatus}
        </p>
      </div>
      
      {tables.length > 0 && (
        <div className="mb-6 p-4 bg-gray-100 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Database Tables</h2>
          <ul className="list-disc pl-5">
            {tables.map((table, index) => (
              <li key={index} className={table === 'categories' ? 'text-green-600 font-bold' : ''}>
                {table} {table === 'categories' && '✓'}
              </li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-4">Categories ({categories.length})</h2>

        {loading ? (
          <p>Loading categories...</p>
        ) : categories.length === 0 ? (
          <p>No categories found.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map(category => (
              <div key={category.id} className="border border-gray-200 rounded-md p-4">
                <h3 className="font-bold">{category.name}</h3>
                <p className="text-sm text-gray-500 mb-2">Slug: {category.slug}</p>
                {category.description && (
                  <p className="text-sm mb-2">{category.description}</p>
                )}
                <p className="text-sm text-gray-500">Icon: {category.icon || 'None'}</p>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {error && (
        <div className="p-4 bg-red-100 text-red-700 rounded-lg mb-6">
          <h2 className="font-bold mb-2">Error</h2>
          <p>{error}</p>
        </div>
      )}
      
      {debugInfo && (
        <div className="p-4 bg-gray-100 rounded-lg overflow-auto">
          <h2 className="text-lg font-semibold mb-2">Debug Information</h2>
          <pre className="text-xs">{JSON.stringify(debugInfo, null, 2)}</pre>
        </div>
      )}
      
      <div className="mt-6">
        <Link href="/" className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
          Back to Home
        </Link>
      </div>
    </div>
  );
}
