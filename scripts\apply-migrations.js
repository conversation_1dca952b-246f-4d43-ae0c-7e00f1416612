// Script to apply database migrations
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service key. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration(filePath) {
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    console.log(`Applying migration: ${path.basename(filePath)}`);
    
    // Execute the SQL directly using the rpc function
    const { error } = await supabase.rpc('execute_sql', { sql });
    
    if (error) {
      console.error(`Error applying migration ${path.basename(filePath)}:`, error);
      return false;
    }
    
    console.log(`Successfully applied migration: ${path.basename(filePath)}`);
    return true;
  } catch (err) {
    console.error(`Error reading or applying migration ${path.basename(filePath)}:`, err);
    return false;
  }
}

async function applyMigrations() {
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
  
  try {
    // Check if migrations directory exists
    if (!fs.existsSync(migrationsDir)) {
      console.error('Migrations directory not found.');
      return;
    }
    
    // Get all SQL files in the migrations directory
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to apply in order
    
    if (migrationFiles.length === 0) {
      console.log('No migration files found.');
      return;
    }
    
    console.log(`Found ${migrationFiles.length} migration files.`);
    
    // Apply each migration
    let successCount = 0;
    for (const file of migrationFiles) {
      const filePath = path.join(migrationsDir, file);
      const success = await applyMigration(filePath);
      if (success) successCount++;
    }
    
    console.log(`Applied ${successCount} of ${migrationFiles.length} migrations.`);
  } catch (err) {
    console.error('Error applying migrations:', err);
  }
}

// Create the execute_sql function if it doesn't exist
async function createExecuteSqlFunction() {
  const { error } = await supabase.rpc('execute_sql', { 
    sql: `
      CREATE OR REPLACE FUNCTION execute_sql(sql text)
      RETURNS void AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `
  }).catch(() => {
    // If the function doesn't exist yet, create it directly
    return supabase.from('_rpc').select('*').limit(1).then(() => {
      return supabase.rpc('execute_sql', { 
        sql: `
          CREATE OR REPLACE FUNCTION execute_sql(sql text)
          RETURNS void AS $$
          BEGIN
            EXECUTE sql;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
        `
      });
    });
  });
  
  if (error) {
    console.error('Error creating execute_sql function:', error);
    return false;
  }
  
  return true;
}

// Main function
async function main() {
  console.log('Starting database migration...');
  
  // Create the execute_sql function if needed
  const functionCreated = await createExecuteSqlFunction();
  if (!functionCreated) {
    console.error('Failed to create execute_sql function. Aborting migrations.');
    return;
  }
  
  // Apply migrations
  await applyMigrations();
  
  console.log('Migration process completed.');
}

main().catch(err => {
  console.error('Unhandled error during migration:', err);
  process.exit(1);
});
