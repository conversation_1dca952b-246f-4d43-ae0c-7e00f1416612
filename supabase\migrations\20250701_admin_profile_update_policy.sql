-- Add policy to allow admins to update any user's profile

DO $$
BEGIN
    -- First, drop the policy if it exists to ensure we're creating a fresh one
    DROP POLICY IF EXISTS "Admins can update any profile" ON public.profiles;
    
    -- Create policy for admins to update any profile
    -- This policy allows admins to update ANY profile (not just their own)
    CREATE POLICY "Admins can update any profile"
        ON public.profiles FOR UPDATE
        TO authenticated
        USING (
            EXISTS (
                SELECT 1 FROM public.profiles
                WHERE id = auth.uid() AND role = 'admin'
            )
        )
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM public.profiles
                WHERE id = auth.uid() AND role = 'admin'
            )
        );
    
    RAISE NOTICE 'Created policy: Ad<PERSON> can update any profile';
END
$$;