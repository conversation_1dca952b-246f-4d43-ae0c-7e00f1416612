'use client';

import React, { useState, useEffect } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkHtml from 'remark-html';

interface MarkdownEditorProps {
  initialValue?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minHeight?: string;
  label?: string;
  required?: boolean;
  id?: string;
  name?: string;
}

export default function MarkdownEditor({
  initialValue = '',
  onChange,
  placeholder = 'Write your content here...',
  minHeight = '300px',
  label,
  required = false,
  id = 'markdown-editor',
  name = 'content'
}: MarkdownEditorProps) {
  const [content, setContent] = useState(initialValue);
  const [preview, setPreview] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSplitView, setIsSplitView] = useState(false);

  // Generate preview HTML from markdown
  useEffect(() => {
    const generatePreview = async () => {
      if (!content) {
        setPreview('<p class="text-gray-400">Nothing to preview</p>');
        return;
      }

      try {
        const result = unified()
          .use(remarkParse)
          .use(remarkHtml)
          .processSync(content);
        
        setPreview(result.toString());
      } catch (error) {
        console.error('Error parsing markdown:', error);
        setPreview('<p class="text-red-500">Error parsing markdown</p>');
      }
    };

    generatePreview();
  }, [content]);

  // Handle content change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    onChange(newContent);
  };

  // Insert markdown syntax
  const insertSyntax = (syntax: string, placeholder = '', selectionOffset = 0) => {
    const textarea = document.getElementById(id) as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    const beforeText = textarea.value.substring(0, start);
    const afterText = textarea.value.substring(end);

    let insertedText;
    if (selectedText) {
      insertedText = syntax.replace(placeholder, selectedText);
    } else {
      insertedText = syntax;
    }

    const newText = beforeText + insertedText + afterText;
    setContent(newText);
    onChange(newText);

    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      if (selectedText) {
        textarea.setSelectionRange(start, start + insertedText.length);
      } else {
        const newPosition = start + syntax.indexOf(placeholder) + selectionOffset;
        textarea.setSelectionRange(newPosition, newPosition + placeholder.length);
      }
    }, 0);
  };

  return (
    <div className="markdown-editor">
      {label && (
        <label htmlFor={id} className="block text-gray-700 font-medium mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      <div className="border border-gray-300 rounded-md overflow-hidden">
        {/* Toolbar */}
        <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap items-center gap-1">
          <button
            type="button"
            onClick={() => insertSyntax('# Heading', 'Heading')}
            className="p-1 hover:bg-gray-200 rounded"
            title="Heading 1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
            </svg>
          </button>
          <button
            type="button"
            onClick={() => insertSyntax('**Bold**', 'Bold', 2)}
            className="p-1 hover:bg-gray-200 rounded"
            title="Bold"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </button>
          <button
            type="button"
            onClick={() => insertSyntax('*Italic*', 'Italic', 1)}
            className="p-1 hover:bg-gray-200 rounded"
            title="Italic"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h8" />
            </svg>
          </button>
          <button
            type="button"
            onClick={() => insertSyntax('[Link Text](https://example.com)', 'Link Text', 1)}
            className="p-1 hover:bg-gray-200 rounded"
            title="Link"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.172 13.828a4 4 0 005.656 0l4-4a4 4 0 10-5.656-5.656l-1.102 1.101" />
            </svg>
          </button>
          <button
            type="button"
            onClick={() => insertSyntax('- List item', 'List item')}
            className="p-1 hover:bg-gray-200 rounded"
            title="Unordered List"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
          </button>
          <button
            type="button"
            onClick={() => insertSyntax('1. List item', 'List item')}
            className="p-1 hover:bg-gray-200 rounded"
            title="Ordered List"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </button>
          <button
            type="button"
            onClick={() => insertSyntax('> Blockquote', 'Blockquote')}
            className="p-1 hover:bg-gray-200 rounded"
            title="Blockquote"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          </button>
          <button
            type="button"
            onClick={() => insertSyntax('```\ncode\n```', 'code')}
            className="p-1 hover:bg-gray-200 rounded"
            title="Code Block"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
          </button>

          <div className="ml-auto flex gap-2">
            <button
              type="button"
              onClick={() => {
                setIsPreviewMode(false);
                setIsSplitView(false);
              }}
              className={`px-2 py-1 rounded ${!isPreviewMode && !isSplitView ? 'bg-nature-green text-white' : 'hover:bg-gray-200'}`}
            >
              Edit
            </button>
            <button
              type="button"
              onClick={() => {
                setIsPreviewMode(false);
                setIsSplitView(true);
              }}
              className={`px-2 py-1 rounded ${isSplitView ? 'bg-nature-green text-white' : 'hover:bg-gray-200'}`}
            >
              Split
            </button>
            <button
              type="button"
              onClick={() => {
                setIsPreviewMode(true);
                setIsSplitView(false);
              }}
              className={`px-2 py-1 rounded ${isPreviewMode && !isSplitView ? 'bg-nature-green text-white' : 'hover:bg-gray-200'}`}
            >
              Preview
            </button>
          </div>
        </div>

        {/* Editor/Preview Area */}
        <div className={`${isSplitView ? 'grid grid-cols-2 divide-x' : 'block'}`}>
          {/* Editor */}
          {(!isPreviewMode || isSplitView) && (
            <div className="editor-container">
              <textarea
                id={id}
                name={name}
                value={content}
                onChange={handleChange}
                placeholder={placeholder}
                className="w-full p-4 focus:outline-none resize-y"
                style={{ minHeight }}
                required={required}
              ></textarea>
            </div>
          )}

          {/* Preview */}
          {(isPreviewMode || isSplitView) && (
            <div 
              className="preview-container p-4 prose prose-sm max-w-none overflow-auto"
              style={{ minHeight }}
              dangerouslySetInnerHTML={{ __html: preview }}
            ></div>
          )}
        </div>
      </div>

      <div className="mt-2 text-sm text-gray-500">
        <p>
          Supports Markdown formatting. You can use # for headings, * for lists, ** for bold, etc.
        </p>
      </div>
    </div>
  );
}
