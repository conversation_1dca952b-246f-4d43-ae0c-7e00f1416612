'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { getForumCategoryBySlug, getTopicsByCategory } from '@/lib/forums';
import { ForumCategory, ForumTopic } from '@/lib/forum-types';
import Link from 'next/link';
import UserLevelDisplay from '@/components/UserLevelDisplay';
import VerificationBadge from '@/components/VerificationBadge';
import { formatDistanceToNow } from 'date-fns';
import { motion as Motion, AnimatePresence } from 'framer-motion';
import {
  FaLeaf, FaApple,
  FaYinYang, FaBookMedical, FaMicroscope, FaQuestion
} from 'react-icons/fa';
import {
  FaRegComments, FaRegLightbulb, FaRegClock,
  FaRegUser, FaRegBookmark, FaRegEye, FaRegBell
} from 'react-icons/fa';
import { GiMeditation } from 'react-icons/gi';
import { HiOutlinePencilSquare, HiChevronLeft, HiChevronRight, HiArrowLongLeft } from 'react-icons/hi2';

// Helper function to get icon based on category slug
const getCategoryIcon = (slug: string) => {
  const iconMap: Record<string, React.ReactNode> = {
    'general-discussion': <FaRegComments className="text-nature-green" />,
    'herbal-remedies': <FaLeaf className="text-nature-green" />,
    'nutrition-diet': <FaApple className="text-nature-green" />,
    'mind-body-practices': <GiMeditation className="text-nature-green" />,
    'traditional-medicine': <FaYinYang className="text-nature-green" />,
    'success-stories': <FaRegLightbulb className="text-nature-green" />,
    'research-science': <FaMicroscope className="text-nature-green" />,
    'questions-help': <FaQuestion className="text-nature-green" />
  };

  return iconMap[slug] || <FaRegBookmark className="text-nature-green" />;
};

interface CategoryPageProps {
  params: {
    category: string;
  };
}

export default function CategoryPage({ params }: CategoryPageProps) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const categorySlug = unwrappedParams.category;

  const router = useRouter();
  const { user } = useAuth();
  const [category, setCategory] = useState<ForumCategory | null>(null);
  const [topics, setTopics] = useState<ForumTopic[]>([]);
  const [totalTopics, setTotalTopics] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [orderBy, setOrderBy] = useState<'last_post_at' | 'created_at' | 'view_count'>('last_post_at');
  const [orderDirection, setOrderDirection] = useState<'asc' | 'desc'>('desc');

  const topicsPerPage = 20;

  useEffect(() => {
    async function loadCategoryAndTopics() {
      setIsLoading(true);
      setError(null);

      try {
        // Load category
        const categoryData = await getForumCategoryBySlug(categorySlug);
        setCategory(categoryData);

        // Load topics
        const { data, count } = await getTopicsByCategory({
          categoryId: categoryData.id,
          limit: topicsPerPage,
          offset: (currentPage - 1) * topicsPerPage,
          orderBy,
          orderDirection
        });

        setTopics(data);
        setTotalTopics(count || 0);
      } catch (err: any) {
        setError(err.message || 'Failed to load category or topics');
      } finally {
        setIsLoading(false);
      }
    }

    loadCategoryAndTopics();
  }, [categorySlug, currentPage, orderBy, orderDirection]);

  const totalPages = Math.ceil(totalTopics / topicsPerPage);

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    if (value === 'newest') {
      setOrderBy('created_at');
      setOrderDirection('desc');
    } else if (value === 'oldest') {
      setOrderBy('created_at');
      setOrderDirection('asc');
    } else if (value === 'most_recent_activity') {
      setOrderBy('last_post_at');
      setOrderDirection('desc');
    } else if (value === 'most_viewed') {
      setOrderBy('view_count');
      setOrderDirection('desc');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-nature-green to-nature-green-dark py-10 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <Motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <Link
                href="/forums"
                className="inline-flex items-center text-white/90 hover:text-white mb-4 group transition-colors"
              >
                <HiArrowLongLeft className="mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Forums
              </Link>

              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="flex items-center">
                  <div className="mr-4 text-4xl bg-white/10 p-4 rounded-full">
                    {getCategoryIcon(categorySlug)}
                  </div>
                  <div>
                    <h1 className="text-2xl md:text-3xl font-bold">
                      {category?.name || 'Loading...'}
                    </h1>
                    {category?.description && (
                      <p className="text-white/80 mt-1 max-w-xl">{category.description}</p>
                    )}
                  </div>
                </div>

                {user && category && (
                  <Link
                    href={`/forums/create-topic?category=${category.id}`}
                    className="inline-flex items-center px-5 py-2.5 bg-white text-nature-green-dark rounded-full font-medium shadow-lg hover:bg-gray-100 transition-colors self-start md:self-center"
                  >
                    <HiOutlinePencilSquare className="mr-2" />
                    New Topic
                  </Link>
                )}
              </div>
            </Motion.div>

            {/* Category Stats */}
            {!isLoading && category && (
              <Motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <FaRegComments className="mx-auto text-xl mb-1" />
                  <p className="text-xs uppercase tracking-wider">Topics</p>
                  <p className="text-xl font-bold">{totalTopics}</p>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <FaRegEye className="mx-auto text-xl mb-1" />
                  <p className="text-xs uppercase tracking-wider">Views</p>
                  <p className="text-xl font-bold">{topics.reduce((sum, topic) => sum + topic.view_count, 0)}</p>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <FaRegClock className="mx-auto text-xl mb-1" />
                  <p className="text-xs uppercase tracking-wider">Latest Activity</p>
                  <p className="text-xl font-bold">
                    {topics.length > 0 ? formatDistanceToNow(new Date(topics[0].last_post_at), { addSuffix: false }) : 'N/A'}
                  </p>
                </div>
              </Motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          {error && (
            <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
              <p className="font-medium">Error</p>
              <p>{error}</p>
            </div>
          )}

          {isLoading ? (
            <div className="bg-white rounded-xl shadow-md p-8 text-center">
              <div className="animate-pulse flex flex-col items-center">
                <div className="h-12 w-12 bg-gray-200 rounded-full mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
              <p className="text-gray-500 mt-4">Loading topics...</p>
            </div>
          ) : topics.length === 0 ? (
            <div className="bg-white rounded-xl shadow-md p-8 text-center">
              <FaRegComments className="mx-auto text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 mb-4">No topics found in this category.</p>
              {user && (
                <Link
                  href={`/forums/create-topic?category=${category?.id}`}
                  className="inline-flex items-center px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
                >
                  <HiOutlinePencilSquare className="mr-2" />
                  Start a Discussion
                </Link>
              )}
            </div>
          ) : (
            <>
              {/* Topic Controls */}
              <div className="bg-white rounded-xl shadow-md p-4 mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="text-sm text-gray-500">
                  Showing {(currentPage - 1) * topicsPerPage + 1}-{Math.min(currentPage * topicsPerPage, totalTopics)} of {totalTopics} topics
                </div>

                <div className="flex items-center">
                  <label htmlFor="sort" className="text-sm text-gray-500 mr-2">
                    Sort by:
                  </label>
                  <select
                    id="sort"
                    value={
                      orderBy === 'last_post_at' && orderDirection === 'desc'
                        ? 'most_recent_activity'
                        : orderBy === 'created_at' && orderDirection === 'desc'
                        ? 'newest'
                        : orderBy === 'created_at' && orderDirection === 'asc'
                        ? 'oldest'
                        : 'most_viewed'
                    }
                    onChange={handleSortChange}
                    className="text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-nature-green focus:border-transparent"
                  >
                    <option value="most_recent_activity">Most Recent Activity</option>
                    <option value="newest">Newest</option>
                    <option value="oldest">Oldest</option>
                    <option value="most_viewed">Most Viewed</option>
                  </select>
                </div>
              </div>

              {/* Topics List */}
              <div className="space-y-4 mb-8">
                {topics.map((topic, index) => (
                  <Motion.div
                    key={topic.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <div className={`bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100 overflow-hidden ${topic.is_pinned ? 'border-l-4 border-l-yellow-400' : ''}`}>
                      <Link href={`/forums/${category?.slug}/${topic.slug}`} className="block p-5">
                        <div className="flex items-start">
                          <div className="hidden sm:block flex-shrink-0 mr-4">
                            {topic.author?.avatar_url ? (
                              <img
                                src={topic.author.avatar_url}
                                alt={topic.author.username}
                                className="w-12 h-12 rounded-full border-2 border-gray-100"
                              />
                            ) : (
                              <div className="w-12 h-12 rounded-full bg-nature-green/10 flex items-center justify-center border-2 border-gray-100">
                                <span className="text-nature-green font-medium text-lg">
                                  {topic.author?.username?.charAt(0).toUpperCase() || '?'}
                                </span>
                              </div>
                            )}
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              {topic.is_pinned && (
                                <span className="mr-2 text-yellow-600 bg-yellow-100 px-2 py-0.5 rounded-full text-xs font-medium" title="Pinned Topic">
                                  Pinned
                                </span>
                              )}
                              {topic.is_locked && (
                                <span className="mr-2 text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full text-xs font-medium" title="Locked Topic">
                                  Locked
                                </span>
                              )}
                              <h3 className="text-lg font-semibold text-gray-900 hover:text-nature-green transition-colors">
                                {topic.title}
                              </h3>
                            </div>

                            <div className="flex flex-wrap items-center text-sm text-gray-500 mb-2 gap-x-4 gap-y-1">
                              <span className="flex items-center">
                                <FaRegUser className="mr-1 text-gray-400" />
                                <Link
                                  href={`/profile/${topic.author?.username}`}
                                  className="text-nature-green hover:underline flex items-center"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  {topic.author?.full_name || topic.author?.username}
                                  {topic.author?.is_verified_expert && (
                                    <VerificationBadge isVerified={true} size="sm" className="ml-1" />
                                  )}
                                  {topic.author?.level && topic.author.level > 1 && (
                                    <UserLevelDisplay level={topic.author.level} size="sm" className="ml-1" />
                                  )}
                                </Link>
                              </span>
                              <span className="flex items-center">
                                <FaRegClock className="mr-1 text-gray-400" />
                                {formatDistanceToNow(new Date(topic.created_at), { addSuffix: true })}
                              </span>
                              <span className="flex items-center">
                                <FaRegComments className="mr-1 text-gray-400" />
                                <span className="font-medium">{topic.post_count}</span> replies
                              </span>
                              <span className="flex items-center">
                                <FaRegEye className="mr-1 text-gray-400" />
                                <span className="font-medium">{topic.view_count}</span> views
                              </span>
                            </div>

                            {topic.last_post && (
                              <div className="text-xs text-gray-500 mt-3 pt-3 border-t border-gray-100 flex items-center">
                                <FaRegBell className="mr-1 text-gray-400" />
                                Last reply by{' '}
                                <Link
                                  href={`/profile/${topic.last_post.author?.username}`}
                                  className="text-nature-green hover:underline mx-1"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  {topic.last_post.author?.username}
                                </Link>
                                {formatDistanceToNow(new Date(topic.last_post.created_at), { addSuffix: true })}
                              </div>
                            )}
                          </div>
                        </div>
                      </Link>
                    </div>
                  </Motion.div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <nav className="flex items-center space-x-2">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="p-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      aria-label="Previous page"
                    >
                      <HiChevronLeft className="h-5 w-5" />
                    </button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => handlePageChange(pageNum)}
                            className={`w-10 h-10 rounded-md flex items-center justify-center text-sm font-medium ${
                              currentPage === pageNum
                                ? 'bg-nature-green text-white'
                                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="p-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      aria-label="Next page"
                    >
                      <HiChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
