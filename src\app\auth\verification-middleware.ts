import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const url = new URL(request.url);
  
  // Check if this is a Supabase email verification URL
  if (url.pathname === '/auth/v1/verify' && url.searchParams.has('token') && url.searchParams.has('type')) {
    // Redirect to our custom handler
    const token = url.searchParams.get('token');
    const type = url.searchParams.get('type');
    const redirectTo = url.searchParams.get('redirect_to');
    
    // Build the new URL with all parameters
    const newUrl = new URL('/auth/v1/verify', url.origin);
    url.searchParams.forEach((value, key) => {
      newUrl.searchParams.append(key, value);
    });
    
    return NextResponse.redirect(newUrl);
  }
  
  return NextResponse.next();
}
