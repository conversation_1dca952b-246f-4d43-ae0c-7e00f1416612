'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type ContentType = 'article' | 'media' | 'category';

export default function AdminContentCreate() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState('');
  const [contentType, setContentType] = useState<ContentType>('article');

  // Article form state
  const [articleTitle, setArticleTitle] = useState('');
  const [articleContent, setArticleContent] = useState('');
  const [articleCategory, setArticleCategory] = useState('');
  const [articleTags, setArticleTags] = useState('');
  const [articleStatus, setArticleStatus] = useState('draft');

  // Media form state
  const [mediaTitle, setMediaTitle] = useState('');
  const [mediaDescription, setMediaDescription] = useState('');
  const [mediaFile, setMediaFile] = useState<File | null>(null);
  const [mediaCategory, setMediaCategory] = useState('');
  const [mediaAltText, setMediaAltText] = useState('');

  // Category form state
  const [categoryName, setCategoryName] = useState('');
  const [categoryDescription, setCategoryDescription] = useState('');
  const [categoryParent, setCategoryParent] = useState('');
  const [categorySlug, setCategorySlug] = useState('');

  // Available categories
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);

  // Fetch categories from the database
  const fetchCategories = async () => {
    try {
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .order('name');

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      if (data && data.length > 0) {
        setCategories(data);
      } else {
        // If no categories exist, create a default one
        console.log('No categories found, creating a default category');
        const { data: defaultCategory, error: createError } = await supabase
          .from('categories')
          .insert({
            name: 'General',
            description: 'Default category for all content',
            slug: 'general',
            icon: null
          })
          .select();

        if (createError) {
          console.error('Error creating default category:', createError);
        } else if (defaultCategory) {
          setCategories(defaultCategory);
        }
      }
    } catch (err) {
      console.error('Error in fetchCategories:', err);
    }
  };

  useEffect(() => {
    async function checkAuth() {
      if (loading) return;

      if (!user) {
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (error || !profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        router.push('/');
        return;
      }

      setIsAdmin(true);
      setIsLoading(false);

      // Fetch categories after authentication
      fetchCategories();
    }

    checkAuth();
  }, [user, loading, router]);

  const handleContentTypeChange = (type: ContentType) => {
    setContentType(type);
    setMessage('');
  };

  const handleArticleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!articleTitle || !articleContent || !articleCategory) {
      setMessage('Please fill in all required fields');
      return;
    }

    setIsSaving(true);
    setMessage('');

    try {
      const supabase = createClientComponentClient();

      // Process tags
      const tagsArray = articleTags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      // Generate a slug from the title
      const slug = articleTitle
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Create article
      const { data, error } = await supabase
        .from('articles')
        .insert({
          title: articleTitle,
          slug: `${slug}-${Date.now()}`, // Ensure uniqueness
          content: articleContent,
          excerpt: articleContent.substring(0, 150) + '...',
          category_id: articleCategory, // This should be a UUID from the database
          author_id: user?.id,
          status: articleStatus,
          published_at: articleStatus === 'published' ? new Date().toISOString() : null
        })
        .select();

      if (error) {
        throw error;
      }

      // Log activity (if activity_log table exists)
      try {
        await supabase
          .from('contributions')
          .insert({
            user_id: user?.id,
            content_type: 'article',
            content_id: data?.[0]?.id,
            contribution_type: 'create'
          });
      } catch (logError) {
        console.warn('Could not log contribution, but article was created:', logError);
        // Don't throw error here, as the article was successfully created
      }

      setMessage('Article created successfully!');

      // Reset form
      setArticleTitle('');
      setArticleContent('');
      setArticleCategory('');
      setArticleTags('');
      setArticleStatus('draft');
    } catch (err) {
      console.error('Error creating article:', err);
      // Display a more specific error message
      if (err.code === '23505') {
        setMessage('Error: An article with this title already exists. Please choose a different title.');
      } else if (err.code === '23503') {
        setMessage('Error: Invalid category selected. Please select a valid category.');
      } else if (err.message) {
        setMessage(`Error creating article: ${err.message}`);
      } else {
        setMessage('Error creating article. Please try again.');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleMediaSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!mediaTitle || !mediaFile || !mediaCategory) {
      setMessage('Please fill in all required fields');
      return;
    }

    setIsSaving(true);
    setMessage('');

    try {
      // In a real app, this would upload the file to storage
      // and create a media record in the database

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      setMessage('Media uploaded successfully!');

      // Reset form
      setMediaTitle('');
      setMediaDescription('');
      setMediaFile(null);
      setMediaCategory('');
      setMediaAltText('');
    } catch (err) {
      console.error('Error uploading media:', err);
      setMessage('Error uploading media. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!categoryName || !categorySlug) {
      setMessage('Please fill in all required fields');
      return;
    }

    setIsSaving(true);
    setMessage('');

    try {
      const supabase = createClientComponentClient();

      // Create category
      const { data, error } = await supabase
        .from('categories')
        .insert({
          name: categoryName,
          description: categoryDescription,
          parent_id: categoryParent || null,
          slug: categorySlug,
          icon: null // Add icon field with null value
        })
        .select();

      if (error) {
        throw error;
      }

      setMessage('Category created successfully!');

      // Add to categories list
      setCategories([...categories, { id: data[0].id, name: categoryName }]);

      // Reset form
      setCategoryName('');
      setCategoryDescription('');
      setCategoryParent('');
      setCategorySlug('');
    } catch (err) {
      console.error('Error creating category:', err);
      setMessage('Error creating category. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setMediaFile(e.target.files[0]);
    }
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleCategoryNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setCategoryName(name);
    setCategorySlug(generateSlug(name));
  };

  if (!isAdmin || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Create Content</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => handleContentTypeChange('article')}
            className={`px-4 py-2 font-medium ${
              contentType === 'article'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Article
          </button>
          <button
            onClick={() => handleContentTypeChange('media')}
            className={`px-4 py-2 font-medium ${
              contentType === 'media'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Media
          </button>
          <button
            onClick={() => handleContentTypeChange('category')}
            className={`px-4 py-2 font-medium ${
              contentType === 'category'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Category
          </button>
        </div>

        {message && (
          <div className={`mb-6 p-4 rounded-md ${message.includes('Error') ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {message}
          </div>
        )}

        {contentType === 'article' && (
          <form onSubmit={handleArticleSubmit}>
            <div className="mb-4">
              <label htmlFor="article_title" className="block text-sm font-medium text-gray-700 mb-1">
                Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="article_title"
                value={articleTitle}
                onChange={(e) => setArticleTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="article_content" className="block text-sm font-medium text-gray-700 mb-1">
                Content <span className="text-red-500">*</span>
              </label>
              <textarea
                id="article_content"
                value={articleContent}
                onChange={(e) => setArticleContent(e.target.value)}
                rows={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Markdown formatting is supported.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="article_category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category <span className="text-red-500">*</span>
                </label>
                <select
                  id="article_category"
                  value={articleCategory}
                  onChange={(e) => setArticleCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="article_tags" className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <input
                  type="text"
                  id="article_tags"
                  value={articleTags}
                  onChange={(e) => setArticleTags(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="Enter tags separated by commas"
                />
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <div className="flex space-x-4">
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="status_draft"
                    name="article_status"
                    value="draft"
                    checked={articleStatus === 'draft'}
                    onChange={() => setArticleStatus('draft')}
                    className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                  />
                  <label htmlFor="status_draft" className="ml-2 block text-sm text-gray-700">
                    Draft
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="status_pending"
                    name="article_status"
                    value="pending"
                    checked={articleStatus === 'pending'}
                    onChange={() => setArticleStatus('pending')}
                    className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                  />
                  <label htmlFor="status_pending" className="ml-2 block text-sm text-gray-700">
                    Pending Review
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="status_published"
                    name="article_status"
                    value="published"
                    checked={articleStatus === 'published'}
                    onChange={() => setArticleStatus('published')}
                    className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                  />
                  <label htmlFor="status_published" className="ml-2 block text-sm text-gray-700">
                    Published
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isSaving ? 'Creating...' : 'Create Article'}
              </button>
            </div>
          </form>
        )}

        {contentType === 'media' && (
          <form onSubmit={handleMediaSubmit}>
            <div className="mb-4">
              <label htmlFor="media_title" className="block text-sm font-medium text-gray-700 mb-1">
                Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="media_title"
                value={mediaTitle}
                onChange={(e) => setMediaTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="media_description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="media_description"
                value={mediaDescription}
                onChange={(e) => setMediaDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="media_file" className="block text-sm font-medium text-gray-700 mb-1">
                File <span className="text-red-500">*</span>
              </label>
              <input
                type="file"
                id="media_file"
                onChange={handleFileChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                accept="image/*,video/*,audio/*,.pdf"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Accepted file types: images, videos, audio, PDF. Max size: 10MB.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label htmlFor="media_category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category <span className="text-red-500">*</span>
                </label>
                <select
                  id="media_category"
                  value={mediaCategory}
                  onChange={(e) => setMediaCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="media_alt_text" className="block text-sm font-medium text-gray-700 mb-1">
                  Alt Text (for images)
                </label>
                <input
                  type="text"
                  id="media_alt_text"
                  value={mediaAltText}
                  onChange={(e) => setMediaAltText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="Describe the image for accessibility"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isSaving ? 'Uploading...' : 'Upload Media'}
              </button>
            </div>
          </form>
        )}

        {contentType === 'category' && (
          <form onSubmit={handleCategorySubmit}>
            <div className="mb-4">
              <label htmlFor="category_name" className="block text-sm font-medium text-gray-700 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="category_name"
                value={categoryName}
                onChange={handleCategoryNameChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="category_description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="category_description"
                value={categoryDescription}
                onChange={(e) => setCategoryDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label htmlFor="category_parent" className="block text-sm font-medium text-gray-700 mb-1">
                  Parent Category
                </label>
                <select
                  id="category_parent"
                  value={categoryParent}
                  onChange={(e) => setCategoryParent(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                >
                  <option value="">None (Top Level)</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="category_slug" className="block text-sm font-medium text-gray-700 mb-1">
                  Slug <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="category_slug"
                  value={categorySlug}
                  onChange={(e) => setCategorySlug(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Used in URLs. Auto-generated from name, but can be edited.
                </p>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isSaving ? 'Creating...' : 'Create Category'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
