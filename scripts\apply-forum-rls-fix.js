// <PERSON><PERSON><PERSON> to apply forum RLS fix
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Environment variables loaded:');
console.log('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '\u2713 Found' : '\u2717 Missing');
console.log('- SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '\u2713 Found' : '\u2717 Missing');
console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '\u2713 Found' : '\u2717 Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('\nMissing required Supabase credentials. Please check your .env.local file.');
  console.error('Make sure you have the following variables defined:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY (preferred) or NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250602_fix_forum_rls.sql');
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Applying forum RLS fix...');
    
    // Execute the SQL directly using the SQL API
    const response = await axios({
      method: 'POST',
      url: `${supabaseUrl}/rest/v1/sql`,
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Prefer': 'resolution=ignore-duplicates,return=minimal'
      },
      data: { query: sql }
    });
    
    console.log('\u2705 Forum RLS fix applied successfully!');
    
    // Verify the policy was updated
    console.log('Verifying RLS policies...');
    
    // Create a test topic to verify the policy works
    const { data: topic, error } = await supabase
      .from('forum_topics')
      .insert({
        title: 'Test Topic (RLS Fix Verification)',
        slug: 'test-topic-rls-fix-' + Date.now(),
        category_id: '00000000-0000-0000-0000-000000000000', // This will fail with a foreign key error, but that's expected
        author_id: '00000000-0000-0000-0000-000000000000'
      })
      .select();
    
    if (error && error.code === '23503') {
      console.log('\u2705 RLS policy is working correctly (expected foreign key error)');
    } else if (error && error.message.includes('new row violates row-level security')) {
      console.error('\u274C RLS policy is still not working correctly');
      console.error('Error:', error);
    } else if (error) {
      console.log('\u2705 Got expected error (not an RLS error):', error.message);
    } else {
      console.log('\u2705 Successfully created test topic');
    }
    
    console.log('Migration complete!');
  } catch (err) {
    console.error('Unexpected error:', err);
    process.exit(1);
  }
}

// Run the migration
applyMigration();
