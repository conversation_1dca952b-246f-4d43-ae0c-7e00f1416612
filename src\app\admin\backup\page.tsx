'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type BackupRecord = {
  id: string;
  created_at: string;
  size: string;
  status: 'completed' | 'failed' | 'in_progress';
  type: 'full' | 'partial';
  tables?: string[];
  error?: string;
};

export default function AdminBackup() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [backups, setBackups] = useState<BackupRecord[]>([]);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [backupType, setBackupType] = useState<'full' | 'partial'>('full');
  const [message, setMessage] = useState('');

  const availableTables = [
    'profiles',
    'articles',
    'categories',
    'comments',
    'media',
    'tags',
    'activity_log',
    'site_settings'
  ];

  useEffect(() => {
    async function checkAuth() {
      if (loading) return;
      
      if (!user) {
        router.push('/auth/signin');
        return;
      }
      
      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (error || !profile || profile.role !== 'admin') {
        router.push('/');
        return;
      }
      
      setIsAdmin(true);
      
      // Generate mock backup records
      const mockBackups: BackupRecord[] = [
        {
          id: '1',
          created_at: new Date(Date.now() - 2 * 3600000).toISOString(),
          size: '42.3 MB',
          status: 'completed',
          type: 'full'
        },
        {
          id: '2',
          created_at: new Date(Date.now() - 24 * 3600000).toISOString(),
          size: '41.8 MB',
          status: 'completed',
          type: 'full'
        },
        {
          id: '3',
          created_at: new Date(Date.now() - 2 * 86400000).toISOString(),
          size: '12.5 MB',
          status: 'completed',
          type: 'partial',
          tables: ['profiles', 'articles', 'comments']
        },
        {
          id: '4',
          created_at: new Date(Date.now() - 7 * 86400000).toISOString(),
          size: '39.2 MB',
          status: 'failed',
          type: 'full',
          error: 'Database connection timeout'
        }
      ];
      
      setBackups(mockBackups);
      setIsLoading(false);
    }
    
    checkAuth();
  }, [user, loading, router]);

  const handleTableSelection = (table: string) => {
    if (selectedTables.includes(table)) {
      setSelectedTables(selectedTables.filter(t => t !== table));
    } else {
      setSelectedTables([...selectedTables, table]);
    }
  };

  const handleBackupTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBackupType(e.target.value as 'full' | 'partial');
    if (e.target.value === 'full') {
      setSelectedTables([]);
    }
  };

  const handleCreateBackup = async () => {
    if (backupType === 'partial' && selectedTables.length === 0) {
      setMessage('Please select at least one table for partial backup');
      return;
    }
    
    setIsBackingUp(true);
    setMessage('');
    
    try {
      // Simulate backup process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create a new backup record
      const newBackup: BackupRecord = {
        id: (backups.length + 1).toString(),
        created_at: new Date().toISOString(),
        size: `${Math.floor(Math.random() * 10 + 40)}.${Math.floor(Math.random() * 10)} MB`,
        status: 'completed',
        type: backupType,
        tables: backupType === 'partial' ? selectedTables : undefined
      };
      
      setBackups([newBackup, ...backups]);
      setMessage('Backup created successfully!');
    } catch (err) {
      console.error('Error creating backup:', err);
      setMessage('Error creating backup. Please try again.');
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleRestoreBackup = (backupId: string) => {
    // In a real app, this would trigger a restore process
    alert(`Restore backup ${backupId} functionality would be implemented here`);
  };

  const handleDownloadBackup = (backupId: string) => {
    // In a real app, this would download the backup file
    alert(`Download backup ${backupId} functionality would be implemented here`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (!isAdmin || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Database Management</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-md p-6 md:col-span-2">
          <h2 className="text-xl font-semibold mb-4">Create Backup</h2>
          
          <div className="mb-4">
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="full_backup"
                  name="backup_type"
                  value="full"
                  checked={backupType === 'full'}
                  onChange={handleBackupTypeChange}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                />
                <label htmlFor="full_backup" className="ml-2 block text-sm text-gray-700">
                  Full Backup
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="partial_backup"
                  name="backup_type"
                  value="partial"
                  checked={backupType === 'partial'}
                  onChange={handleBackupTypeChange}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                />
                <label htmlFor="partial_backup" className="ml-2 block text-sm text-gray-700">
                  Partial Backup
                </label>
              </div>
            </div>
            
            {backupType === 'partial' && (
              <div className="mt-3 border border-gray-200 rounded-md p-3">
                <p className="text-sm text-gray-700 mb-2">Select tables to backup:</p>
                <div className="grid grid-cols-2 gap-2">
                  {availableTables.map(table => (
                    <div key={table} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`table_${table}`}
                        checked={selectedTables.includes(table)}
                        onChange={() => handleTableSelection(table)}
                        className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                      />
                      <label htmlFor={`table_${table}`} className="ml-2 block text-sm text-gray-700">
                        {table}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="flex items-center">
            <button
              onClick={handleCreateBackup}
              disabled={isBackingUp}
              className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isBackingUp ? 'Creating Backup...' : 'Create Backup'}
            </button>
            
            {message && (
              <p className={`ml-4 ${message.includes('Error') ? 'text-red-500' : 'text-green-500'}`}>
                {message}
              </p>
            )}
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Database Info</h2>
          
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Total Size</p>
              <p className="text-lg font-medium">42.3 MB</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Tables</p>
              <p className="text-lg font-medium">{availableTables.length}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Last Backup</p>
              <p className="text-lg font-medium">{formatDate(backups[0]?.created_at || new Date().toISOString())}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Status</p>
              <p className="text-lg font-medium text-green-500">Healthy</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Backup History</h2>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Size
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {backups.map((backup) => (
                <tr key={backup.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(backup.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {backup.type === 'full' ? 'Full' : 'Partial'}
                    {backup.tables && (
                      <details className="mt-1">
                        <summary className="text-xs text-gray-500 cursor-pointer">Tables</summary>
                        <ul className="mt-1 text-xs text-gray-500 list-disc list-inside">
                          {backup.tables.map(table => (
                            <li key={table}>{table}</li>
                          ))}
                        </ul>
                      </details>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {backup.size}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      backup.status === 'completed' ? 'bg-green-100 text-green-800' :
                      backup.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {backup.status.replace('_', ' ').toUpperCase()}
                    </span>
                    {backup.error && (
                      <details className="mt-1">
                        <summary className="text-xs text-red-500 cursor-pointer">Error</summary>
                        <p className="mt-1 text-xs text-red-500">{backup.error}</p>
                      </details>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {backup.status === 'completed' && (
                      <>
                        <button
                          onClick={() => handleDownloadBackup(backup.id)}
                          className="text-nature-green hover:text-green-700 mr-3"
                        >
                          Download
                        </button>
                        <button
                          onClick={() => handleRestoreBackup(backup.id)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          Restore
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
              
              {backups.length === 0 && (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    No backups found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
