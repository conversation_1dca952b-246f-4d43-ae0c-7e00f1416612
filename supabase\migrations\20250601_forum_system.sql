-- Forum system tables for NatureHeals.info

-- Forum categories table
CREATE TABLE IF NOT EXISTS public.forum_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    icon TEXT,
    display_order INTEGER DEFAULT 0,
    parent_id UUID REFERENCES public.forum_categories(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Forum topics table
CREATE TABLE IF NOT EXISTS public.forum_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    category_id UUID REFERENCES public.forum_categories(id) ON DELETE CASCADE,
    author_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    is_pinned BOOLEAN DEFAULT false,
    is_locked BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    last_post_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Forum posts table
CREATE TABLE IF NOT EXISTS public.forum_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    topic_id UUID REFERENCES public.forum_topics(id) ON DELETE CASCADE,
    author_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    content TEXT NOT NULL,
    is_solution BOOLEAN DEFAULT false,
    parent_id UUID REFERENCES public.forum_posts(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Forum post reactions table
CREATE TABLE IF NOT EXISTS public.forum_post_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES public.forum_posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    reaction_type TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Reported content table
CREATE TABLE IF NOT EXISTS public.reported_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type TEXT NOT NULL, -- 'forum_post', 'forum_topic', 'article', 'comment', etc.
    content_id UUID NOT NULL,
    reporter_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    reason TEXT NOT NULL,
    status TEXT DEFAULT 'pending', -- 'pending', 'resolved', 'dismissed'
    moderator_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    moderator_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create triggers for updated_at (only if they don't exist)
DO $$
BEGIN
    -- Check and create forum_categories trigger
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_forum_categories_updated_at') THEN
        CREATE TRIGGER update_forum_categories_updated_at
            BEFORE UPDATE ON public.forum_categories
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Check and create forum_topics trigger
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_forum_topics_updated_at') THEN
        CREATE TRIGGER update_forum_topics_updated_at
            BEFORE UPDATE ON public.forum_topics
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Check and create forum_posts trigger
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_forum_posts_updated_at') THEN
        CREATE TRIGGER update_forum_posts_updated_at
            BEFORE UPDATE ON public.forum_posts
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Check and create reported_content trigger
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_reported_content_updated_at') THEN
        CREATE TRIGGER update_reported_content_updated_at
            BEFORE UPDATE ON public.reported_content
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END
$$;

-- Create indexes for performance (only if they don't exist)
DO $$
BEGIN
    -- Check and create forum_topics indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_topics_category_id') THEN
        CREATE INDEX idx_forum_topics_category_id ON public.forum_topics(category_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_topics_author_id') THEN
        CREATE INDEX idx_forum_topics_author_id ON public.forum_topics(author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_topics_slug') THEN
        CREATE INDEX idx_forum_topics_slug ON public.forum_topics(slug);
    END IF;

    -- Check and create forum_posts indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_posts_topic_id') THEN
        CREATE INDEX idx_forum_posts_topic_id ON public.forum_posts(topic_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_posts_author_id') THEN
        CREATE INDEX idx_forum_posts_author_id ON public.forum_posts(author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_posts_parent_id') THEN
        CREATE INDEX idx_forum_posts_parent_id ON public.forum_posts(parent_id);
    END IF;

    -- Check and create forum_post_reactions indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_post_reactions_post_id') THEN
        CREATE INDEX idx_forum_post_reactions_post_id ON public.forum_post_reactions(post_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_forum_post_reactions_user_id') THEN
        CREATE INDEX idx_forum_post_reactions_user_id ON public.forum_post_reactions(user_id);
    END IF;

    -- Check and create reported_content indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_reported_content_content_id') THEN
        CREATE INDEX idx_reported_content_content_id ON public.reported_content(content_id);
    END IF;
END
$$;

-- Enable RLS on forum tables
ALTER TABLE public.forum_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.forum_topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.forum_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.forum_post_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reported_content ENABLE ROW LEVEL SECURITY;

-- RLS policies for forum_categories (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_categories' AND policyname = 'Forum categories are viewable by everyone') THEN
        CREATE POLICY "Forum categories are viewable by everyone"
            ON public.forum_categories FOR SELECT
            USING (true);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_categories' AND policyname = 'Only admins can insert forum categories') THEN
        CREATE POLICY "Only admins can insert forum categories"
            ON public.forum_categories FOR INSERT
            WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_categories' AND policyname = 'Only admins can update forum categories') THEN
        CREATE POLICY "Only admins can update forum categories"
            ON public.forum_categories FOR UPDATE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_categories' AND policyname = 'Only admins can delete forum categories') THEN
        CREATE POLICY "Only admins can delete forum categories"
            ON public.forum_categories FOR DELETE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );
    END IF;
END
$$;

-- RLS policies for forum_topics (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_topics' AND policyname = 'Forum topics are viewable by everyone') THEN
        CREATE POLICY "Forum topics are viewable by everyone"
            ON public.forum_topics FOR SELECT
            USING (true);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_topics' AND policyname = 'Authenticated users can create forum topics') THEN
        CREATE POLICY "Authenticated users can create forum topics"
            ON public.forum_topics FOR INSERT
            WITH CHECK (auth.uid() = author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_topics' AND policyname = 'Users can update their own forum topics') THEN
        CREATE POLICY "Users can update their own forum topics"
            ON public.forum_topics FOR UPDATE
            USING (auth.uid() = author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_topics' AND policyname = 'Moderators can update any forum topic') THEN
        CREATE POLICY "Moderators can update any forum topic"
            ON public.forum_topics FOR UPDATE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_topics' AND policyname = 'Users can delete their own forum topics') THEN
        CREATE POLICY "Users can delete their own forum topics"
            ON public.forum_topics FOR DELETE
            USING (auth.uid() = author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_topics' AND policyname = 'Moderators can delete any forum topic') THEN
        CREATE POLICY "Moderators can delete any forum topic"
            ON public.forum_topics FOR DELETE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
                )
            );
    END IF;
END
$$;

-- RLS policies for forum_posts (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_posts' AND policyname = 'Forum posts are viewable by everyone') THEN
        CREATE POLICY "Forum posts are viewable by everyone"
            ON public.forum_posts FOR SELECT
            USING (true);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_posts' AND policyname = 'Authenticated users can create forum posts') THEN
        CREATE POLICY "Authenticated users can create forum posts"
            ON public.forum_posts FOR INSERT
            WITH CHECK (auth.uid() = author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_posts' AND policyname = 'Users can update their own forum posts') THEN
        CREATE POLICY "Users can update their own forum posts"
            ON public.forum_posts FOR UPDATE
            USING (auth.uid() = author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_posts' AND policyname = 'Moderators can update any forum post') THEN
        CREATE POLICY "Moderators can update any forum post"
            ON public.forum_posts FOR UPDATE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_posts' AND policyname = 'Users can delete their own forum posts') THEN
        CREATE POLICY "Users can delete their own forum posts"
            ON public.forum_posts FOR DELETE
            USING (auth.uid() = author_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_posts' AND policyname = 'Moderators can delete any forum post') THEN
        CREATE POLICY "Moderators can delete any forum post"
            ON public.forum_posts FOR DELETE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
                )
            );
    END IF;
END
$$;

-- RLS policies for forum_post_reactions (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_post_reactions' AND policyname = 'Forum post reactions are viewable by everyone') THEN
        CREATE POLICY "Forum post reactions are viewable by everyone"
            ON public.forum_post_reactions FOR SELECT
            USING (true);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_post_reactions' AND policyname = 'Authenticated users can create forum post reactions') THEN
        CREATE POLICY "Authenticated users can create forum post reactions"
            ON public.forum_post_reactions FOR INSERT
            WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'forum_post_reactions' AND policyname = 'Users can delete their own forum post reactions') THEN
        CREATE POLICY "Users can delete their own forum post reactions"
            ON public.forum_post_reactions FOR DELETE
            USING (auth.uid() = user_id);
    END IF;
END
$$;

-- RLS policies for reported_content (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'reported_content' AND policyname = 'Reported content is viewable by moderators and admins') THEN
        CREATE POLICY "Reported content is viewable by moderators and admins"
            ON public.reported_content FOR SELECT
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
                )
            );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'reported_content' AND policyname = 'Authenticated users can report content') THEN
        CREATE POLICY "Authenticated users can report content"
            ON public.reported_content FOR INSERT
            WITH CHECK (auth.uid() = reporter_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'reported_content' AND policyname = 'Only moderators and admins can update reported content') THEN
        CREATE POLICY "Only moderators and admins can update reported content"
            ON public.reported_content FOR UPDATE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles
                    WHERE id = auth.uid() AND (role = 'moderator' OR role = 'admin')
                )
            );
    END IF;
END
$$;

-- Seed initial forum categories
INSERT INTO public.forum_categories (name, slug, description, display_order)
VALUES
('General Discussion', 'general-discussion', 'General discussions about holistic healing and natural remedies', 1),
('Herbal Remedies', 'herbal-remedies', 'Discussions about herbs and plant-based remedies', 2),
('Nutrition & Diet', 'nutrition-diet', 'Topics related to nutrition, diets, and food as medicine', 3),
('Mind & Body Practices', 'mind-body-practices', 'Yoga, meditation, tai chi, and other mind-body practices', 4),
('Traditional Medicine Systems', 'traditional-medicine', 'Traditional Chinese Medicine, Ayurveda, and other traditional systems', 5),
('Success Stories', 'success-stories', 'Share your healing success stories and experiences', 6),
('Research & Science', 'research-science', 'Scientific research and studies on natural healing methods', 7),
('Questions & Help', 'questions-help', 'Ask questions and get help from the community', 8)
ON CONFLICT (slug) DO NOTHING;
