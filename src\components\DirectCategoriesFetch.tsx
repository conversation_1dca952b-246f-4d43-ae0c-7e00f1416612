'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

type Category = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  icon?: string;
};

// Hardcoded sample categories to ensure we always have something to display
const SAMPLE_CATEGORIES: Category[] = [
  {
    id: '1',
    name: 'Herbal Remedies',
    slug: 'herbal-remedies',
    description: 'Natural plant-based medicines and treatments'
  },
  {
    id: '2',
    name: 'Nutritional Healing',
    slug: 'nutritional-healing',
    description: 'Healing through food and dietary approaches'
  },
  {
    id: '3',
    name: 'Traditional Medicine',
    slug: 'traditional-medicine',
    description: 'Ancient healing practices from around the world'
  }
];

export default function DirectCategoriesFetch() {
  // Start with sample categories to ensure immediate display
  const [categories, setCategories] = useState<Category[]>(SAMPLE_CATEGORIES);
  const [loading, setLoading] = useState(false); // Start with false to avoid blocking UI
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    let isMounted = true;

    async function fetchCategories() {
      if (!isMounted) return;

      try {
        // Simple fetch with minimal overhead
        const response = await fetch('/api/categories');

        if (!response.ok) {
          console.log('API call failed, using sample categories');
          return;
        }

        const data = await response.json();

        if (isMounted && Array.isArray(data) && data.length > 0) {
          setCategories(data);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        // Keep using sample categories
      }
    }

    // Fetch without blocking the UI
    fetchCategories();

    // Clean up function
    return () => {
      isMounted = false;
    };
  }, []);

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg shadow-md text-red-700 mb-4">
        <p className="font-bold text-lg mb-2">Error loading categories:</p>
        <p className="mb-4">{error}</p>

        {debugInfo && (
          <div className="mt-4 p-4 bg-gray-100 rounded text-sm text-gray-800 overflow-auto">
            <p className="font-bold mb-2">Debug Information:</p>
            <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
          </div>
        )}

        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="bg-yellow-50 p-4 rounded-md text-yellow-700 mb-4">
        <p>No categories found. Please check back later or contact the administrator.</p>

        {debugInfo && (
          <div className="mt-4 p-4 bg-gray-100 rounded text-sm text-gray-800 overflow-auto">
            <p className="font-bold mb-2">Debug Information:</p>
            <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
          </div>
        )}
      </div>
    );
  }

  // Default icon mapping for categories
  const getIconForCategory = (slug: string, icon?: string) => {
    // If the category has an icon specified, use it
    if (icon) {
      return icon;
    }

    // Otherwise, use a default icon based on the slug
    const iconMap: Record<string, string> = {
      'herbal-remedies': 'leaf',
      'mind-body-practices': 'sparkles',
      'nutritional-healing': 'cake',
      'medicinal-herbs': 'beaker',
      'aromatherapy': 'fire',
      'traditional-medicine': 'academic-cap',
      'ayurveda': 'sun',
      'chinese-medicine': 'moon',
      'homeopathy': 'droplet',
      'naturopathy': 'globe',
      'foraging': 'map',
      'gardening': 'home',
      'herbalism': 'collection',
      'nutrition': 'shopping-cart',
      'essential-oils': 'color-swatch',
      'holistic-health': 'heart',
      'sustainable-living': 'lightning-bolt'
    };

    return iconMap[slug] || 'bookmark';
  };

  // Render the appropriate icon
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'leaf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      case 'sparkles':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
          </svg>
        );
      case 'heart':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
        );
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {categories.slice(0, 3).map((category) => (
          <div key={category.id} className="p-6 bg-white rounded-lg shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 hover:border-green-200 group">
            <div className="bg-[#2e7d32]/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-4 text-[#2e7d32] group-hover:bg-[#2e7d32]/20 transition-all duration-300 shadow-sm">
              {renderIcon(getIconForCategory(category.slug, category.icon))}
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">{category.name}</h3>
            <div className="h-px w-16 bg-green-200 mb-4"></div>
            <p className="text-gray-700 mb-4">{category.description}</p>
            <a
              href={`/categories/${category.slug}`}
              className="text-[#2e7d32] font-medium hover:underline flex items-center transition-all duration-200 transform hover:translate-x-1"
            >
              Explore
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 ml-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </a>
          </div>
        ))}
      </div>

      {debugInfo && process.env.NODE_ENV === 'development' && (
        <div className="mt-8 p-4 bg-gray-100 rounded text-sm text-gray-800 overflow-auto">
          <p className="font-bold mb-2">Debug Information:</p>
          <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
