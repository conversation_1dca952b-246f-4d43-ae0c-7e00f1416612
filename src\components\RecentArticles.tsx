'use client';


import Image from "next/image";
import AnimatedCard from "@/components/AnimatedCard";
import { useEffect, useState } from "react";
import { formatDate } from "@/lib/utils";
import { getRecentArticles } from "@/app/actions";

// Article type definition
type Article = {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  created_at: string;
  author_id: string;
  profiles: {
    username: string;
    full_name: string | null;
    avatar_url: string | null;
    avatar_type: string | null;
    preset_avatar: string | null;
  };
  categories: {
    name: string;
    slug: string;
  } | null;
  media_url: string | null;
};

const RecentArticles = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchArticles() {
      try {
        const { data, error } = await getRecentArticles(3);
        if (error) {
          setError(error.message || 'Failed to load articles');
          return;
        }
        if (data) {
          setArticles(data);
        }
      } catch (err: any) {
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    }

    fetchArticles();
  }, []);

  return (
    <section className="py-8 sm:py-12 md:py-16 bg-gradient-to-br from-white to-green-50 rounded-xl border border-green-100 shadow-md my-8 sm:my-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 sm:mb-12">
          <div className="inline-block mb-4">
            <div className="h-1 w-24 bg-green-300 mb-1 mx-auto"></div>
            <div className="h-1 w-16 bg-green-500 mx-auto"></div>
          </div>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-gray-900">Recent Contributions</h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto">
            Explore the latest knowledge shared by our community members
          </p>
        </div>

        {loading ? (
          <div className="text-center py-16">
            <div className="bg-white rounded-xl shadow-md p-8 max-w-md mx-auto border border-gray-100">
              <div className="flex flex-col items-center">
                <div className="relative w-16 h-16 mb-4">
                  <div className="absolute top-0 left-0 w-full h-full rounded-full border-4 border-green-100"></div>
                  <div className="absolute top-0 left-0 w-full h-full rounded-full border-t-4 border-[#2e7d32] animate-spin"></div>
                  <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center text-[#2e7d32]">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2 text-gray-900">Loading Content</h3>
                <p className="text-gray-600">Fetching the latest contributions...</p>
              </div>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-white rounded-xl shadow-md p-8 max-w-2xl mx-auto border border-red-100 mb-8">
              <div className="bg-red-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">Error Loading Content</h3>
              <p className="text-red-500 mb-4">{error}</p>
              <p className="text-gray-600 mb-2">Don't worry, we've prepared some sample content for you below:</p>
            </div>
            <div className="mt-4">
              {/* Fallback content when there's an error */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <AnimatedCard className="bg-white rounded-xl shadow-lg overflow-hidden group border border-gray-100">
                  <div className="relative">
                    <div className="h-48 bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
                      <span className="text-green-800 font-medium">Turmeric Root</span>
                    </div>
                    <div className="absolute top-3 right-3 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-medium text-green-800 shadow-sm">
                      Herbal Remedies
                    </div>
                  </div>
                  <div className="p-6 card-content">
                    <div className="flex items-center mb-3">
                      <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        Added 2 days ago
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-gray-900">My Grandmother's Turmeric Tea Recipe</h3>
                    <div className="h-px w-16 bg-green-200 mb-3"></div>
                    <p className="text-gray-700 mb-4">This simple turmeric tea helped my family through cold season for generations. It combines turmeric, ginger, honey and black pepper for maximum benefits.</p>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        GardenHealer
                      </span>
                      <a
                        href="/wiki/grandmothers-turmeric-tea"
                        className="text-[#2e7d32] font-medium hover:underline flex items-center transition-all duration-200 transform hover:translate-x-1"
                      >
                        Read More
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </a>
                    </div>
                  </div>
                </AnimatedCard>
              </div>
            </div>
          </div>
        ) : articles.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-white rounded-xl shadow-md p-8 max-w-2xl mx-auto border border-gray-100">
              <div className="bg-green-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-[#2e7d32]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2 text-gray-900">No articles found</h3>
              <p className="text-gray-600 mb-6">Be the first to share your knowledge and contribute to our growing community!</p>
              <a
                href="/contribute"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#2e7d32] to-[#388e3c] text-white rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-300 hover:translate-y-[-2px] group"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Create an Article
              </a>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {articles.map((article) => (
              <AnimatedCard key={article.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100 hover:border-green-200">
                <div className="relative">
                  {article.media_url ? (
                    <Image
                      src={article.media_url}
                      alt={article.title}
                      height={192}
                      width={512}
                      className="object-cover h-48 w-full group-hover:scale-105 transition-transform duration-500"
                    />
                  ) : (
                    <div className="h-48 bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center group-hover:from-green-100 group-hover:to-green-200 transition-all duration-300">
                      <span className="text-green-800 font-medium">{article.categories?.name || 'Article'}</span>
                    </div>
                  )}
                  {article.categories?.name && (
                    <div className="absolute top-3 right-3 bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-medium text-green-800 shadow-sm">
                      {article.categories.name}
                    </div>
                  )}
                </div>
                <div className="p-4 sm:p-6 card-content">
                  <div className="flex items-center mb-3">
                    <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      Added {formatDate(article.created_at)}
                    </div>
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 group-hover:text-[#2e7d32] transition-colors duration-300">{article.title}</h3>
                  <div className="h-px w-16 bg-green-200 mb-3"></div>
                  <p className="text-gray-700 mb-4 line-clamp-2">{article.excerpt}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      {article.profiles.username}
                    </span>
                    <a
                      href={`/wiki/${article.slug}`}
                      className="text-[#2e7d32] font-medium hover:underline flex items-center transition-all duration-200 transform hover:translate-x-1"
                    >
                      Read More
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </a>
                  </div>
                </div>
              </AnimatedCard>
            ))}
          </div>
        )}

        <div className="text-center mt-12">
          <a
            href="/wiki"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#2e7d32] to-[#388e3c] text-white rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-2px] group"
          >
            <span className="mr-2">View All Articles</span>
            <span className="bg-white/20 rounded-full p-1 group-hover:bg-white/30 transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </span>
          </a>
        </div>
      </div>
    </section>
  );
};

export default RecentArticles;