import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { cspMiddleware } from './middleware/csp';

export async function middleware(request: NextRequest) {
  const url = new URL(request.url);

  // Check if this is a redirect from Supabase email verification
  // This would be our own domain with error parameters from Supabase
  if (url.pathname === '/' &&
      (url.searchParams.has('error') || url.hash.includes('error='))) {

    // Extract error information
    const errorParam = url.searchParams.get('error');
    const errorCodeParam = url.searchParams.get('error_code');
    const errorDescParam = url.searchParams.get('error_description');

    // Also check hash fragment for errors (Supabase sometimes puts errors in the hash)
    const hashParams = new URLSearchParams(url.hash.replace('#', ''));
    const errorHash = hashParams.get('error');
    const errorCodeHash = hashParams.get('error_code');
    const errorDescHash = hashParams.get('error_description');

    // Combine error information
    const error = errorParam || errorHash || '';
    const errorCode = errorCodeParam || errorCodeHash || '';
    const errorDesc = errorDescParam || errorDescHash || '';

    if (error || errorCode || errorDesc) {
      // Redirect to our error page with the error information
      const errorUrl = new URL('/auth/verify/error', url.origin);
      if (error) errorUrl.searchParams.append('error', error);
      if (errorCode) errorUrl.searchParams.append('code', errorCode);
      if (errorDesc) errorUrl.searchParams.append('description', errorDesc);

      return NextResponse.redirect(errorUrl);
    }
  }

  // Check if this is a verification success redirect
  if (url.pathname === '/' && url.searchParams.has('verification') && url.searchParams.get('verification') === 'success') {
    return NextResponse.redirect(new URL('/auth/verify/success', url.origin));
  }

  // Skip CSP for API routes
  const isApiRoute = request.nextUrl.pathname.startsWith('/api/');

  // Apply CSP middleware for non-API routes
  let res = isApiRoute ? NextResponse.next() : cspMiddleware(request);

  // Create a Supabase client configured for the middleware
  const supabase = createMiddlewareClient({ req: request, res });

  // Refresh the session if it exists - this will extend the session duration
  // and prevent users from being logged out after a short period
  await supabase.auth.getSession();

  // Add cache control headers for dynamic routes
  if (!isApiRoute && !request.nextUrl.pathname.includes('.')) {
    // Add cache control headers for HTML pages
    res.headers.set('Vary', 'Cookie'); // Important for caching based on auth
    res.headers.set(
      'Cache-Control',
      'private, no-cache, no-store, max-age=0, must-revalidate'
    );
  }

  // Add special no-cache headers for critical paths
  if (
    request.nextUrl.pathname === '/' ||
    request.nextUrl.pathname.startsWith('/categories') ||
    request.nextUrl.pathname.startsWith('/auth')
  ) {
    res.headers.set(
      'Cache-Control',
      'no-store, no-cache, must-revalidate, proxy-revalidate'
    );
    res.headers.set('Pragma', 'no-cache');
    res.headers.set('Expires', '0');
  }

  return res;
}

export const config = {
  matcher: [
    // Match all routes that need authentication session management
    '/categories/:path*',
    '/forums/:path*',
    '/wiki/:path*',
    '/admin/:path*',
    '/profile/:path*',
    '/auth/:path*',
    '/settings/:path*',
    // Match all other routes except static files
    '/((?!_next/static|_next/image|favicon.ico|api/).)*',
  ],
};
