# Apply the SQL migration to fix categories
$env:PGPASSWORD = $env:SUPABASE_DB_PASSWORD
$supabaseUrl = $env:NEXT_PUBLIC_SUPABASE_URL

if (-not $supabaseUrl) {
    Write-Host "Error: NEXT_PUBLIC_SUPABASE_URL environment variable is not set." -ForegroundColor Red
    exit 1
}

# Extract project ID from the URL
$projectId = $supabaseUrl -replace "https://", "" -replace "\.supabase\.co.*", ""

Write-Host "Applying migration for project: $projectId" -ForegroundColor Green

# Apply the migration
Get-Content "supabase\migrations\20240617_get_all_categories.sql" | psql -h db.$projectId.supabase.co -U postgres -d postgres

Write-Host "Migration applied successfully!" -ForegroundColor Green
