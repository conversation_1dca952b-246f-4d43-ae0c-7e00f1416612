'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function TestModerationPage() {
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  async function testDirectQuery() {
    setIsLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const supabase = createClientComponentClient();
      
      // Test direct query without .single()
      const { data, error } = await supabase
        .from('moderation_actions')
        .select('*')
        .limit(5);
      
      if (error) {
        setError(`Direct query error: ${error.message}`);
        return;
      }
      
      setResult({ directQuery: data });
    } catch (err: any) {
      setError(`Exception in direct query: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }

  async function testInsertQuery() {
    setIsLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const supabase = createClientComponentClient();
      
      // Get current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData?.user) {
        setError('No authenticated user found');
        return;
      }
      
      // Test insert without .single()
      const { data, error } = await supabase
        .from('moderation_actions')
        .insert({
          action: 'test_action',
          action_type: 'test_action',
          moderator_id: userData.user.id,
          content_id: 'test-content-id',
          content_type: 'test',
          reason: 'Testing moderation actions',
          created_at: new Date().toISOString()
        })
        .select();
      
      if (error) {
        setError(`Insert error: ${error.message}`);
        return;
      }
      
      setResult({ insertQuery: data });
    } catch (err: any) {
      setError(`Exception in insert query: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Test Moderation Actions</h1>
      
      <div className="space-y-4">
        <button 
          onClick={testDirectQuery}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-2"
          disabled={isLoading}
        >
          Test Direct Query
        </button>
        
        <button 
          onClick={testInsertQuery}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          disabled={isLoading}
        >
          Test Insert Query
        </button>
      </div>
      
      {isLoading && (
        <div className="mt-4 p-4 bg-gray-100 rounded">
          Loading...
        </div>
      )}
      
      {error && (
        <div className="mt-4 p-4 bg-red-100 text-red-700 rounded">
          <h2 className="font-bold">Error:</h2>
          <pre className="whitespace-pre-wrap">{error}</pre>
        </div>
      )}
      
      {result && (
        <div className="mt-4 p-4 bg-green-100 text-green-700 rounded">
          <h2 className="font-bold">Result:</h2>
          <pre className="whitespace-pre-wrap">{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
