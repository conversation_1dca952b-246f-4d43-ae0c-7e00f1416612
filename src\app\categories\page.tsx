import Link from 'next/link';
import ClientSideCategories from '@/components/ClientSideCategories';

// Disable caching for this page
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default function CategoriesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb navigation */}
      <nav className="flex mb-6 text-sm">
        <Link href="/" className="text-gray-500 hover:text-gray-700">
          Home
        </Link>
        <span className="mx-2 text-gray-500">/</span>
        <span className="text-gray-900 font-medium">Categories</span>
      </nav>

      <h1 className="text-3xl font-bold mb-4 text-gray-900">Browse Categories</h1>

      <p className="text-gray-700 mb-8 max-w-3xl">
        Explore our collection of holistic healing categories. Each category contains articles, remedies, and wisdom about natural approaches to health and wellness. Click on a category to discover related content.
      </p>

      {/* Use client-side component to load categories */}
      <ClientSideCategories />
    </div>
  );
}
