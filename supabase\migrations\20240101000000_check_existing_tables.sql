-- Script to check existing table structures

-- Check if reported_content table exists and its structure
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'reported_content'
) AS reported_content_exists;

-- Check reported_content columns if it exists
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'reported_content';

-- Check if moderation_actions table exists and its structure
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'moderation_actions'
) AS moderation_actions_exists;

-- Check moderation_actions columns if it exists
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'moderation_actions';

-- Check if user_bans table exists
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'user_bans'
) AS user_bans_exists;

-- Check if auto_moderation_logs table exists
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'auto_moderation_logs'
) AS auto_moderation_logs_exists;

-- Check if user_notifications table exists
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'user_notifications'
) AS user_notifications_exists;
