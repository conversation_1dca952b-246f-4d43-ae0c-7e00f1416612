/**
 * This script helps test the moderation features by making API calls to the Supabase backend.
 * It can be used to verify that the database tables and RLS policies are working correctly.
 * 
 * Usage:
 * 1. Update the SUPABASE_URL and SUPABASE_ANON_KEY constants with your project values
 * 2. Run the script with Node.js: node test-moderation.js
 */

const { createClient } = require('@supabase/supabase-js');

// Update these with your Supabase project URL and anon key
const SUPABASE_URL = 'https://your-project-url.supabase.co';
const SUPABASE_ANON_KEY = 'your-anon-key';

// Create a Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Test user credentials - update these with valid credentials from your project
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'admin-password';
const USER_EMAIL = '<EMAIL>';
const USER_PASSWORD = 'user-password';

// Test functions
async function testAsAdmin() {
  console.log('Testing as admin user...');
  
  // Sign in as admin
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: ADMIN_EMAIL,
    password: ADMIN_PASSWORD,
  });
  
  if (authError) {
    console.error('Error signing in as admin:', authError);
    return;
  }
  
  console.log('Signed in as admin:', authData.user.email);
  
  // Test fetching banned users
  const { data: bannedUsers, error: bannedError } = await supabase
    .from('user_bans')
    .select('*, user:user_id(username, email), moderator:moderator_id(username)')
    .is('revoked_at', null);
  
  if (bannedError) {
    console.error('Error fetching banned users:', bannedError);
  } else {
    console.log(`Found ${bannedUsers.length} active bans:`);
    bannedUsers.forEach(ban => {
      console.log(`- ${ban.user?.username || ban.user_id} banned by ${ban.moderator?.username || ban.moderator_id}`);
      console.log(`  Reason: ${ban.reason}`);
      console.log(`  Type: ${ban.ban_type}`);
      console.log(`  Expires: ${ban.expires_at || 'Never'}`);
      console.log('');
    });
  }
  
  // Test fetching reported content
  const { data: reports, error: reportsError } = await supabase
    .from('reported_content')
    .select('*')
    .eq('status', 'pending');
  
  if (reportsError) {
    console.error('Error fetching reported content:', reportsError);
  } else {
    console.log(`Found ${reports.length} pending reports`);
  }
  
  // Test fetching moderation actions
  const { data: actions, error: actionsError } = await supabase
    .from('moderation_actions')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(5);
  
  if (actionsError) {
    console.error('Error fetching moderation actions:', actionsError);
  } else {
    console.log(`Found ${actions.length} recent moderation actions`);
    actions.forEach(action => {
      console.log(`- ${action.action_type} on ${action.content_type} by moderator ${action.moderator_id}`);
      console.log(`  Reason: ${action.reason}`);
      console.log(`  Date: ${action.created_at}`);
      console.log('');
    });
  }
}

async function testAsUser() {
  console.log('\nTesting as regular user...');
  
  // Sign in as regular user
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: USER_EMAIL,
    password: USER_PASSWORD,
  });
  
  if (authError) {
    console.error('Error signing in as user:', authError);
    return;
  }
  
  console.log('Signed in as user:', authData.user.email);
  
  // Test fetching banned users (should only see own bans if any)
  const { data: bannedUsers, error: bannedError } = await supabase
    .from('user_bans')
    .select('*');
  
  if (bannedError) {
    console.error('Error fetching banned users:', bannedError);
  } else {
    console.log(`User can see ${bannedUsers.length} bans (should only be their own)`);
  }
  
  // Test fetching moderation actions (should be denied)
  const { data: actions, error: actionsError } = await supabase
    .from('moderation_actions')
    .select('*')
    .limit(5);
  
  if (actionsError) {
    console.log('Expected error fetching moderation actions (access denied):', actionsError.message);
  } else {
    console.warn('Warning: User can access moderation actions, RLS policy may not be working correctly');
    console.log(`Found ${actions.length} moderation actions`);
  }
  
  // Test creating a report (should be allowed)
  const testReportData = {
    content_type: 'post',
    content_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
    reporter_id: authData.user.id,
    reason: 'Test report from script',
  };
  
  const { data: reportData, error: reportError } = await supabase
    .from('reported_content')
    .insert(testReportData)
    .select()
    .single();
  
  if (reportError) {
    console.error('Error creating test report:', reportError);
  } else {
    console.log('Successfully created test report:', reportData.id);
    
    // Clean up the test report
    await supabase
      .from('reported_content')
      .delete()
      .eq('id', reportData.id);
    
    console.log('Cleaned up test report');
  }
  
  // Test fetching notifications (should see own notifications)
  const { data: notifications, error: notificationsError } = await supabase
    .from('user_notifications')
    .select('*');
  
  if (notificationsError) {
    console.error('Error fetching notifications:', notificationsError);
  } else {
    console.log(`User can see ${notifications.length} notifications`);
  }
}

// Run the tests
async function runTests() {
  try {
    await testAsAdmin();
    await testAsUser();
    console.log('\nTests completed!');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

runTests();
