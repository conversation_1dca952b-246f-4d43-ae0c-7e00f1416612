/* Article content styling */
.article-content h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.article-content h2 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.article-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

.article-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.75rem;
}

.article-content p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

.article-content ul, .article-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.article-content ul {
  list-style-type: disc;
}

.article-content ol {
  list-style-type: decimal;
}

.article-content li {
  margin-bottom: 0.5rem;
}
.article-content a {
  color: #2e7d32;
  text-decoration: underline;
}

.article-content a:hover {
  color: #1b5e20;
}

.article-content blockquote {
  border-left: 4px solid #2e7d32;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: #555;
}

.article-content code {
  background-color: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

.article-content pre {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 0.25rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

.article-content img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 0.25rem;
}

.article-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.article-content th, .article-content td {
  border: 1px solid #ddd;
  padding: 0.5rem;
}

.article-content th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.article-content tr:nth-child(even) {
  background-color: #f9f9f9;
}
