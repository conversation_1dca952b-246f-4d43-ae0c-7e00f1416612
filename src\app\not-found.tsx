import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
        <h1 className="text-4xl font-bold mb-6 text-nature-green">404 - Page Not Found</h1>
        <p className="text-lg text-gray-800 mb-8">
          The page you are looking for doesn't exist or has been moved.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link
            href="/"
            className="px-6 py-3 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Return to Home
          </Link>
          <Link
            href="/wiki"
            className="px-6 py-3 border border-nature-green text-nature-green rounded-md hover:bg-green-50 transition-colors"
          >
            Browse Wiki
          </Link>
        </div>
      </div>
    </div>
  );
}
