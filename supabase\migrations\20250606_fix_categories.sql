-- Fix Categories Table and RLS
-- This script sets up the categories table with proper RLS policies and admin functions

-- 1. First, make sure the categories table exists with the right structure
DO $$
BEGIN
  -- Check if the categories table exists
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'categories') THEN
    -- Create the table if it doesn't exist
    CREATE TABLE public.categories (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name TEXT NOT NULL,
      slug TEXT NOT NULL UNIQUE,
      description TEXT,
      parent_id UUID REFERENCES public.categories(id),
      icon TEXT,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    RAISE NOTICE 'Created categories table';
  ELSE
    -- Check if the icon column exists
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'categories' AND column_name = 'icon'
    ) THEN
      -- Add the icon column if it doesn't exist
      ALTER TABLE public.categories ADD COLUMN icon TEXT;
      RAISE NOTICE 'Added icon column to categories table';
    END IF;

    -- Check if the parent_id column exists
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'categories' AND column_name = 'parent_id'
    ) THEN
      -- Add the parent_id column if it doesn't exist
      ALTER TABLE public.categories ADD COLUMN parent_id UUID REFERENCES public.categories(id);
      RAISE NOTICE 'Added parent_id column to categories table';
    END IF;

    -- Check if the updated_at column exists
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'categories' AND column_name = 'updated_at'
    ) THEN
      -- Add the updated_at column if it doesn't exist
      ALTER TABLE public.categories ADD COLUMN updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();
      RAISE NOTICE 'Added updated_at column to categories table';
    END IF;

    RAISE NOTICE 'Categories table already exists';
  END IF;
END
$$;

-- 2. Disable RLS temporarily to ensure we can modify the table
DO $$
DECLARE
  rls_enabled BOOLEAN;
BEGIN
  SELECT rowsecurity INTO rls_enabled
  FROM pg_tables
  WHERE schemaname = 'public' AND tablename = 'categories';

  IF rls_enabled THEN
    ALTER TABLE public.categories DISABLE ROW LEVEL SECURITY;
    RAISE NOTICE 'Disabled RLS on categories table temporarily';
  ELSE
    RAISE NOTICE 'RLS already disabled on categories table';
  END IF;
END
$$;

-- 3. Create admin function to manage categories (bypassing RLS)
CREATE OR REPLACE FUNCTION public.admin_manage_category(
  p_action TEXT, -- 'create', 'update', 'delete'
  p_id UUID DEFAULT NULL,
  p_name TEXT DEFAULT NULL,
  p_description TEXT DEFAULT NULL,
  p_parent_id UUID DEFAULT NULL,
  p_icon TEXT DEFAULT NULL
) RETURNS JSONB
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_user_role TEXT;
  v_slug TEXT;
  v_result JSONB;
  v_category_record RECORD;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();

  -- Check if the user exists and is an admin
  SELECT role INTO v_user_role
  FROM public.profiles
  WHERE id = v_user_id;

  IF v_user_role IS NULL OR v_user_role != 'admin' THEN
    RAISE EXCEPTION 'Unauthorized: Only admins can manage categories';
  END IF;

  -- Generate slug from name if provided
  IF p_name IS NOT NULL THEN
    v_slug := LOWER(REGEXP_REPLACE(REGEXP_REPLACE(p_name, '[^\w\s]', '', 'g'), '\s+', '-', 'g'));
  END IF;

  -- Perform the requested action
  IF p_action = 'create' THEN
    -- Check if name is provided
    IF p_name IS NULL THEN
      RAISE EXCEPTION 'Category name is required';
    END IF;

    -- Check if slug already exists
    IF EXISTS (SELECT 1 FROM public.categories WHERE slug = v_slug) THEN
      RAISE EXCEPTION 'A category with this name already exists';
    END IF;

    -- Create the category
    INSERT INTO public.categories (name, slug, description, parent_id, icon, created_at, updated_at)
    VALUES (p_name, v_slug, p_description, p_parent_id, p_icon, NOW(), NOW())
    RETURNING * INTO v_category_record;

    -- Convert to JSON
    SELECT jsonb_build_object(
      'id', v_category_record.id,
      'name', v_category_record.name,
      'slug', v_category_record.slug,
      'description', v_category_record.description,
      'parent_id', v_category_record.parent_id,
      'icon', v_category_record.icon,
      'created_at', v_category_record.created_at,
      'updated_at', v_category_record.updated_at
    ) INTO v_result;

  ELSIF p_action = 'update' THEN
    -- Check if ID is provided
    IF p_id IS NULL THEN
      RAISE EXCEPTION 'Category ID is required for update';
    END IF;

    -- Check if category exists
    IF NOT EXISTS (SELECT 1 FROM public.categories WHERE id = p_id) THEN
      RAISE EXCEPTION 'Category not found';
    END IF;

    -- Check if slug already exists for a different category
    IF p_name IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.categories
      WHERE slug = v_slug AND id != p_id
    ) THEN
      RAISE EXCEPTION 'A category with this name already exists';
    END IF;

    -- Update the category
    UPDATE public.categories
    SET
      name = COALESCE(p_name, name),
      slug = CASE WHEN p_name IS NOT NULL THEN v_slug ELSE slug END,
      description = COALESCE(p_description, description),
      parent_id = CASE
                    WHEN p_parent_id::TEXT = 'null' THEN NULL
                    WHEN p_parent_id IS NOT NULL THEN p_parent_id
                    ELSE parent_id
                  END,
      icon = CASE
               WHEN p_icon::TEXT = 'null' THEN NULL
               WHEN p_icon IS NOT NULL THEN p_icon
               ELSE icon
             END,
      updated_at = NOW()
    WHERE id = p_id
    RETURNING * INTO v_category_record;

    -- Convert to JSON
    SELECT jsonb_build_object(
      'id', v_category_record.id,
      'name', v_category_record.name,
      'slug', v_category_record.slug,
      'description', v_category_record.description,
      'parent_id', v_category_record.parent_id,
      'icon', v_category_record.icon,
      'created_at', v_category_record.created_at,
      'updated_at', v_category_record.updated_at
    ) INTO v_result;

  ELSIF p_action = 'delete' THEN
    -- Check if ID is provided
    IF p_id IS NULL THEN
      RAISE EXCEPTION 'Category ID is required for delete';
    END IF;

    -- Check if category exists
    IF NOT EXISTS (SELECT 1 FROM public.categories WHERE id = p_id) THEN
      RAISE EXCEPTION 'Category not found';
    END IF;

    -- Check if category has children
    IF EXISTS (SELECT 1 FROM public.categories WHERE parent_id = p_id) THEN
      RAISE EXCEPTION 'Cannot delete a category that has child categories';
    END IF;

    -- Check if category has articles
    IF EXISTS (SELECT 1 FROM public.articles WHERE category_id = p_id) THEN
      RAISE EXCEPTION 'Cannot delete a category that has articles';
    END IF;

    -- Delete the category
    DELETE FROM public.categories
    WHERE id = p_id
    RETURNING * INTO v_category_record;

    -- Convert to JSON
    SELECT jsonb_build_object(
      'id', v_category_record.id,
      'success', true,
      'message', 'Category deleted successfully'
    ) INTO v_result;

  ELSE
    RAISE EXCEPTION 'Invalid action: %', p_action;
  END IF;

  RETURN v_result;
END;
$$;

-- 4. Create a function to get all categories (no auth required)
CREATE OR REPLACE FUNCTION public.get_all_categories()
RETURNS SETOF categories
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT * FROM public.categories ORDER BY name;
$$;

-- 5. Re-enable RLS and set up policies
DO $$
DECLARE
  rls_enabled BOOLEAN;
BEGIN
  SELECT rowsecurity INTO rls_enabled
  FROM pg_tables
  WHERE schemaname = 'public' AND tablename = 'categories';

  IF NOT rls_enabled THEN
    ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
    RAISE NOTICE 'Enabled RLS on categories table';
  ELSE
    RAISE NOTICE 'RLS already enabled on categories table';
  END IF;
END
$$;

-- 6. Create RLS policies if they don't exist
DO $$
BEGIN
  -- Check if "Categories are viewable by everyone" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'categories'
    AND policyname = 'Categories are viewable by everyone'
  ) THEN
    -- Create select policy
    CREATE POLICY "Categories are viewable by everyone"
      ON public.categories FOR SELECT
      USING (true);
    RAISE NOTICE 'Created SELECT policy for categories table';
  END IF;

  -- Check if "Only admins can insert categories" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'categories'
    AND policyname = 'Only admins can insert categories'
  ) THEN
    -- Create insert policy
    CREATE POLICY "Only admins can insert categories"
      ON public.categories FOR INSERT
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
    RAISE NOTICE 'Created INSERT policy for categories table';
  END IF;

  -- Check if "Only admins can update categories" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'categories'
    AND policyname = 'Only admins can update categories'
  ) THEN
    -- Create update policy
    CREATE POLICY "Only admins can update categories"
      ON public.categories FOR UPDATE
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
    RAISE NOTICE 'Created UPDATE policy for categories table';
  END IF;

  -- Check if "Only admins can delete categories" policy exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'categories'
    AND policyname = 'Only admins can delete categories'
  ) THEN
    -- Create delete policy
    CREATE POLICY "Only admins can delete categories"
      ON public.categories FOR DELETE
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
    RAISE NOTICE 'Created DELETE policy for categories table';
  END IF;
END
$$;

-- 7. Create some sample categories if none exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM public.categories LIMIT 1) THEN
    INSERT INTO public.categories (name, slug, description, icon)
    VALUES
      ('Herbal Remedies', 'herbal-remedies', 'Natural remedies using herbs and plants', 'leaf'),
      ('Nutritional Healing', 'nutritional-healing', 'Healing through proper nutrition and diet', 'cake'),
      ('Mind-Body Practices', 'mind-body-practices', 'Techniques that combine physical and mental focus', 'sparkles'),
      ('Traditional Medicine', 'traditional-medicine', 'Ancient healing systems from around the world', 'academic-cap');
  END IF;
END
$$;
