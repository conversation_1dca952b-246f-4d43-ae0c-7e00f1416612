'use client';

import React, { useState, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { FaUpload, FaSpinner } from 'react-icons/fa';

interface DirectImageUploaderProps {
  articleId: string;
  onSuccess: () => void;
}

const DirectImageUploader: React.FC<DirectImageUploaderProps> = ({ articleId, onSuccess }) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError(null);
      setSuccess(null);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setError('Please select a file to upload');
      return;
    }

    setUploading(true);
    setError(null);
    setSuccess(null);

    try {
      const supabase = createClientComponentClient();

      // 1. Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = fileName;

      // Get the current user ID for RLS policies
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      if (!currentUser) {
        throw new Error('You must be logged in to upload images');
      }

      const { error: uploadError, data: uploadData } = await supabase.storage
        .from('media')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Error uploading file: ${uploadError.message}`);
      }

      // 2. Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('media')
        .getPublicUrl(filePath);

      // 3. Create a record in the media table
      const { data: mediaData, error: mediaError } = await supabase
        .from('media')
        .insert({
          title: file.name.split('.')[0].replace(/-|_/g, ' '),
          description: file.name.split('.')[0].replace(/-|_/g, ' '),
          file_url: publicUrl,
          file_type: file.type,
          file_size: file.size,
          uploader_id: currentUser.id,
          status: 'published'
        })
        .select()
        .single();

      if (mediaError) {
        throw new Error(`Error creating media record: ${mediaError.message}`);
      }

      // 4. Create association between article and media
      const { error: articleMediaError } = await supabase
        .from('article_media')
        .insert({
          article_id: articleId,
          media_id: mediaData.id,
          display_order: 0 // Make it the first image
        });

      if (articleMediaError) {
        throw new Error(`Error associating image with article: ${articleMediaError.message}`);
      }

      setSuccess('Image uploaded and associated with article successfully!');
      setFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Call the success callback
      onSuccess();
    } catch (err: any) {
      console.error('Error in direct upload:', err);
      setError(err.message || 'An error occurred during upload');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-300 my-4 direct-image-uploader">
      <h2 className="text-lg font-bold mb-2">Direct Image Upload</h2>

      {error && (
        <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 text-green-700 p-3 rounded-md mb-4">
          {success}
        </div>
      )}

      <div className="flex items-center space-x-4">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/jpeg,image/png,image/gif,image/webp"
          className="flex-1"
          disabled={uploading}
        />

        <button
          onClick={handleUpload}
          disabled={!file || uploading}
          className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
        >
          {uploading ? (
            <>
              <FaSpinner className="animate-spin mr-2" />
              Uploading...
            </>
          ) : (
            <>
              <FaUpload className="mr-2" />
              Upload
            </>
          )}
        </button>
      </div>

      <p className="text-sm text-gray-500 mt-2">
        Upload an image to be associated with this article. It will be set as the featured image.
      </p>
    </div>
  );
};

export default DirectImageUploader;
