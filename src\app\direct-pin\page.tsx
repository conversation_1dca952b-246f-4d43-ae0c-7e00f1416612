'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';

export default function DirectPinPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [topicId, setTopicId] = useState('');
  const [topicSlug, setTopicSlug] = useState('');
  const [topics, setTopics] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (!user || (user.role !== 'admin' && user.role !== 'moderator')) {
      router.push('/');
      return;
    }

    // Load topics
    loadTopics();
  }, [user, router]);

  const loadTopics = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const supabase = createClientComponentClient();
      
      const { data, error } = await supabase
        .from('forum_topics')
        .select(`
          id,
          title,
          slug,
          is_pinned,
          is_locked,
          created_at,
          author:author_id (username)
        `)
        .order('created_at', { ascending: false })
        .limit(20);
      
      if (error) {
        throw error;
      }
      
      setTopics(data || []);
    } catch (err: any) {
      setError(err.message || 'Failed to load topics');
    } finally {
      setLoading(false);
    }
  };

  const handleDirectPin = async (id: string, currentPinned: boolean) => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const supabase = createClientComponentClient();
      
      // Direct database update
      const { error } = await supabase
        .from('forum_topics')
        .update({
          is_pinned: !currentPinned,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        throw error;
      }
      
      // Log the action
      await supabase
        .from('moderation_actions')
        .insert({
          action: currentPinned ? 'unpin_topic' : 'pin_topic',
          action_type: currentPinned ? 'unpin_topic' : 'pin_topic',
          moderator_id: user?.id || '',
          content_id: id,
          content_type: 'forum_topic',
          reason: `Topic ${currentPinned ? 'unpinned' : 'pinned'} by moderator using direct pin tool`,
          created_at: new Date().toISOString()
        });
      
      setSuccess(`Topic ${currentPinned ? 'unpinned' : 'pinned'} successfully!`);
      
      // Update the local state
      setTopics(topics.map(topic => 
        topic.id === id ? { ...topic, is_pinned: !currentPinned } : topic
      ));
    } catch (err: any) {
      setError(err.message || 'Failed to update topic');
    } finally {
      setLoading(false);
    }
  };

  const handleDirectLock = async (id: string, currentLocked: boolean) => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const supabase = createClientComponentClient();
      
      // Direct database update
      const { error } = await supabase
        .from('forum_topics')
        .update({
          is_locked: !currentLocked,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (error) {
        throw error;
      }
      
      // Log the action
      await supabase
        .from('moderation_actions')
        .insert({
          action: currentLocked ? 'unlock_topic' : 'lock_topic',
          action_type: currentLocked ? 'unlock_topic' : 'lock_topic',
          moderator_id: user?.id || '',
          content_id: id,
          content_type: 'forum_topic',
          reason: `Topic ${currentLocked ? 'unlocked' : 'locked'} by moderator using direct lock tool`,
          created_at: new Date().toISOString()
        });
      
      setSuccess(`Topic ${currentLocked ? 'unlocked' : 'locked'} successfully!`);
      
      // Update the local state
      setTopics(topics.map(topic => 
        topic.id === id ? { ...topic, is_locked: !currentLocked } : topic
      ));
    } catch (err: any) {
      setError(err.message || 'Failed to update topic');
    } finally {
      setLoading(false);
    }
  };

  const handleGoToTopic = (slug: string) => {
    if (!slug) return;
    
    // Find the category for this topic
    const topic = topics.find(t => t.slug === slug);
    if (!topic) {
      setError('Topic not found');
      return;
    }
    
    // Get the category from the database
    const supabase = createClientComponentClient();
    supabase
      .from('forum_topics')
      .select(`
        category:category_id (slug)
      `)
      .eq('id', topic.id)
      .single()
      .then(({ data, error }) => {
        if (error) {
          setError(error.message);
          return;
        }
        
        if (!data || !data.category) {
          setError('Category not found');
          return;
        }
        
        // Navigate to the topic
        router.push(`/forums/${data.category.slug}/${slug}`);
      });
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Direct Topic Pin/Lock Tool</h1>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          {success}
        </div>
      )}
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Go to Topic by Slug</h2>
        <div className="flex gap-2">
          <input
            type="text"
            value={topicSlug}
            onChange={(e) => setTopicSlug(e.target.value)}
            placeholder="Enter topic slug"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
          />
          <button
            onClick={() => handleGoToTopic(topicSlug)}
            disabled={!topicSlug || loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            Go to Topic
          </button>
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Recent Topics</h2>
        
        {loading && <p className="text-gray-500">Loading topics...</p>}
        
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead className="bg-gray-100">
              <tr>
                <th className="py-2 px-4 border-b text-left">Title</th>
                <th className="py-2 px-4 border-b text-left">Author</th>
                <th className="py-2 px-4 border-b text-left">Created</th>
                <th className="py-2 px-4 border-b text-center">Pinned</th>
                <th className="py-2 px-4 border-b text-center">Locked</th>
                <th className="py-2 px-4 border-b text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              {topics.map((topic) => (
                <tr key={topic.id} className="hover:bg-gray-50">
                  <td className="py-2 px-4 border-b">
                    <a 
                      href="#" 
                      onClick={(e) => {
                        e.preventDefault();
                        handleGoToTopic(topic.slug);
                      }}
                      className="text-blue-600 hover:underline"
                    >
                      {topic.title}
                    </a>
                  </td>
                  <td className="py-2 px-4 border-b">{topic.author?.username || 'Unknown'}</td>
                  <td className="py-2 px-4 border-b">{new Date(topic.created_at).toLocaleString()}</td>
                  <td className="py-2 px-4 border-b text-center">
                    <span className={`inline-block w-3 h-3 rounded-full ${topic.is_pinned ? 'bg-green-500' : 'bg-red-500'}`}></span>
                  </td>
                  <td className="py-2 px-4 border-b text-center">
                    <span className={`inline-block w-3 h-3 rounded-full ${topic.is_locked ? 'bg-yellow-500' : 'bg-gray-300'}`}></span>
                  </td>
                  <td className="py-2 px-4 border-b text-center">
                    <div className="flex justify-center space-x-2">
                      <button
                        onClick={() => handleDirectPin(topic.id, topic.is_pinned)}
                        disabled={loading}
                        className={`px-3 py-1 rounded-md ${
                          topic.is_pinned 
                            ? 'bg-gray-100 text-gray-700 hover:bg-gray-200' 
                            : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                        }`}
                      >
                        {topic.is_pinned ? 'Unpin' : 'Pin'}
                      </button>
                      <button
                        onClick={() => handleDirectLock(topic.id, topic.is_locked)}
                        disabled={loading}
                        className={`px-3 py-1 rounded-md ${
                          topic.is_locked 
                            ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                            : 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                        }`}
                      >
                        {topic.is_locked ? 'Unlock' : 'Lock'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="mt-4">
          <button
            onClick={loadTopics}
            disabled={loading}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50"
          >
            Refresh Topics
          </button>
        </div>
      </div>
    </div>
  );
}
