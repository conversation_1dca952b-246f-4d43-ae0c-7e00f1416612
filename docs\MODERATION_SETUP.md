# Setting Up the Moderation System

This document provides instructions for setting up the moderation system for the NatureHeals.info forum.

## Database Setup

### 1. Apply Database Migrations

First, you need to create the necessary database tables and set up Row Level Security (RLS) policies:

#### Option 1: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
cd supabase
supabase db push
```

#### Option 2: Using Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of each migration file in the `supabase/migrations` directory
4. Execute them in order:
   - `20240101000001_create_moderation_tables.sql`
   - `20240101000002_create_moderation_rls_policies.sql`
   - `20240101000004_update_profiles_for_bans.sql`
   - (Optional) `20240101000003_add_sample_moderation_data.sql`

### 2. Verify Database Setup

After applying the migrations:

1. Go to the Table Editor in your Supabase dashboard
2. Verify that the following tables have been created:
   - `user_bans`
   - `reported_content`
   - `moderation_actions`
   - `auto_moderation_logs`
   - `user_notifications`
3. Check that the `profiles` table has the new columns:
   - `is_banned`
   - `ban_expires_at`
4. Verify that RLS policies are enabled for all tables

## Configuration

### 1. Update Banned Words List

The content filtering system uses a list of banned words. You can update this list in the `src/lib/forums.ts` file:

```typescript
// List of banned words/phrases for automatic filtering
const BANNED_WORDS = [
  'badword1',
  'badword2',
  'offensive1',
  'offensive2',
  // Add more banned words as needed
];
```

Replace the placeholder words with actual words you want to filter.

### 2. Configure Notification Settings

If you want to customize the notification messages, you can update the templates in the `src/lib/notifications.ts` file.

## Testing

After setting up the database and configuration:

1. Follow the instructions in the `docs/MODERATION_TESTING.md` file to test all moderation features
2. Use the `scripts/test-moderation.js` script to test the backend functionality

## Troubleshooting

### Common Issues

#### RLS Policy Errors

If you encounter permission errors when accessing the moderation tables:

1. Verify that the RLS policies were applied correctly
2. Check that the user has the correct role (admin, moderator, or user)
3. Make sure the `profiles` table has the correct role values

#### Missing Tables

If some tables are missing:

1. Check the Supabase SQL Editor history to see if there were any errors during migration
2. Verify that all migration scripts were executed
3. Try running the missing table creation scripts again

#### Content Filtering Not Working

If the automatic content filtering is not working:

1. Verify that the `BANNED_WORDS` array in `src/lib/forums.ts` is correctly defined
2. Check that the `autoModerateContent` function is being called before content is saved
3. Look for any errors in the browser console or server logs

## Maintenance

### Regular Tasks

1. **Review Banned Words List**: Periodically update the banned words list based on community needs
2. **Clean Up Old Data**: Consider implementing a policy to delete or archive old moderation logs and resolved reports
3. **Monitor Performance**: Watch for any performance issues with the moderation tables, especially as they grow larger

### Database Indexes

The migration scripts create indexes for better performance. If you notice slow queries, you may need to add additional indexes based on your specific query patterns.

## Security Considerations

1. **RLS Policies**: The RLS policies are designed to restrict access to moderation data. Be careful when modifying these policies.
2. **Admin/Moderator Roles**: Only grant admin and moderator roles to trusted users.
3. **Sensitive Data**: The moderation logs and reports may contain sensitive information. Ensure this data is handled appropriately.

## Extending the System

If you want to extend the moderation system:

1. **Additional Ban Types**: You can add more ban types by updating the `ban_type` check constraint in the `user_bans` table
2. **Custom Filters**: You can implement more sophisticated content filtering beyond simple word matching
3. **Automated Moderation**: Consider implementing AI-based moderation tools for more advanced content filtering
