'use client';

import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function TestPinPage() {
  const [topicId, setTopicId] = useState('');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleCheckTopic = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const supabase = createClientComponentClient();
      
      // Check if the topic exists
      const { data, error: checkError } = await supabase
        .from('forum_topics')
        .select('*')
        .eq('id', topicId)
        .maybeSingle();
      
      if (checkError) {
        setError(`Error checking topic: ${checkError.message}`);
        return;
      }
      
      if (!data) {
        setError(`Topic with ID ${topicId} not found`);
        return;
      }
      
      setResult({ topic: data });
    } catch (err: any) {
      setError(`Exception: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePinTopic = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const supabase = createClientComponentClient();
      
      // First check if the topic exists
      const { data: topic, error: checkError } = await supabase
        .from('forum_topics')
        .select('*')
        .eq('id', topicId)
        .maybeSingle();
      
      if (checkError) {
        setError(`Error checking topic: ${checkError.message}`);
        return;
      }
      
      if (!topic) {
        setError(`Topic with ID ${topicId} not found`);
        return;
      }
      
      // Update the topic directly
      const { data, error: updateError } = await supabase
        .from('forum_topics')
        .update({
          is_pinned: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', topicId)
        .select();
      
      if (updateError) {
        setError(`Error updating topic: ${updateError.message}`);
        return;
      }
      
      setResult({ updated: data });
    } catch (err: any) {
      setError(`Exception: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleUnpinTopic = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const supabase = createClientComponentClient();
      
      // First check if the topic exists
      const { data: topic, error: checkError } = await supabase
        .from('forum_topics')
        .select('*')
        .eq('id', topicId)
        .maybeSingle();
      
      if (checkError) {
        setError(`Error checking topic: ${checkError.message}`);
        return;
      }
      
      if (!topic) {
        setError(`Topic with ID ${topicId} not found`);
        return;
      }
      
      // Update the topic directly
      const { data, error: updateError } = await supabase
        .from('forum_topics')
        .update({
          is_pinned: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', topicId)
        .select();
      
      if (updateError) {
        setError(`Error updating topic: ${updateError.message}`);
        return;
      }
      
      setResult({ updated: data });
    } catch (err: any) {
      setError(`Exception: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Test Topic Pin/Unpin</h1>
      
      <div className="mb-4">
        <label htmlFor="topicId" className="block text-sm font-medium text-gray-700 mb-1">
          Topic ID
        </label>
        <input
          type="text"
          id="topicId"
          value={topicId}
          onChange={(e) => setTopicId(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          placeholder="Enter topic ID"
        />
      </div>
      
      <div className="flex space-x-2 mb-4">
        <button
          onClick={handleCheckTopic}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          disabled={loading || !topicId}
        >
          Check Topic
        </button>
        
        <button
          onClick={handlePinTopic}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          disabled={loading || !topicId}
        >
          Pin Topic
        </button>
        
        <button
          onClick={handleUnpinTopic}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          disabled={loading || !topicId}
        >
          Unpin Topic
        </button>
      </div>
      
      {loading && (
        <div className="mb-4 p-4 bg-gray-100 rounded">
          Loading...
        </div>
      )}
      
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded">
          <h2 className="font-bold">Error:</h2>
          <p>{error}</p>
        </div>
      )}
      
      {result && (
        <div className="mb-4 p-4 bg-green-100 text-green-700 rounded">
          <h2 className="font-bold">Result:</h2>
          <pre className="whitespace-pre-wrap">{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
