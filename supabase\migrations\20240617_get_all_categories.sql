-- Create a function to get all categories, bypassing <PERSON><PERSON>
CREATE OR <PERSON><PERSON>LACE FUNCTION get_all_categories()
RETURNS SETOF categories
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM categories ORDER BY name;
$$;

-- Create a function to get service status
CREATE OR REPLACE FUNCTION get_service_status()
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT jsonb_build_object(
    'status', 'ok',
    'timestamp', extract(epoch from now()),
    'database', current_database(),
    'version', version()
  );
$$;
