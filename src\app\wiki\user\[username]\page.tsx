'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { FaArrowLeft, FaUser, FaBook, FaEdit, FaComment } from 'react-icons/fa';
import UserLevelDisplay from '@/components/UserLevelDisplay';
import VerificationBadge from '@/components/VerificationBadge';

interface WikiContribution {
  id: string;
  type: 'article' | 'edit' | 'comment';
  title: string;
  slug: string;
  created_at: string;
  content?: string;
}

export default function UserWikiContributionsPage({ params }: { params: { username: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const [contributions, setContributions] = useState<WikiContribution[]>([]);
  const [profile, setProfile] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const itemsPerPage = 10;

  // Unwrap params with React.use() to avoid warnings
  const unwrappedParams = use(params);
  const username = unwrappedParams.username;

  useEffect(() => {
    async function loadUserAndContributions() {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClientComponentClient();

        // First, get the user profile by username
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('username', username)
          .maybeSingle();

        if (profileError) {
          if (profileError.code === 'PGRST116') {
            // This is the error when multiple rows are returned
            setError('Multiple users found with this username. Please contact support.');
          } else {
            setError(profileError.message || 'Error loading user profile');
          }
          setIsLoading(false);
          return;
        }

        if (!profileData) {
          setError('User not found');
          setIsLoading(false);
          return;
        }

        setProfile(profileData);

        // Get articles created by the user
        const { data: articlesData, error: articlesError } = await supabase
          .from('articles')
          .select('id, title, slug, created_at')
          .eq('author_id', profileData.id)
          .order('created_at', { ascending: false });

        if (articlesError) {
          setError(articlesError.message || 'Error loading articles');
          setIsLoading(false);
          return;
        }

        // Get article edits by the user
        const { data: editsData, error: editsError } = await supabase
          .from('revisions')
          .select(`
            id,
            created_at,
            article:article_id (
              title,
              slug
            )
          `)
          .eq('editor_id', profileData.id)
          .order('created_at', { ascending: false });

        if (editsError) {
          setError(editsError.message || 'Error loading article edits');
          setIsLoading(false);
          return;
        }

        // Get comments by the user
        const { data: commentsData, error: commentsError } = await supabase
          .from('comments')
          .select(`
            id,
            content,
            created_at,
            article:article_id (
              title,
              slug
            )
          `)
          .eq('author_id', profileData.id)
          .order('created_at', { ascending: false });

        if (commentsError) {
          setError(commentsError.message || 'Error loading comments');
          setIsLoading(false);
          return;
        }

        // Transform and combine all contributions
        const allContributions: WikiContribution[] = [
          ...(articlesData || []).map((article: any) => ({
            id: article.id,
            type: 'article' as const,
            title: article.title,
            slug: article.slug,
            created_at: article.created_at
          })),
          ...(editsData || []).map((edit: any) => ({
            id: edit.id,
            type: 'edit' as const,
            title: edit.article?.title || 'Unknown Article',
            slug: edit.article?.slug || '',
            created_at: edit.created_at
          })),
          ...(commentsData || []).map((comment: any) => ({
            id: comment.id,
            type: 'comment' as const,
            title: comment.article?.title || 'Unknown Article',
            slug: comment.article?.slug || '',
            created_at: comment.created_at,
            content: comment.content
          }))
        ];

        // Sort by date (newest first)
        allContributions.sort((a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        // Paginate
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = page * itemsPerPage;
        const paginatedContributions = allContributions.slice(startIndex, endIndex);

        setContributions(paginatedContributions);
        setHasMore(allContributions.length > endIndex);
      } catch (err: any) {
        console.error('Error in loadUserAndContributions:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    }

    if (username) {
      loadUserAndContributions();
    }
  }, [username, page]);

  const loadMoreContributions = () => {
    setPage(prevPage => prevPage + 1);
  };

  const truncateContent = (content: string, maxLength = 150) => {
    if (!content) return '';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const getContributionIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <FaBook className="text-green-600" />;
      case 'edit':
        return <FaEdit className="text-blue-600" />;
      case 'comment':
        return <FaComment className="text-purple-600" />;
      default:
        return null;
    }
  };

  const getContributionLabel = (type: string) => {
    switch (type) {
      case 'article':
        return 'Created article';
      case 'edit':
        return 'Edited article';
      case 'comment':
        return 'Commented on';
      default:
        return 'Contributed to';
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 p-4 rounded-md text-red-700 mb-4">
          {error}
        </div>
        <button
          onClick={() => router.push('/wiki')}
          className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Wiki
        </button>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">User Not Found</h1>
          <p className="mb-6">The user you're looking for doesn't exist or has deleted their account.</p>
          <button
            onClick={() => router.push('/wiki')}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Return to Wiki
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href="/wiki" className="inline-flex items-center text-nature-green hover:underline">
          <FaArrowLeft className="mr-2" /> Back to Wiki
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-4">
              {profile.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt={profile.full_name || profile.username}
                  className="h-full w-full object-cover"
                />
              ) : (
                <FaUser className="text-gray-400 text-2xl" />
              )}
            </div>
            <div>
              <div className="flex items-center">
                <h1 className="text-2xl font-bold">{profile.full_name || profile.username}</h1>
                {profile.is_verified_expert && <VerificationBadge className="ml-2" />}
              </div>
              <div className="flex items-center text-gray-600">
                <span>@{profile.username}</span>
                <UserLevelDisplay level={profile.level || 1} className="ml-2" />
              </div>
            </div>
          </div>

          <div className="flex items-center text-gray-600 mb-4">
            <FaBook className="mr-2" />
            <span>{contributions.length} wiki contributions</span>
          </div>

          <Link href={`/profile/${profile.username}`} className="text-nature-green hover:underline">
            View Full Profile
          </Link>
        </div>
      </div>

      <h2 className="text-2xl font-bold mb-6">Wiki Contributions by {profile.username}</h2>

      {contributions.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-8 text-center text-gray-500">
          <FaBook className="mx-auto text-4xl mb-4 text-gray-300" />
          <p>This user hasn't made any wiki contributions yet.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {contributions.map((contribution) => (
            <div key={`${contribution.type}-${contribution.id}`} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
              <div className="p-5">
                <div className="flex items-start">
                  <div className="mt-1 mr-3">
                    {getContributionIcon(contribution.type)}
                  </div>
                  <div className="flex-1">
                    <div className="mb-2">
                      <div className="text-sm text-gray-600 mb-1">
                        {getContributionLabel(contribution.type)}:
                      </div>
                      <Link
                        href={`/wiki/${contribution.slug}`}
                        className="text-lg font-semibold text-nature-green hover:underline"
                      >
                        {contribution.title}
                      </Link>
                      <div className="text-sm text-gray-500">
                        {formatDistanceToNow(new Date(contribution.created_at), { addSuffix: true })}
                      </div>
                    </div>

                    {contribution.content && (
                      <div className="prose prose-sm max-w-none mt-2">
                        <p>{truncateContent(contribution.content)}</p>
                      </div>
                    )}

                    <div className="mt-3">
                      <Link
                        href={`/wiki/${contribution.slug}${contribution.type === 'comment' ? `#comment-${contribution.id}` : ''}`}
                        className="text-nature-green hover:underline text-sm"
                      >
                        View {contribution.type === 'article' ? 'article' : contribution.type === 'edit' ? 'edit history' : 'comment'} →
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {hasMore && (
        <div className="mt-6 text-center">
          <button
            onClick={loadMoreContributions}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Load More Contributions'}
          </button>
        </div>
      )}
    </div>
  );
}
