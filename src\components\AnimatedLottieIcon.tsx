'use client';

import { Player } from '@lottiefiles/react-lottie-player';
import React from 'react';

type AnimatedLottieIconProps = {
  src: string;
  className?: string;
  loop?: boolean;
  autoplay?: boolean;
};

export default function AnimatedLottieIcon({
  src,
  className = '',
  loop = true,
  autoplay = true,
}: AnimatedLottieIconProps) {
  return (
    <Player
      src={src}
      className={className}
      loop={loop}
      autoplay={autoplay}
      style={{ width: '56px', height: '56px' }}
      keepLastFrame={true}
    />
  );
}