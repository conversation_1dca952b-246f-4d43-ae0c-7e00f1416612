'use client';

import React, { useState } from 'react';
import UserLeaderboard from '@/components/UserLeaderboard';
import Link from 'next/link';

export default function LeaderboardsPage() {
  const [activeTab, setActiveTab] = useState<'reputation' | 'articles' | 'forum_posts' | 'solutions'>('reputation');
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Community Leaderboards</h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex -mb-px">
              <button
                onClick={() => setActiveTab('reputation')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'reputation'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                Reputation
              </button>
              <button
                onClick={() => setActiveTab('articles')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'articles'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                Articles
              </button>
              <button
                onClick={() => setActiveTab('forum_posts')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'forum_posts'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                Forum Posts
              </button>
              <button
                onClick={() => setActiveTab('solutions')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'solutions'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                Solutions
              </button>
            </nav>
          </div>
          
          <div className="p-6">
            {activeTab === 'reputation' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Top Contributors by Reputation</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  These members have earned the most reputation points through their contributions to the community.
                  Reputation is earned by creating articles, posting in forums, receiving likes, and having posts marked as solutions.
                </p>
                <UserLeaderboard type="reputation" limit={20} />
              </div>
            )}
            
            {activeTab === 'articles' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Top Article Contributors</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  These members have contributed the most articles to our knowledge base, sharing their expertise and insights with the community.
                </p>
                <UserLeaderboard type="articles" limit={20} />
              </div>
            )}
            
            {activeTab === 'forum_posts' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Most Active Forum Members</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  These members are the most active in our community forums, helping others and engaging in discussions.
                </p>
                <UserLeaderboard type="forum_posts" limit={20} />
              </div>
            )}
            
            {activeTab === 'solutions' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Top Solution Providers</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  These members have had the most forum posts marked as solutions, providing valuable answers to community questions.
                </p>
                <UserLeaderboard type="solutions" limit={20} />
              </div>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">How Reputation Works</h2>
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <p>
                Reputation points are earned through various activities on NatureHeals.info:
              </p>
              <ul>
                <li><strong>Creating an article:</strong> +10 points</li>
                <li><strong>Creating a forum topic:</strong> +5 points</li>
                <li><strong>Posting in a forum:</strong> +3 points</li>
                <li><strong>Adding a comment:</strong> +2 points</li>
                <li><strong>Receiving a reaction:</strong> +1 point</li>
                <li><strong>Having a post marked as solution:</strong> +5 points</li>
              </ul>
              <p>
                As you earn reputation points, you'll level up and unlock new badges and privileges.
              </p>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Levels and Badges</h2>
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <p>
                Your level is calculated based on your reputation points:
              </p>
              <ul>
                <li><strong>Level 1 (Seedling):</strong> 0+ points</li>
                <li><strong>Level 2 (Sprout):</strong> 10+ points</li>
                <li><strong>Level 5 (Blooming Plant):</strong> 100+ points</li>
                <li><strong>Level 10 (Herbal Practitioner):</strong> 450+ points</li>
                <li><strong>Level 20 (Herbal Master):</strong> 2000+ points</li>
                <li><strong>Level 30 (Herbal Sage):</strong> 5000+ points</li>
              </ul>
              <p>
                Special badges are awarded for achievements like reaching certain levels, providing solutions, and becoming a verified expert.
              </p>
              <p className="mt-4">
                <Link href="/profile" className="text-nature-green hover:underline">
                  View your profile
                </Link> to see your current level and badges.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
