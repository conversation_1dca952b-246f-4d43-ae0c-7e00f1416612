import { createServerClient } from '@supabase/ssr';
import { type CookieOptions } from '@supabase/ssr';

// Import these conditionally to avoid errors in pages directory
let cookies: any;
let redirect: any;

// Only import server-only modules in server environment
if (typeof window === 'undefined') {
  import('next/headers').then(module => {
    cookies = module.cookies;
  });
  import('next/navigation').then(module => {
    redirect = module.redirect;
  });
}

// Create a client-side only cookie handler
const clientCookieStore = {
  get(name: string) {
    if (typeof document !== 'undefined') {
      const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
      return match ? match[2] : undefined;
    }
    return undefined;
  },
  set(name: string, value: string, options: CookieOptions) {
    if (typeof document !== 'undefined') {
      let cookie = name + '=' + value;
      if (options.maxAge) cookie += '; Max-Age=' + options.maxAge;
      if (options.domain) cookie += '; Domain=' + options.domain;
      if (options.path) cookie += '; Path=' + options.path;
      if (options.secure) cookie += '; Secure';
      if (options.sameSite) cookie += '; SameSite=' + options.sameSite;
      document.cookie = cookie;
    }
  },
  remove(name: string, options: CookieOptions) {
    if (typeof document !== 'undefined') {
      this.set(name, '', { ...options, maxAge: 0 });
    }
  },
};

// Create a client for server components (read-only)
export const createClient = async () => {
  // Use client-side cookie handling if in browser
  if (typeof window !== 'undefined') {
    return createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: clientCookieStore,
      }
    );
  }

  // Server-side implementation with read-only cookie handlers
  try {
    // Check if cookies function is available (server component)
    if (typeof cookies === 'function') {
      // Use await to properly handle the async cookies() function
      const cookieStore = await cookies();

      return createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            async get(name: string) {
              try {
                return cookieStore.get(name)?.value;
              } catch (error) {
                console.error('Error getting cookie in server component:', error);
                return undefined;
              }
            },
            async set(name: string, value: string, options: CookieOptions) {
              // Intentionally empty to avoid errors in server components
            },
            async remove(name: string, options: CookieOptions) {
              // Intentionally empty to avoid errors in server components
            },
          },
        }
      );
    }
  } catch (error) {
    console.error('Error creating Supabase client in server component:', error);
  }

  // Return a client with no cookie handling as fallback
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
    }
  );
};

// Create a client for server actions (can modify cookies)
export const createActionClient = async () => {
  // Use client-side cookie handling if in browser
  if (typeof window !== 'undefined') {
    return createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: clientCookieStore,
      }
    );
  }

  // Server-side implementation with full cookie handling for server actions
  try {
    // Check if cookies function is available (server component)
    if (typeof cookies === 'function') {
      // Use await to properly handle the async cookies() function
      const cookieStore = await cookies();

      return createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            async get(name: string) {
              try {
                return cookieStore.get(name)?.value;
              } catch (error) {
                console.error('Error getting cookie in server action:', error);
                return undefined;
              }
            },
            async set(name: string, value: string, options: CookieOptions) {
              try {
                cookieStore.set({ name, value, ...options });
              } catch (error) {
                console.error('Error setting cookie in server action:', error);
              }
            },
            async remove(name: string, options: CookieOptions) {
              try {
                cookieStore.set({ name, value: '', ...options });
              } catch (error) {
                console.error('Error removing cookie in server action:', error);
              }
            },
          },
        }
      );
    }
  } catch (error) {
    console.error('Error creating Supabase action client:', error);
  }

  // Return a client with no cookie handling as fallback
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
    }
  );
};

export async function getSession() {
  const supabase = await createClient();
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return session;
  } catch (error) {
    return null;
  }
}

export async function getUserProfile() {
  const session = await getSession();

  if (!session) {
    return null;
  }

  const supabase = await createClient();
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', session.user.id)
    .single();

  return profile;
}

export async function requireAuth() {
  const session = await getSession();
  if (!session) {
    if (typeof redirect === 'function') {
      redirect('/auth/signin');
    } else {
      // Fallback for client-side
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/signin';
      }
      throw new Error('Not authenticated');
    }
  }
  return session;
}