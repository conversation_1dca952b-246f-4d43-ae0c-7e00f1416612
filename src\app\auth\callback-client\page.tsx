'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function AuthCallback() {
  const router = useRouter();

  useEffect(() => {
    // Parse the hash fragment for access_token, refresh_token, etc.
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);

    const access_token = params.get('access_token');
    const refresh_token = params.get('refresh_token');
    const expires_in = params.get('expires_in');
    const token_type = params.get('token_type');

    if (access_token && refresh_token && token_type) {
      const supabase = createClientComponentClient();

      // Set the session in Supabase client
      supabase.auth.setSession({
        access_token,
        refresh_token,
        token_type,
        expires_in: expires_in ? parseInt(expires_in, 10) : undefined,
        user: null, // Supabase will fetch user info
      }).then(() => {
        // Clean up the URL and redirect to home or profile
        router.replace('/');
      });
    } else {
      // If no tokens, just redirect home
      router.replace('/');
    }
  }, [router]);

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Completing sign in...</h1>
          <div className="mt-4 flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
