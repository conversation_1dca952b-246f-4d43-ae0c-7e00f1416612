'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useAuth } from './AuthProvider';
import { usePathname } from 'next/navigation';
import SiteLogo from './SiteLogo';

export default function MobileNavigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, supabase } = useAuth();
  const pathname = usePathname();
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when path changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);
  
  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node) && isMenuOpen) {
        setIsMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // Handle escape key press
  useEffect(() => {
    function handleEscapeKey(event: KeyboardEvent) {
      if (event.key === 'Escape' && isMenuOpen) {
        setIsMenuOpen(false);
      }
    }

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isMenuOpen]);

  return (
    <>
      <button
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        className="md:hidden text-white p-2 -m-2 rounded-md hover:bg-green-800 transition-colors focus:outline-none focus:ring-2 focus:ring-green-100"
        aria-label="Open menu"
        aria-expanded={isMenuOpen}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden transition-opacity duration-300 ease-in-out">
          <div
            ref={menuRef}
            className="bg-green-800 h-full w-4/5 max-w-sm shadow-xl overflow-y-auto transform transition-transform duration-300 ease-in-out"
            role="dialog"
            aria-modal="true"
            aria-label="Mobile navigation menu"
          >
            <div className="p-6 border-b border-green-700">
              <div className="flex justify-between items-center">
                <div onClick={() => setIsMenuOpen(false)}>
                  <SiteLogo size="lg" />
                </div>
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="text-white hover:text-green-100 p-2 bg-green-700 rounded-full focus:outline-none focus:ring-2 focus:ring-green-100"
                  aria-label="Close menu"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <nav className="p-5">
              <ul className="space-y-4">
                <li>
                  <Link
                    href="/"
                    className={`block py-2 px-3 rounded-md text-white ${pathname === '/' ? 'bg-green-700 font-medium' : 'hover:bg-green-700'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    href="/wiki"
                    className={`block py-2 px-3 rounded-md text-white ${pathname === '/wiki' ? 'bg-green-700 font-medium' : 'hover:bg-green-700'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Wiki
                  </Link>
                </li>
                <li>
                  <Link
                    href="/categories"
                    className={`block py-2 px-3 rounded-md text-white ${pathname === '/categories' ? 'bg-green-700 font-medium' : 'hover:bg-green-700'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Categories
                  </Link>
                </li>
                <li>
                  <Link
                    href="/forums"
                    className={`block py-2 px-3 rounded-md text-white ${pathname.startsWith('/forums') ? 'bg-green-700 font-medium' : 'hover:bg-green-700'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Forums
                  </Link>
                </li>
                <li>
                  <Link
                    href="/contribute"
                    className={`block py-2 px-3 rounded-md text-white ${pathname === '/contribute' ? 'bg-green-700 font-medium' : 'hover:bg-green-700'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Contribute
                  </Link>
                </li>
                <li>
                  <Link
                    href="/about"
                    className={`block py-2 px-3 rounded-md text-white ${pathname === '/about' ? 'bg-green-700 font-medium' : 'hover:bg-green-700'}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    About
                  </Link>
                </li>
              </ul>
            </nav>

            <div className="border-t border-green-700 p-5">
              {!user ? (
                <div className="mb-6">
                  <div className="flex flex-col space-y-4">
                    <Link
                      href="/auth/signin"
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-blue-700 transition-colors text-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="w-full bg-yellow-500 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-yellow-600 transition-colors text-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Register
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="mb-6">
                  <div className="flex flex-col space-y-4">
                    <Link
                      href="/profile"
                      className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-blue-700 transition-colors text-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      My Profile
                    </Link>
                    <button
                      onClick={async () => {
                        await supabase.auth.signOut();
                        setIsMenuOpen(false);
                        window.location.href = '/';
                      }}
                      className="w-full bg-red-500 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-red-600 transition-colors text-center"
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                <Link
                  href="/search"
                  className="flex items-center space-x-2 text-white hover:text-green-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <span>Search</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
