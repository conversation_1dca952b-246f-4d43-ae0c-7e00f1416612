'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useClickOutside } from '@/lib/hooks';
import { useAuth } from './AuthProvider';
import SiteLogo from './SiteLogo';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const { user, supabase } = useAuth();

  // Close the menu when clicking outside
  useClickOutside(menuRef, onClose);

  // Close the menu when the path changes (user navigates)
  useEffect(() => {
    onClose();
  }, [pathname, onClose]);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden">
      <div
        ref={menuRef}
        className="bg-white h-full w-4/5 max-w-sm shadow-xl transform transition-transform duration-300 ease-in-out"
        style={{
          transform: 'translateX(0)',
          height: '100%',
          overflowY: 'auto'
        }}
      >
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div onClick={onClose}>
              <SiteLogo size="lg" />
            </div>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close menu"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <nav className="p-5">
          <ul className="space-y-4">
            <li>
              <Link
                href="/"
                className={`block py-2 px-3 rounded-md ${pathname === '/' ? 'bg-green-50 text-nature-green font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                href="/wiki"
                className={`block py-2 px-3 rounded-md ${pathname === '/wiki' ? 'bg-green-50 text-nature-green font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
              >
                Wiki
              </Link>
            </li>
            <li>
              <Link
                href="/categories"
                className={`block py-2 px-3 rounded-md ${pathname === '/categories' ? 'bg-green-50 text-nature-green font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
              >
                Categories
              </Link>
            </li>
            <li>
              <Link
                href="/contribute"
                className={`block py-2 px-3 rounded-md ${pathname === '/contribute' ? 'bg-green-50 text-nature-green font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
              >
                Contribute
              </Link>
            </li>
            <li>
              <Link
                href="/about"
                className={`block py-2 px-3 rounded-md ${pathname === '/about' ? 'bg-green-50 text-nature-green font-medium' : 'text-gray-700 hover:bg-gray-50'}`}
              >
                About
              </Link>
            </li>
          </ul>
        </nav>

        <div className="border-t border-gray-200 p-5">
          {!user ? (
            <div className="mb-6">
              <div className="flex flex-col space-y-3">
                <Link
                  href="/auth/signin"
                  className="flex items-center justify-center space-x-2 bg-green-800 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-green-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  <span>Sign In</span>
                </Link>
                <Link
                  href="/auth/signup"
                  className="flex items-center justify-center space-x-2 bg-yellow-600 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-yellow-500 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  <span>Register</span>
                </Link>
              </div>
            </div>
          ) : (
            <div className="mb-6">
              <div className="flex flex-col space-y-3">
                <Link
                  href="/profile"
                  className="flex items-center justify-center space-x-2 bg-green-800 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-green-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span>My Profile</span>
                </Link>
                <button
                  onClick={async () => {
                    await supabase.auth.signOut();
                    onClose();
                    window.location.href = '/';
                  }}
                  className="flex items-center justify-center space-x-2 bg-red-600 text-white py-3 px-4 rounded-md font-medium shadow-sm hover:bg-red-500 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          )}

          <div className="space-y-3">
            <Link
              href="/search"
              className="flex items-center space-x-2 text-gray-700 hover:text-nature-green"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <span>Search</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
