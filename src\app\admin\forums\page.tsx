'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClient } from '@/lib/supabase';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';

interface ForumTopic {
  id: string;
  title: string;
  slug: string;
  category_id: string;
  author_id: string;
  is_pinned: boolean;
  is_locked: boolean;
  view_count: number;
  last_post_at: string;
  created_at: string;
  updated_at: string;
  author?: {
    username: string;
    full_name?: string;
  };
  category?: {
    name: string;
    slug: string;
  };
  post_count?: number;
}

interface ForumPost {
  id: string;
  topic_id: string;
  author_id: string;
  content: string;
  created_at: string;
  author?: {
    username: string;
    full_name?: string;
  };
  topic?: {
    title: string;
    slug: string;
    category: {
      slug: string;
    };
  };
}

interface ReportedContent {
  id: string;
  content_type: 'topic' | 'post';
  content_id: string;
  reporter_id: string;
  reason: string;
  status: 'pending' | 'resolved' | 'dismissed';
  created_at: string;
  reporter?: {
    username: string;
    full_name?: string;
  };
  content?: ForumTopic | ForumPost;
}

export default function AdminForumsPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [activeTab, setActiveTab] = useState<'reported' | 'topics' | 'posts'>('reported');
  const [reportedContent, setReportedContent] = useState<ReportedContent[]>([]);
  const [topics, setTopics] = useState<ForumTopic[]>([]);
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedContent, setSelectedContent] = useState<any | null>(null);
  const [actionType, setActionType] = useState<'view' | 'edit' | 'delete' | 'lock' | 'pin' | null>(null);

  useEffect(() => {
    async function checkAuth() {
      if (loading) return;

      if (!user) {
        router.push('/auth/signin?redirect=/admin/forums');
        return;
      }

      if (user.role !== 'admin' && user.role !== 'moderator') {
        router.push('/');
        return;
      }

      loadContent();
    }

    checkAuth();
  }, [user, loading, router, activeTab]);

  const loadContent = async () => {
    setIsLoading(true);
    setError(null);

    const supabase = createClient();

    try {
      switch (activeTab) {
        case 'reported':
          const { data: reportedData, error: reportedError } = await supabase
            .from('reported_content')
            .select(`
              *,
              reporter:reporter_id (
                username,
                full_name
              )
            `)
            .eq('status', 'pending')
            .order('created_at', { ascending: false });

          if (reportedError) throw reportedError;

          // Fetch the actual content for each report
          const reportedWithContent = await Promise.all(
            (reportedData || []).map(async (report) => {
              if (report.content_type === 'topic') {
                const { data: topic } = await supabase
                  .from('forum_topics')
                  .select(`
                    *,
                    author:author_id (
                      username,
                      full_name
                    ),
                    category:category_id (
                      name,
                      slug
                    )
                  `)
                  .eq('id', report.content_id)
                  .single();

                return { ...report, content: topic };
              } else {
                const { data: post } = await supabase
                  .from('forum_posts')
                  .select(`
                    *,
                    author:author_id (
                      username,
                      full_name
                    ),
                    topic:topic_id (
                      title,
                      slug,
                      category:category_id (
                        slug
                      )
                    )
                  `)
                  .eq('id', report.content_id)
                  .single();

                return { ...report, content: post };
              }
            })
          );

          setReportedContent(reportedWithContent);
          break;

        case 'topics':
          const { data: topicsData, error: topicsError } = await supabase
            .from('forum_topics')
            .select(`
              *,
              author:author_id (
                username,
                full_name
              ),
              category:category_id (
                name,
                slug
              )
            `)
            .order('created_at', { ascending: false })
            .limit(50);

          if (topicsError) throw topicsError;

          // Get post counts for each topic
          const topicsWithCounts = await Promise.all(
            (topicsData || []).map(async (topic) => {
              const { count } = await supabase
                .from('forum_posts')
                .select('*', { count: 'exact', head: true })
                .eq('topic_id', topic.id);

              return { ...topic, post_count: count };
            })
          );

          setTopics(topicsWithCounts);
          break;

        case 'posts':
          const { data: postsData, error: postsError } = await supabase
            .from('forum_posts')
            .select(`
              *,
              author:author_id (
                username,
                full_name
              ),
              topic:topic_id (
                title,
                slug,
                category:category_id (
                  slug
                )
              )
            `)
            .order('created_at', { ascending: false })
            .limit(50);

          if (postsError) throw postsError;

          setPosts(postsData || []);
          break;
      }
    } catch (err: any) {
      console.error('Error loading content:', err);
      setError(err.message || 'Failed to load content');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReportAction = async (reportId: string, action: 'resolve' | 'dismiss') => {
    const supabase = createClient();

    try {
      const { error } = await supabase
        .from('reported_content')
        .update({
          status: action === 'resolve' ? 'resolved' : 'dismissed',
          moderator_id: user?.id,
          resolved_at: new Date().toISOString()
        })
        .eq('id', reportId);

      if (error) throw error;

      // Refresh the reported content list
      setReportedContent(prev => prev.filter(report => report.id !== reportId));
    } catch (err: any) {
      console.error(`Error ${action}ing report:`, err);
      setError(err.message || `Failed to ${action} report`);
    }
  };

  const handleTopicAction = async (topicId: string, action: 'lock' | 'unlock' | 'pin' | 'unpin' | 'delete') => {
    try {
      console.log(`Performing ${action} action on topic ${topicId}`);

      // Check if the topic exists in our current list
      const topicExists = topics.some(topic => topic.id === topicId);
      if (!topicExists) {
        console.error(`Topic with ID ${topicId} not found in the current list`);
        setError(`Topic with ID ${topicId} not found. It may have been deleted or moved.`);
        return;
      }

      if (action === 'delete') {
        // Use the deleteForumTopic function from forums.ts
        const { deleteForumTopic } = await import('@/lib/forums');
        await deleteForumTopic({
          topicId,
          moderatorId: user?.id || '',
          hardDelete: true,
          reason: 'Topic deleted from admin panel'
        });

        // Refresh the topics list
        setTopics(prev => prev.filter(topic => topic.id !== topicId));
      } else if (action === 'lock' || action === 'unlock') {
        // Use the direct action function
        const { directLockTopic } = await import('@/lib/direct-forum-actions');
        const result = await directLockTopic({
          topicId,
          isLocked: action === 'lock',
          moderatorId: user?.id || '',
          reason: `Topic ${action === 'lock' ? 'locked' : 'unlocked'} from admin panel`
        });

        if (!result.success) {
          throw new Error(result.error || `Failed to ${action} topic`);
        }

        // Update the topic in the list and force a re-render
        const updatedTopics = topics.map(topic => {
          if (topic.id === topicId) {
            return { ...topic, is_locked: action === 'lock' };
          }
          return topic;
        });
        setTopics(updatedTopics);

        // Show success message
        setError(null);
        alert(`Topic ${action === 'lock' ? 'locked' : 'unlocked'} successfully`);
      } else if (action === 'pin' || action === 'unpin') {
        // Use the direct action function
        const { directPinTopic } = await import('@/lib/direct-forum-actions');
        const result = await directPinTopic({
          topicId,
          isPinned: action === 'pin',
          moderatorId: user?.id || '',
          reason: `Topic ${action === 'pin' ? 'pinned' : 'unpinned'} from admin panel`
        });

        if (!result.success) {
          throw new Error(result.error || `Failed to ${action} topic`);
        }

        // Update the topic in the list and force a re-render
        const updatedTopics = topics.map(topic => {
          if (topic.id === topicId) {
            return { ...topic, is_pinned: action === 'pin' };
          }
          return topic;
        });
        setTopics(updatedTopics);

        // Show success message
        setError(null);
        alert(`Topic ${action === 'pin' ? 'pinned' : 'unpinned'} successfully`);
      }
    } catch (err: any) {
      console.error(`Error performing ${action} action:`, err);
      setError(err.message || `Failed to ${action} topic`);
    }
  };

  const handlePostAction = async (postId: string, action: 'delete') => {
    const supabase = createClient();

    try {
      if (action === 'delete') {
        const { error } = await supabase
          .from('forum_posts')
          .delete()
          .eq('id', postId);

        if (error) throw error;

        // Refresh the posts list
        setPosts(prev => prev.filter(post => post.id !== postId));
      }
    } catch (err: any) {
      console.error(`Error performing ${action} action:`, err);
      setError(err.message || `Failed to ${action} post`);
    }
  };

  const handleContentSelect = (content: any, action: 'view' | 'edit' | 'delete' | 'lock' | 'pin') => {
    setSelectedContent(content);
    setActionType(action);
  };

  const closeModal = () => {
    setSelectedContent(null);
    setActionType(null);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Forum Moderation</h1>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 text-center">
            <p className="text-gray-500 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Forum Moderation</h1>

        {error && (
          <div className="mb-6 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-md">
            {error}
          </div>
        )}

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex -mb-px">
              <button
                onClick={() => setActiveTab('reported')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'reported'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                Reported Content
                {reportedContent.length > 0 && (
                  <span className="ml-2 px-2 py-0.5 text-xs bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full">
                    {reportedContent.length}
                  </span>
                )}
              </button>
              <button
                onClick={() => setActiveTab('topics')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'topics'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                Topics
              </button>
              <button
                onClick={() => setActiveTab('posts')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'posts'
                    ? 'border-nature-green text-nature-green'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                Posts
              </button>
            </nav>
          </div>

          <div className="p-6">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green"></div>
              </div>
            ) : activeTab === 'reported' ? (
              reportedContent.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">No reported content to review</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {reportedContent.map((report) => (
                    <div key={report.id} className="bg-gray-50 dark:bg-gray-750 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-start">
                        <div>
                          <span className="px-2 py-1 text-xs bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full">
                            Reported {report.content_type}
                          </span>
                          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            Reported by {report.reporter?.full_name || report.reporter?.username} • {formatDistanceToNow(new Date(report.created_at), { addSuffix: true })}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleReportAction(report.id, 'dismiss')}
                            className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                          >
                            Dismiss
                          </button>
                          <button
                            onClick={() => handleReportAction(report.id, 'resolve')}
                            className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded hover:bg-green-200 dark:hover:bg-green-900/50"
                          >
                            Resolve
                          </button>
                        </div>
                      </div>

                      <div className="mt-3">
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">Reason for report:</h3>
                        <p className="mt-1 text-gray-700 dark:text-gray-300">{report.reason}</p>
                      </div>

                      <div className="mt-4 p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                        {report.content_type === 'topic' ? (
                          <div>
                            <h3 className="font-medium text-gray-900 dark:text-gray-100">
                              Topic: {(report.content as ForumTopic)?.title}
                            </h3>
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                              By {(report.content as ForumTopic)?.author?.full_name || (report.content as ForumTopic)?.author?.username} in {(report.content as ForumTopic)?.category?.name}
                            </p>
                            <div className="mt-3 flex space-x-2">
                              <Link
                                href={`/forums/${(report.content as ForumTopic)?.category?.slug}/${(report.content as ForumTopic)?.slug}`}
                                target="_blank"
                                className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50"
                              >
                                View Topic
                              </Link>
                              <button
                                onClick={() => handleTopicAction((report.content as ForumTopic)?.id, 'lock')}
                                className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded hover:bg-yellow-200 dark:hover:bg-yellow-900/50"
                              >
                                Lock Topic
                              </button>
                              <button
                                onClick={() => handleTopicAction((report.content as ForumTopic)?.id, 'delete')}
                                className="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-900/50"
                              >
                                Delete Topic
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div>
                            <h3 className="font-medium text-gray-900 dark:text-gray-100">
                              Post in: {(report.content as ForumPost)?.topic?.title}
                            </h3>
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                              By {(report.content as ForumPost)?.author?.full_name || (report.content as ForumPost)?.author?.username}
                            </p>
                            <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-750 rounded border border-gray-200 dark:border-gray-700">
                              <p className="text-gray-700 dark:text-gray-300 text-sm whitespace-pre-line">
                                {(report.content as ForumPost)?.content}
                              </p>
                            </div>
                            <div className="mt-3 flex space-x-2">
                              <Link
                                href={`/forums/${(report.content as ForumPost)?.topic?.category?.slug}/${(report.content as ForumPost)?.topic?.slug}#post-${(report.content as ForumPost)?.id}`}
                                target="_blank"
                                className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50"
                              >
                                View Post
                              </Link>
                              <button
                                onClick={() => handlePostAction((report.content as ForumPost)?.id, 'delete')}
                                className="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-900/50"
                              >
                                Delete Post
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )
            ) : activeTab === 'topics' ? (
              topics.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">No topics found</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-750">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Topic
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Author
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Category
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Posts
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Created
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {topics.map((topic) => (
                        <tr key={topic.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              <Link
                                href={`/forums/${topic.category?.slug}/${topic.slug}`}
                                target="_blank"
                                className="hover:text-nature-green"
                              >
                                {topic.title}
                              </Link>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {topic.author?.full_name || topic.author?.username}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {topic.category?.name}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {topic.post_count}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {formatDistanceToNow(new Date(topic.created_at), { addSuffix: true })}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex space-x-1">
                              {topic.is_pinned && (
                                <span className="px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full">
                                  Pinned
                                </span>
                              )}
                              {topic.is_locked && (
                                <span className="px-2 py-0.5 text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full">
                                  Locked
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <button
                                onClick={() => handleTopicAction(topic.id, topic.is_pinned ? 'unpin' : 'pin')}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                              >
                                {topic.is_pinned ? 'Unpin' : 'Pin'}
                              </button>
                              <button
                                onClick={() => handleTopicAction(topic.id, topic.is_locked ? 'unlock' : 'lock')}
                                className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"
                              >
                                {topic.is_locked ? 'Unlock' : 'Lock'}
                              </button>
                              <button
                                onClick={() => handleTopicAction(topic.id, 'delete')}
                                className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                              >
                                Delete
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )
            ) : (
              posts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">No posts found</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {posts.map((post) => (
                    <div key={post.id} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between">
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-gray-100">
                            <Link
                              href={`/forums/${post.topic?.category?.slug}/${post.topic?.slug}#post-${post.id}`}
                              target="_blank"
                              className="hover:text-nature-green"
                            >
                              Re: {post.topic?.title}
                            </Link>
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            By {post.author?.full_name || post.author?.username} • {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
                          </p>
                        </div>
                        <div>
                          <button
                            onClick={() => handlePostAction(post.id, 'delete')}
                            className="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-900/50"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                      <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-750 rounded border border-gray-200 dark:border-gray-700">
                        <p className="text-gray-700 dark:text-gray-300 text-sm whitespace-pre-line">
                          {post.content.length > 300 ? post.content.substring(0, 300) + '...' : post.content}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
