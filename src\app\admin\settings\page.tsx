'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type SiteSettings = {
  site_name: string;
  site_description: string;
  contact_email: string;
  allow_registrations: boolean;
  require_email_verification: boolean;
  maintenance_mode: boolean;
  theme_color: string;
  footer_text: string;
};

export default function AdminSettings() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [settings, setSettings] = useState<SiteSettings>({
    site_name: 'NatureHeals.info',
    site_description: 'A community-driven knowledge base for holistic healing, plant-based medicines, and natural treatments.',
    contact_email: '<EMAIL>',
    allow_registrations: true,
    require_email_verification: true,
    maintenance_mode: false,
    theme_color: '#2e7d32',
    footer_text: '© {year} NatureHeals.info. All rights reserved.'
  });

  useEffect(() => {
    async function checkAuth() {
      if (loading) return;
      
      if (!user) {
        router.push('/auth/signin');
        return;
      }
      
      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (error || !profile || profile.role !== 'admin') {
        router.push('/');
        return;
      }
      
      setIsAdmin(true);
      
      // Load settings
      try {
        const { data: settingsData, error: settingsError } = await supabase
          .from('site_settings')
          .select('*')
          .single();
        
        if (!settingsError && settingsData) {
          setSettings(settingsData);
        }
      } catch (err) {
        console.error('Error loading settings:', err);
      } finally {
        setIsLoading(false);
      }
    }
    
    checkAuth();
  }, [user, loading, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAdmin) return;
    
    setIsSaving(true);
    setSaveMessage('');
    
    try {
      const supabase = createClientComponentClient();
      
      // Check if settings record exists
      const { count, error: countError } = await supabase
        .from('site_settings')
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        throw countError;
      }
      
      let saveError;
      
      if (count && count > 0) {
        // Update existing settings
        const { error } = await supabase
          .from('site_settings')
          .update(settings)
          .eq('id', 1);
        
        saveError = error;
      } else {
        // Insert new settings
        const { error } = await supabase
          .from('site_settings')
          .insert({
            id: 1,
            ...settings
          });
        
        saveError = error;
      }
      
      if (saveError) {
        throw saveError;
      }
      
      setSaveMessage('Settings saved successfully!');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (err) {
      console.error('Error saving settings:', err);
      setSaveMessage('Error saving settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (!isAdmin || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Site Settings</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSave}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">General Settings</h2>
              
              <div className="mb-4">
                <label htmlFor="site_name" className="block text-sm font-medium text-gray-700 mb-1">
                  Site Name
                </label>
                <input
                  type="text"
                  id="site_name"
                  name="site_name"
                  value={settings.site_name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label htmlFor="site_description" className="block text-sm font-medium text-gray-700 mb-1">
                  Site Description
                </label>
                <textarea
                  id="site_description"
                  name="site_description"
                  value={settings.site_description}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Email
                </label>
                <input
                  type="email"
                  id="contact_email"
                  name="contact_email"
                  value={settings.contact_email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label htmlFor="theme_color" className="block text-sm font-medium text-gray-700 mb-1">
                  Theme Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    id="theme_color"
                    name="theme_color"
                    value={settings.theme_color}
                    onChange={handleChange}
                    className="h-10 w-10 border border-gray-300 rounded mr-2"
                  />
                  <input
                    type="text"
                    value={settings.theme_color}
                    onChange={handleChange}
                    name="theme_color"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  />
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-xl font-semibold mb-4">User Settings</h2>
              
              <div className="mb-4 flex items-center">
                <input
                  type="checkbox"
                  id="allow_registrations"
                  name="allow_registrations"
                  checked={settings.allow_registrations}
                  onChange={handleChange}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                />
                <label htmlFor="allow_registrations" className="ml-2 block text-sm text-gray-700">
                  Allow New User Registrations
                </label>
              </div>
              
              <div className="mb-4 flex items-center">
                <input
                  type="checkbox"
                  id="require_email_verification"
                  name="require_email_verification"
                  checked={settings.require_email_verification}
                  onChange={handleChange}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                />
                <label htmlFor="require_email_verification" className="ml-2 block text-sm text-gray-700">
                  Require Email Verification
                </label>
              </div>
              
              <div className="mb-4 flex items-center">
                <input
                  type="checkbox"
                  id="maintenance_mode"
                  name="maintenance_mode"
                  checked={settings.maintenance_mode}
                  onChange={handleChange}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                />
                <label htmlFor="maintenance_mode" className="ml-2 block text-sm text-gray-700">
                  Maintenance Mode
                </label>
              </div>
              
              <div className="mb-4">
                <label htmlFor="footer_text" className="block text-sm font-medium text-gray-700 mb-1">
                  Footer Text
                </label>
                <textarea
                  id="footer_text"
                  name="footer_text"
                  value={settings.footer_text}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Use {'{year}'} to insert the current year.
                </p>
              </div>
            </div>
          </div>
          
          <div className="mt-6 flex items-center justify-end">
            {saveMessage && (
              <p className={`mr-4 ${saveMessage.includes('Error') ? 'text-red-500' : 'text-green-500'}`}>
                {saveMessage}
              </p>
            )}
            <button
              type="submit"
              disabled={isSaving}
              className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isSaving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
