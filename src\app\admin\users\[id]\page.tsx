'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type UserActivity = {
  id: string;
  action: string;
  content_type: string;
  content_title: string;
  created_at: string;
};

export default function UserDetailPage({ params }: { params: { id: string } }) {
  // Unwrap params with React.use() to avoid warnings
  const unwrappedParams = React.use(params as any);
  const userId = unwrappedParams.id;
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [userData, setUserData] = useState<any>(null);
  const [userActivity, setUserActivity] = useState<UserActivity[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [newRole, setNewRole] = useState<string>('');

  useEffect(() => {
    async function checkAuth() {
      if (authLoading) return;

      if (!user) {
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (error || !profile || profile.role !== 'admin') {
        router.push('/');
        return;
      }

      setIsAdmin(true);
      setIsLoading(false);
      loadUserData();
    }

    checkAuth();
  }, [user, authLoading, router, userId]);

  async function loadUserData() {
    try {
      setIsLoading(true);

      const supabase = createClientComponentClient();

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        setError('Failed to load user: ' + profileError.message);
        return;
      }

      if (!profileData) {
        setError('User not found');
        return;
      }

      setUserData(profileData);
      setNewRole(profileData.role || 'user');

      // Generate mock activity data
      const mockActivity: UserActivity[] = [
        {
          id: '1',
          action: 'created',
          content_type: 'article',
          content_title: 'The Benefits of Ashwagandha',
          created_at: new Date(Date.now() - 2 * 3600000).toISOString()
        },
        {
          id: '2',
          action: 'commented',
          content_type: 'article',
          content_title: 'Acupuncture for Back Pain',
          created_at: new Date(Date.now() - 5 * 3600000).toISOString()
        },
        {
          id: '3',
          action: 'updated',
          content_type: 'profile',
          content_title: 'User Profile',
          created_at: new Date(Date.now() - 24 * 3600000).toISOString()
        },
        {
          id: '4',
          action: 'uploaded',
          content_type: 'media',
          content_title: 'Herbal Remedies Collection',
          created_at: new Date(Date.now() - 3 * 86400000).toISOString()
        }
      ];

      setUserActivity(mockActivity);
    } catch (err) {
      console.error('Error loading user data:', err);
      setError('An unexpected error occurred while loading user data');
    } finally {
      setIsLoading(false);
    }
  }

  const handleRoleChange = async () => {
    try {
      setIsUpdating(true);
      setError('');
      setSuccess('');

      const supabase = createClientComponentClient();

      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole })
        .eq('id', userId);

      if (error) {
        setError(`Failed to update user role: ${error.message}`);
        return;
      }

      setUserData({ ...userData, role: newRole });
      setSuccess(`User role updated to ${newRole}`);
    } catch (err) {
      console.error('Error updating user role:', err);
      setError('An unexpected error occurred while updating user role');
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'created': return 'bg-green-100 text-green-800';
      case 'updated': return 'bg-blue-100 text-blue-800';
      case 'commented': return 'bg-purple-100 text-purple-800';
      case 'uploaded': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isAdmin || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">User Details</h1>
          <button
            onClick={() => router.push('/admin/users')}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Back to Users
          </button>
        </div>

        <div className="bg-red-50 p-6 rounded-lg shadow-md text-red-700 border border-red-200">
          {error || 'User not found'}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">User Details</h1>
        <button
          onClick={() => router.push('/admin/users')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Users
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow-md md:col-span-2">
          <div className="flex items-center mb-6">
            <div className="h-20 w-20 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 text-2xl font-bold mr-4">
              {userData.username?.substring(0, 2).toUpperCase() || 'U'}
            </div>
            <div>
              <h2 className="text-2xl font-bold">{userData.full_name || 'Unnamed User'}</h2>
              <p className="text-gray-500">@{userData.username || 'user'}</p>
              <p className="text-gray-500">{`${userData.username || 'user'}@example.com`}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">User ID</h3>
              <p className="text-gray-900 break-all">{userData.id}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Joined</h3>
              <p className="text-gray-900">{formatDate(userData.created_at)}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Current Role</h3>
              <p className="text-gray-900">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  userData.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                  userData.role === 'moderator' ? 'bg-blue-100 text-blue-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {userData.role?.charAt(0).toUpperCase() + userData.role?.slice(1) || 'User'}
                </span>
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Status</h3>
              <p className="text-gray-900">
                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                  Active
                </span>
              </p>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-4">
            <h3 className="text-lg font-medium mb-4">Change User Role</h3>
            <div className="flex items-center">
              <select
                value={newRole}
                onChange={(e) => setNewRole(e.target.value)}
                className="mr-4 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="user">User</option>
                <option value="moderator">Moderator</option>
                <option value="admin">Admin</option>
              </select>
              <button
                onClick={handleRoleChange}
                disabled={isUpdating || newRole === userData.role}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isUpdating ? 'Updating...' : 'Update Role'}
              </button>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-medium mb-4">User Stats</h3>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-1">Articles</h4>
              <p className="text-2xl font-bold">{Math.floor(Math.random() * 10)}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-1">Comments</h4>
              <p className="text-2xl font-bold">{Math.floor(Math.random() * 20)}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-1">Media Uploads</h4>
              <p className="text-2xl font-bold">{Math.floor(Math.random() * 15)}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-1">Last Active</h4>
              <p className="text-gray-700">
                {formatDate(new Date(Date.now() - Math.floor(Math.random() * 86400000 * 7)).toISOString())}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-lg font-medium mb-4">Recent Activity</h3>

        {userActivity.length === 0 ? (
          <p className="text-gray-500">No recent activity found.</p>
        ) : (
          <div className="space-y-4">
            {userActivity.map(activity => (
              <div key={activity.id} className="flex items-start">
                <div className={`p-2 rounded-full mr-4 ${getActionColor(activity.action)}`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    {activity.action === 'created' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />}
                    {activity.action === 'updated' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />}
                    {activity.action === 'commented' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />}
                    {activity.action === 'uploaded' && <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />}
                  </svg>
                </div>
                <div>
                  <p className="text-sm">
                    <span className="font-medium">{activity.action.charAt(0).toUpperCase() + activity.action.slice(1)}</span>
                    {' '}{activity.content_type}{' '}
                    <span className="font-medium">{activity.content_title}</span>
                  </p>
                  <p className="text-xs text-gray-500 mt-1">{formatDate(activity.created_at)}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-6 flex justify-end space-x-4">
        <button
          onClick={() => router.push(`/admin/users/${userId}/edit`)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Edit User
        </button>
        <button
          onClick={() => {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
              alert('User deletion would be implemented here in a real application');
            }
          }}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Delete User
        </button>
      </div>
    </div>
  );
}
