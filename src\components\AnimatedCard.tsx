'use client';

import { motion, HTMLMotionProps } from 'framer-motion';
import React from 'react';

type AnimatedCardProps = React.PropsWithChildren<HTMLMotionProps<'div'>> & {
  className?: string;
};

export default function AnimatedCard({ children, className = '', ...props }: AnimatedCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.04, boxShadow: '0 8px 32px rgba(46,125,50,0.15)' }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 40 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      className={`rounded-xl bg-white shadow-md transition-all ${className}`}
      {...props}
    >
      {children}
    </motion.div>
  );
}