'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { formatDistanceToNow, format } from 'date-fns';
import Link from 'next/link';

interface UserActivity {
  id: string;
  user_id: string;
  activity_type: string;
  content_type: string;
  content_id: string;
  metadata: any;
  created_at: string;
  profiles: {
    username: string;
    full_name?: string;
    avatar_url?: string;
  };
  article_title?: string;
  article_slug?: string;
}

export default function ActivityPage() {
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 20;

  useEffect(() => {
    fetchActivities();
  }, [filter, page]);

  async function fetchActivities() {
    setLoading(true);
    setError(null);

    try {
      let query = supabase
        .from('user_activities')
        .select(`
          *,
          profiles:user_id (username, full_name, avatar_url)
        `)
        .order('created_at', { ascending: false })
        .range((page - 1) * pageSize, page * pageSize - 1);

      // Apply filter if not 'all'
      if (filter !== 'all') {
        query = query.eq('activity_type', filter);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      // Fetch additional data for activities related to articles
      const activitiesWithArticles = await Promise.all(
        data.map(async (activity) => {
          if (activity.content_type === 'article') {
            const { data: articleData } = await supabase
              .from('articles')
              .select('title, slug')
              .eq('id', activity.content_id)
              .single();

            return {
              ...activity,
              article_title: articleData?.title,
              article_slug: articleData?.slug
            };
          }
          return activity;
        })
      );

      if (page === 1) {
        setActivities(activitiesWithArticles);
      } else {
        setActivities(prev => [...prev, ...activitiesWithArticles]);
      }

      // Check if there are more activities to load
      setHasMore(data.length === pageSize);
    } catch (err: any) {
      console.error('Error fetching user activities:', err);
      setError(err.message || 'Failed to fetch activities');
    } finally {
      setLoading(false);
    }
  }

  const loadMore = () => {
    setPage(prev => prev + 1);
  };

  // Get activity description based on type
  const getActivityDescription = (activity: UserActivity) => {
    const userName = activity.profiles.full_name || activity.profiles.username;
    
    switch (activity.activity_type) {
      case 'article_create':
        return (
          <>
            <Link href={`/profile/${activity.profiles.username}`} className="font-medium text-nature-green hover:underline">
              {userName}
            </Link>{' '}
            published a new article{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'article_edit':
        return (
          <>
            <Link href={`/profile/${activity.profiles.username}`} className="font-medium text-nature-green hover:underline">
              {userName}
            </Link>{' '}
            updated{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'comment':
        return (
          <>
            <Link href={`/profile/${activity.profiles.username}`} className="font-medium text-nature-green hover:underline">
              {userName}
            </Link>{' '}
            commented on{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'bookmark':
        return (
          <>
            <Link href={`/profile/${activity.profiles.username}`} className="font-medium text-nature-green hover:underline">
              {userName}
            </Link>{' '}
            bookmarked{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}
          </>
        );
      case 'social_share':
        return (
          <>
            <Link href={`/profile/${activity.profiles.username}`} className="font-medium text-nature-green hover:underline">
              {userName}
            </Link>{' '}
            shared{' '}
            {activity.article_slug && activity.article_title ? (
              <Link href={`/wiki/${activity.article_slug}`} className="text-nature-green hover:underline">
                {activity.article_title}
              </Link>
            ) : (
              'an article'
            )}{' '}
            on {activity.metadata?.platform || 'social media'}
          </>
        );
      default:
        return (
          <>
            <Link href={`/profile/${activity.profiles.username}`} className="font-medium text-nature-green hover:underline">
              {userName}
            </Link>{' '}
            performed an activity
          </>
        );
    }
  };

  // Get icon based on activity type
  const getActivityIcon = (activity: UserActivity) => {
    switch (activity.activity_type) {
      case 'article_create':
        return (
          <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'article_edit':
        return (
          <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </div>
        );
      case 'comment':
        return (
          <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'bookmark':
        return (
          <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
            </svg>
          </div>
        );
      case 'social_share':
        return (
          <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600 dark:text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  // Group activities by date
  const groupActivitiesByDate = () => {
    const groups: { [key: string]: UserActivity[] } = {};
    
    activities.forEach(activity => {
      const date = new Date(activity.created_at);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      let groupKey;
      
      if (date.toDateString() === today.toDateString()) {
        groupKey = 'Today';
      } else if (date.toDateString() === yesterday.toDateString()) {
        groupKey = 'Yesterday';
      } else {
        groupKey = format(date, 'MMMM d, yyyy');
      }
      
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      
      groups[groupKey].push(activity);
    });
    
    return groups;
  };

  const activityGroups = groupActivitiesByDate();

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Community Activity</h1>

      {/* Filters */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-4 overflow-x-auto pb-2">
          <button
            onClick={() => {
              setFilter('all');
              setPage(1);
            }}
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              filter === 'all'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-600 dark:text-gray-400 hover:text-nature-green dark:hover:text-nature-green'
            }`}
          >
            All Activity
          </button>
          <button
            onClick={() => {
              setFilter('article_create');
              setPage(1);
            }}
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              filter === 'article_create'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-600 dark:text-gray-400 hover:text-nature-green dark:hover:text-nature-green'
            }`}
          >
            New Articles
          </button>
          <button
            onClick={() => {
              setFilter('article_edit');
              setPage(1);
            }}
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              filter === 'article_edit'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-600 dark:text-gray-400 hover:text-nature-green dark:hover:text-nature-green'
            }`}
          >
            Article Updates
          </button>
          <button
            onClick={() => {
              setFilter('comment');
              setPage(1);
            }}
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              filter === 'comment'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-600 dark:text-gray-400 hover:text-nature-green dark:hover:text-nature-green'
            }`}
          >
            Comments
          </button>
          <button
            onClick={() => {
              setFilter('bookmark');
              setPage(1);
            }}
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              filter === 'bookmark'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-600 dark:text-gray-400 hover:text-nature-green dark:hover:text-nature-green'
            }`}
          >
            Bookmarks
          </button>
          <button
            onClick={() => {
              setFilter('social_share');
              setPage(1);
            }}
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              filter === 'social_share'
                ? 'text-nature-green border-b-2 border-nature-green'
                : 'text-gray-600 dark:text-gray-400 hover:text-nature-green dark:hover:text-nature-green'
            }`}
          >
            Shares
          </button>
        </div>
      </div>

      {loading && page === 1 ? (
        <div className="flex justify-center items-center min-h-[40vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-md">
          {error}
        </div>
      ) : activities.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 dark:text-gray-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className="text-xl font-semibold mt-4">No activity found</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2 mb-6">
            {filter === 'all'
              ? 'There is no recent activity to display.'
              : `There is no recent activity of type "${filter}".`}
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
          >
            Back to Home
          </Link>
        </div>
      ) : (
        <div className="space-y-8">
          {Object.entries(activityGroups).map(([date, activities]) => (
            <div key={date}>
              <h2 className="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">{date}</h2>
              <div className="space-y-4">
                {activities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-start p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
                  >
                    <div className="mr-4">
                      {getActivityIcon(activity)}
                    </div>
                    <div className="flex-1">
                      <div className="text-gray-800 dark:text-gray-200">
                        {getActivityDescription(activity)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {hasMore && (
            <div className="text-center py-4">
              <button
                onClick={loadMore}
                disabled={loading}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors disabled:opacity-50"
              >
                {loading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
