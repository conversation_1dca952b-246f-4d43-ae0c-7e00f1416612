import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  try {
    const { name } = params;
    
    // Validate migration name to prevent directory traversal
    if (!/^[a-zA-Z0-9_]+$/.test(name)) {
      return NextResponse.json(
        { error: 'Invalid migration name' },
        { status: 400 }
      );
    }
    
    // Get the migration file path
    const filePath = path.join(process.cwd(), 'supabase', 'migrations', `${name}.sql`);
    
    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Migration not found' },
        { status: 404 }
      );
    }
    
    // Read the file
    const sql = fs.readFileSync(filePath, 'utf8');
    
    return NextResponse.json({ sql });
  } catch (error) {
    console.error('Error fetching migration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
