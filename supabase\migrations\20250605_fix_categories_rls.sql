-- Fix RLS for categories table

-- First, check if R<PERSON> is enabled on categories table
DO $$
DECLARE
  rls_enabled BOOLEAN;
BEGIN
  SELECT rowsecurity INTO rls_enabled
  FROM pg_tables
  WHERE schemaname = 'public' AND tablename = 'categories';
  
  IF rls_enabled THEN
    -- <PERSON><PERSON> is enabled, so create policies to allow operations
    
    -- Check if select policy exists
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'categories' 
      AND policyname = 'Categories are viewable by everyone'
    ) THEN
      -- Create select policy
      CREATE POLICY "Categories are viewable by everyone"
        ON public.categories FOR SELECT
        USING (true);
      RAISE NOTICE 'Created SELECT policy for categories table';
    END IF;
    
    -- Check if insert policy exists
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'categories' 
      AND policyname = 'Only admins can insert categories'
    ) THEN
      -- Create insert policy
      CREATE POLICY "Only admins can insert categories"
        ON public.categories FOR INSERT
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'admin'
          )
        );
      RAISE NOTICE 'Created INSERT policy for categories table';
    END IF;
    
    -- Check if update policy exists
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'categories' 
      AND policyname = 'Only admins can update categories'
    ) THEN
      -- Create update policy
      CREATE POLICY "Only admins can update categories"
        ON public.categories FOR UPDATE
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'admin'
          )
        );
      RAISE NOTICE 'Created UPDATE policy for categories table';
    END IF;
    
    -- Check if delete policy exists
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'categories' 
      AND policyname = 'Only admins can delete categories'
    ) THEN
      -- Create delete policy
      CREATE POLICY "Only admins can delete categories"
        ON public.categories FOR DELETE
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'admin'
          )
        );
      RAISE NOTICE 'Created DELETE policy for categories table';
    END IF;
    
    RAISE NOTICE 'RLS policies created for categories table';
  ELSE
    -- RLS is not enabled, so enable it and create policies
    ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Categories are viewable by everyone"
      ON public.categories FOR SELECT
      USING (true);
      
    CREATE POLICY "Only admins can insert categories"
      ON public.categories FOR INSERT
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    CREATE POLICY "Only admins can update categories"
      ON public.categories FOR UPDATE
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    CREATE POLICY "Only admins can delete categories"
      ON public.categories FOR DELETE
      USING (
        EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    RAISE NOTICE 'Enabled RLS and created policies for categories table';
  END IF;
END
$$;

-- Create a temporary policy to allow all operations (for testing)
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE schemaname = 'public' AND tablename = 'categories' AND rowsecurity = true
  ) THEN
    -- Check if the temporary policy exists
    IF NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'categories' 
      AND policyname = 'Allow all operations temporarily'
    ) THEN
      -- Create temporary policy
      CREATE POLICY "Allow all operations temporarily"
        ON public.categories
        USING (true)
        WITH CHECK (true);
      RAISE NOTICE 'Created temporary policy for categories table';
    END IF;
  END IF;
END
$$;
