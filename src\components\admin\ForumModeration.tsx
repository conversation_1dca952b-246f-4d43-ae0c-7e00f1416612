'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  editForumPost,
  deleteForumPost,
  setTopicLockStatus,
  setTopicPinStatus,
  moveTopicToCategory,
  deleteForumTopic
} from '@/lib/forums';
import { useAuth } from '@/components/AuthProvider';
import {
  FaEdit,
  FaTrash,
  FaLock,
  FaUnlock,
  FaThumbtack,
  FaExchangeAlt,
  FaExclamationTriangle,
  FaCheck,
  FaTimes
} from 'react-icons/fa';

interface ForumModerationProps {
  contentType: 'post' | 'topic';
  contentId: string;
  topicId?: string;
  currentContent?: string;
  isLocked?: boolean;
  isPinned?: boolean;
  categoryId?: string;
  onSuccess?: () => void;
}

export default function ForumModeration({
  contentType,
  contentId,
  topicId,
  currentContent,
  isLocked = false,
  isPinned = false,
  categoryId,
  onSuccess
}: ForumModerationProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(currentContent || '');
  const [moderationReason, setModerationReason] = useState('');
  const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
  const [isHardDelete, setIsHardDelete] = useState(false);
  const [isMoving, setIsMoving] = useState(false);
  const [newCategoryId, setNewCategoryId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Handle edit post
  const handleEditPost = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      await editForumPost({
        postId: contentId,
        content: editedContent,
        moderatorId: user.id,
        reason: moderationReason || 'Content edited by moderator'
      });

      setSuccess('Post edited successfully');
      setIsEditing(false);
      if (onSuccess) onSuccess();
    } catch (err: any) {
      setError(err.message || 'Failed to edit post');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete post/topic
  const handleDelete = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      if (contentType === 'post') {
        await deleteForumPost({
          postId: contentId,
          moderatorId: user.id,
          reason: moderationReason || 'Content removed by moderator',
          hardDelete: isHardDelete
        });
      } else {
        await deleteForumTopic({
          topicId: contentId,
          moderatorId: user.id,
          reason: moderationReason || 'Topic removed by moderator',
          hardDelete: isHardDelete
        });
      }

      setSuccess(`${contentType === 'post' ? 'Post' : 'Topic'} ${isHardDelete ? 'permanently deleted' : 'removed'} successfully`);
      setIsConfirmingDelete(false);
      if (onSuccess) onSuccess();
    } catch (err: any) {
      setError(err.message || `Failed to delete ${contentType}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle lock/unlock topic
  const handleLockToggle = async () => {
    if (!user || contentType !== 'topic') return;

    setIsLoading(true);
    setError(null);

    try {
      console.log(`Toggling lock status for topic ${contentId}, current status: ${isLocked}`);

      // Use the direct action function
      const { directLockTopic } = await import('@/lib/direct-forum-actions');
      const result = await directLockTopic({
        topicId: contentId,
        isLocked: !isLocked, // Toggle the current state
        moderatorId: user.id,
        reason: moderationReason || `Topic ${isLocked ? 'unlocked' : 'locked'} by moderator`
      });

      if (!result.success) {
        console.error(`Error ${isLocked ? 'unlocking' : 'locking'} topic:`, result.error);
        setError(result.error || `Failed to ${isLocked ? 'unlock' : 'lock'} topic`);
        return;
      }

      setSuccess(`Topic ${isLocked ? 'unlocked' : 'locked'} successfully`);

      // Force a hard refresh of the page to show the updated state
      if (onSuccess) {
        // Wait a moment to ensure the database update completes
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } catch (err: any) {
      console.error('Exception in handleLockToggle:', err);
      setError(err.message || 'Failed to update topic lock status');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle pin/unpin topic
  const handlePinToggle = async () => {
    if (!user || contentType !== 'topic') return;

    setIsLoading(true);
    setError(null);

    try {
      console.log(`Toggling pin status for topic ${contentId}, current status: ${isPinned}`);

      // Use the direct action function
      const { directPinTopic } = await import('@/lib/direct-forum-actions');
      const result = await directPinTopic({
        topicId: contentId,
        isPinned: !isPinned, // Toggle the current state
        moderatorId: user.id,
        reason: moderationReason || `Topic ${isPinned ? 'unpinned' : 'pinned'} by moderator`
      });

      if (!result.success) {
        console.error(`Error ${isPinned ? 'unpinning' : 'pinning'} topic:`, result.error);
        setError(result.error || `Failed to ${isPinned ? 'unpin' : 'pin'} topic`);
        return;
      }

      setSuccess(`Topic ${isPinned ? 'unpinned' : 'pinned'} successfully`);

      // Force a hard refresh of the page to show the updated state
      if (onSuccess) {
        // Wait a moment to ensure the database update completes
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } catch (err: any) {
      console.error('Exception in handlePinToggle:', err);
      setError(err.message || 'Failed to update topic pin status');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle move topic
  const handleMoveTopic = async () => {
    if (!user || contentType !== 'topic' || !newCategoryId) return;

    setIsLoading(true);
    setError(null);

    try {
      await moveTopicToCategory({
        topicId: contentId,
        newCategoryId,
        moderatorId: user.id,
        reason: moderationReason || 'Topic moved to another category'
      });

      setSuccess('Topic moved successfully');
      setIsMoving(false);
      if (onSuccess) onSuccess();
    } catch (err: any) {
      setError(err.message || 'Failed to move topic');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-xl font-bold mb-4">Moderation Actions</h2>

      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md flex items-center">
          <FaExclamationTriangle className="mr-2" />
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md flex items-center">
          <FaCheck className="mr-2" />
          {success}
        </div>
      )}

      {/* Moderation reason input */}
      <div className="mb-4">
        <label htmlFor="moderationReason" className="block text-sm font-medium text-gray-700 mb-1">
          Moderation Reason (optional)
        </label>
        <input
          type="text"
          id="moderationReason"
          value={moderationReason}
          onChange={(e) => setModerationReason(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
          placeholder="Reason for moderation action"
        />
      </div>

      {/* Edit post form */}
      {isEditing && contentType === 'post' && (
        <div className="mb-4">
          <label htmlFor="editedContent" className="block text-sm font-medium text-gray-700 mb-1">
            Edit Content
          </label>
          <textarea
            id="editedContent"
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
            rows={6}
          />
          <div className="mt-2 flex justify-end space-x-2">
            <button
              onClick={() => setIsEditing(false)}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleEditPost}
              className="px-3 py-1 bg-nature-green text-white rounded-md hover:bg-green-700"
              disabled={isLoading}
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      )}

      {/* Delete confirmation */}
      {isConfirmingDelete && (
        <div className="mb-4 p-4 border border-red-200 bg-red-50 rounded-md">
          <p className="text-red-700 mb-2">
            Are you sure you want to {isHardDelete ? 'permanently delete' : 'remove'} this {contentType}?
            {!isHardDelete && ' (Soft delete will mark it as removed but keep it in the database)'}
          </p>
          <div className="flex items-center mb-2">
            <input
              type="checkbox"
              id="hardDelete"
              checked={isHardDelete}
              onChange={(e) => setIsHardDelete(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="hardDelete" className="text-sm text-red-700">
              Permanently delete (cannot be undone)
            </label>
          </div>
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setIsConfirmingDelete(false)}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700"
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Confirm Delete'}
            </button>
          </div>
        </div>
      )}

      {/* Move topic form */}
      {isMoving && contentType === 'topic' && (
        <div className="mb-4 p-4 border border-blue-200 bg-blue-50 rounded-md">
          <p className="text-blue-700 mb-2">
            Select a new category for this topic:
          </p>
          <select
            value={newCategoryId}
            onChange={(e) => setNewCategoryId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green mb-2"
          >
            <option value="">Select a category</option>
            {/* This would be populated with categories from the database */}
            <option value="category1">General Discussion</option>
            <option value="category2">Herbal Remedies</option>
            <option value="category3">Nutrition & Diet</option>
            <option value="category4">Mind-Body Practices</option>
          </select>
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setIsMoving(false)}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleMoveTopic}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              disabled={isLoading || !newCategoryId}
            >
              {isLoading ? 'Moving...' : 'Move Topic'}
            </button>
          </div>
        </div>
      )}

      {/* Action buttons */}
      <div className="flex flex-wrap gap-2">
        {contentType === 'post' && (
          <button
            onClick={() => setIsEditing(true)}
            className="px-3 py-2 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 flex items-center"
            disabled={isLoading}
          >
            <FaEdit className="mr-1" />
            Edit
          </button>
        )}

        <button
          onClick={() => setIsConfirmingDelete(true)}
          className="px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 flex items-center"
          disabled={isLoading}
        >
          <FaTrash className="mr-1" />
          Delete
        </button>

        {contentType === 'topic' && (
          <>
            <button
              onClick={handleLockToggle}
              className={`px-3 py-2 ${isLocked ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'} rounded-md hover:bg-opacity-80 flex items-center`}
              disabled={isLoading}
            >
              {isLocked ? <FaUnlock className="mr-1" /> : <FaLock className="mr-1" />}
              {isLocked ? 'Unlock' : 'Lock'}
            </button>

            <button
              onClick={handlePinToggle}
              className={`px-3 py-2 ${isPinned ? 'bg-gray-100 text-gray-700' : 'bg-blue-100 text-blue-700'} rounded-md hover:bg-opacity-80 flex items-center`}
              disabled={isLoading}
            >
              <FaThumbtack className="mr-1" />
              {isPinned ? 'Unpin' : 'Pin'}
            </button>

            <button
              onClick={() => setIsMoving(true)}
              className="px-3 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 flex items-center"
              disabled={isLoading}
            >
              <FaExchangeAlt className="mr-1" />
              Move
            </button>
          </>
        )}
      </div>
    </div>
  );
}
