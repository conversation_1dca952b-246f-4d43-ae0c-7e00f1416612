{"name": "natureheals", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "apply-migrations": "node scripts/apply-new-features.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@lottiefiles/react-lottie-player": "^3.6.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/typography": "^0.5.16", "axios": "^1.8.4", "chart.js": "^4.4.9", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "framer-motion": "^12.6.5", "lottie-react": "^2.4.1", "next": "15.3.0", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "unified": "^11.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.17.30", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-google-recaptcha": "^2.1.9", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.0", "netlify-plugin-cache": "^1.0.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "ts-node": "^10.9.2", "typescript": "^5"}}