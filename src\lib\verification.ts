import { createClient } from '@/lib/supabase';
import { ExpertVerification } from '@/lib/profile-types';

// Get verification request by user ID
export async function getVerificationRequestByUserId(userId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('expert_verifications')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(1)
    .single();
    
  if (error && error.code !== 'PGRST116') { // PGRST116 is "No rows returned"
    console.error('Error fetching verification request:', error);
    throw error;
  }
  
  return data as ExpertVerification | null;
}

// Create a new verification request
export async function createVerificationRequest({
  userId,
  credentials,
  specialty,
  documents
}: {
  userId: string;
  credentials: string;
  specialty: string;
  documents: string[];
}) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('expert_verifications')
    .insert({
      user_id: userId,
      credentials,
      specialty,
      verification_documents: documents,
      status: 'pending'
    })
    .select()
    .single();
    
  if (error) {
    console.error('Error creating verification request:', error);
    throw error;
  }
  
  return data as ExpertVerification;
}

// Get all verification requests (for admin)
export async function getAllVerificationRequests({
  status = 'pending',
  limit = 10,
  offset = 0
}: {
  status?: 'pending' | 'approved' | 'rejected' | 'all';
  limit?: number;
  offset?: number;
}) {
  const supabase = createClient();
  
  let query = supabase
    .from('expert_verifications')
    .select(`
      *,
      user:user_id (
        id,
        username,
        full_name,
        avatar_url,
        expertise_level
      ),
      reviewer:reviewer_id (
        username,
        full_name
      )
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);
    
  if (status !== 'all') {
    query = query.eq('status', status);
  }
  
  const { data, error, count } = await query;
  
  if (error) {
    console.error('Error fetching verification requests:', error);
    throw error;
  }
  
  return { data, count };
}

// Update verification request status (for admin)
export async function updateVerificationStatus({
  requestId,
  status,
  reviewerId,
  reviewNotes
}: {
  requestId: string;
  status: 'approved' | 'rejected';
  reviewerId: string;
  reviewNotes?: string;
}) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('expert_verifications')
    .update({
      status,
      reviewer_id: reviewerId,
      review_notes: reviewNotes,
      updated_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single();
    
  if (error) {
    console.error('Error updating verification status:', error);
    throw error;
  }
  
  // If approved, update the user's profile
  if (status === 'approved') {
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        is_verified_expert: true,
        badges: supabase.rpc('array_append_unique', { 
          arr: 'badges', 
          el: 'verified' 
        })
      })
      .eq('id', data.user_id);
      
    if (profileError) {
      console.error('Error updating user profile:', profileError);
      throw profileError;
    }
    
    // Add achievement
    const { error: achievementError } = await supabase
      .from('user_achievements')
      .insert({
        user_id: data.user_id,
        achievement_type: 'expert_verification',
        achievement_data: {
          specialty: data.specialty
        }
      });
      
    if (achievementError) {
      console.error('Error creating achievement:', achievementError);
    }
  }
  
  return data as ExpertVerification;
}

// Upload verification document
export async function uploadVerificationDocument(file: File, userId: string) {
  const supabase = createClient();
  
  const fileExt = file.name.split('.').pop();
  const fileName = `${userId}-${Date.now()}.${fileExt}`;
  const filePath = `verification-documents/${fileName}`;
  
  const { data, error } = await supabase
    .storage
    .from('user-uploads')
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: false
    });
    
  if (error) {
    console.error('Error uploading document:', error);
    throw error;
  }
  
  // Get public URL
  const { data: { publicUrl } } = supabase
    .storage
    .from('user-uploads')
    .getPublicUrl(filePath);
    
  return publicUrl;
}
