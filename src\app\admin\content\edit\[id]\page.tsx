'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useAuth } from '@/components/AuthProvider';
import ArticleImageManager from '@/components/ArticleImageManager';
import StorageSetup from '@/components/StorageSetup';

export default function EditContentPage() {
  const router = useRouter();
  const params = useParams();
  const { user, loading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  // Article form state
  const [articleTitle, setArticleTitle] = useState('');
  const [articleContent, setArticleContent] = useState('');
  const [articleExcerpt, setArticleExcerpt] = useState('');
  const [articleCategory, setArticleCategory] = useState('');
  const [articleTags, setArticleTags] = useState('');
  const [articleStatus, setArticleStatus] = useState<'draft' | 'pending_review' | 'published' | 'rejected'>('draft');
  const [articleSlug, setArticleSlug] = useState('');

  // Categories for dropdown
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);

  // Original article data for comparison
  const [originalArticle, setOriginalArticle] = useState<any>(null);

  useEffect(() => {
    async function checkAuth() {
      if (loading) return;

      if (!user) {
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        router.push('/');
        return;
      }

      setIsAdmin(true);

      // Load categories
      await fetchCategories();

      // Load article data
      await fetchArticle();

      setIsLoading(false);
    }

    checkAuth();
  }, [user, loading, router, params.id]);

  const fetchCategories = async () => {
    try {
      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .order('name');

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      setCategories(data || []);
    } catch (err) {
      console.error('Error in fetchCategories:', err);
    }
  };

  const fetchArticle = async () => {
    try {
      if (!params.id) return;

      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('articles')
        .select(`
          *,
          profiles (username, full_name),
          categories (id, name)
        `)
        .eq('id', params.id)
        .single();

      if (error) {
        setError(`Error loading article: ${error.message}`);
        return;
      }

      if (!data) {
        setError('Article not found');
        return;
      }

      // Store original article data
      setOriginalArticle(data);

      // Set form values
      setArticleTitle(data.title || '');
      setArticleContent(data.content || '');
      setArticleExcerpt(data.excerpt || '');
      setArticleCategory(data.category_id || '');
      setArticleStatus(data.status || 'draft');
      setArticleSlug(data.slug || '');

      // Fetch tags for this article
      const { data: tagData, error: tagError } = await supabase
        .from('article_tags')
        .select(`
          tags (name)
        `)
        .eq('article_id', params.id);

      if (!tagError && tagData) {
        const tagNames = tagData.map(t => t.tags?.name).filter(Boolean);
        setArticleTags(tagNames.join(', '));
      }

    } catch (err) {
      console.error('Error fetching article:', err);
      setError('An unexpected error occurred while loading the article');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!articleTitle || !articleContent || !articleCategory) {
      setMessage('Please fill in all required fields');
      return;
    }

    setIsSaving(true);
    setMessage('');
    setError('');

    try {
      const supabase = createClientComponentClient();

      // Generate a slug if it was changed or is empty
      let slug = articleSlug;
      if (!slug || articleTitle !== originalArticle?.title) {
        slug = articleTitle
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '') +
          (originalArticle?.slug ? '' : `-${Date.now()}`); // Add timestamp only for new slugs
      }

      // Generate excerpt if empty
      const excerpt = articleExcerpt || articleContent.substring(0, 150) + '...';

      // Update article
      const { data, error } = await supabase
        .from('articles')
        .update({
          title: articleTitle,
          slug,
          content: articleContent,
          excerpt,
          category_id: articleCategory,
          status: articleStatus,
          updated_at: new Date().toISOString(),
          published_at: articleStatus === 'published' && !originalArticle?.published_at
            ? new Date().toISOString()
            : originalArticle?.published_at
        })
        .eq('id', params.id)
        .select();

      if (error) {
        throw error;
      }

      // Process tags if they've changed
      if (articleTags !== originalArticle?.tags) {
        // Parse tags
        const tagsArray = articleTags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);

        // First, remove all existing tags for this article
        await supabase
          .from('article_tags')
          .delete()
          .eq('article_id', params.id);

        // Then add the new tags
        for (const tagName of tagsArray) {
          // Check if tag exists
          let { data: existingTag } = await supabase
            .from('tags')
            .select('id')
            .eq('name', tagName)
            .single();

          let tagId;

          if (!existingTag) {
            // Create new tag
            const slug = tagName
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '');

            const { data: newTag } = await supabase
              .from('tags')
              .insert({
                name: tagName,
                slug
              })
              .select()
              .single();

            tagId = newTag?.id;
          } else {
            tagId = existingTag.id;
          }

          if (tagId) {
            // Add tag to article
            await supabase
              .from('article_tags')
              .insert({
                article_id: params.id,
                tag_id: tagId
              });
          }
        }
      }

      // Log activity
      try {
        await supabase
          .from('contributions')
          .insert({
            user_id: user?.id,
            content_type: 'article',
            content_id: params.id,
            contribution_type: 'edit'
          });
      } catch (logError) {
        console.warn('Could not log contribution, but article was updated:', logError);
      }

      setMessage('Article updated successfully!');

      // Refresh original article data
      await fetchArticle();
    } catch (err: any) {
      console.error('Error updating article:', err);

      if (err.code === '23505') {
        setError('Error: An article with this slug already exists. Please modify the title to create a unique slug.');
      } else if (err.message) {
        setError(`Error updating article: ${err.message}`);
      } else {
        setError('Error updating article. Please try again.');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const generateExcerpt = () => {
    if (!articleContent) return;

    const excerpt = articleContent.substring(0, 150) + (articleContent.length > 150 ? '...' : '');
    setArticleExcerpt(excerpt);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        <span className="ml-3 text-lg">Loading article...</span>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Edit Article</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/admin/content/manage')}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Back to Content
          </button>
          {originalArticle?.status === 'published' && originalArticle?.slug && (
            <a
              href={`/wiki/${originalArticle.slug}`}
              target="_blank"
              rel="noopener noreferrer"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              View Published
            </a>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      {message && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          {message}
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md">
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="article_title" className="block text-sm font-medium text-gray-700 mb-1">
              Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="article_title"
              value={articleTitle}
              onChange={(e) => setArticleTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="article_slug" className="block text-sm font-medium text-gray-700 mb-1">
              Slug
            </label>
            <input
              type="text"
              id="article_slug"
              value={articleSlug}
              onChange={(e) => setArticleSlug(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              placeholder="Leave blank to generate from title"
            />
            <p className="text-xs text-gray-500 mt-1">
              URL-friendly version of the title. Leave blank to auto-generate.
            </p>
          </div>

          <div className="mb-4">
            <label htmlFor="article_content" className="block text-sm font-medium text-gray-700 mb-1">
              Content <span className="text-red-500">*</span>
            </label>
            <textarea
              id="article_content"
              value={articleContent}
              onChange={(e) => setArticleContent(e.target.value)}
              rows={15}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              required
            />
            <div className="text-xs text-gray-500 mt-1">
              <p className="mb-1">Markdown formatting is supported:</p>
              <ul className="list-disc list-inside ml-2 space-y-1">
                <li><code># Heading 1</code>, <code>## Heading 2</code>, etc. for headings</li>
                <li><code>**bold**</code> for <strong>bold text</strong></li>
                <li><code>*italic*</code> for <em>italic text</em></li>
                <li><code>[link text](https://example.com)</code> for <a href="#" className="text-nature-green">links</a></li>
                <li><code>![alt text](image-url)</code> for images (use the image manager below)</li>
                <li><code>- item</code> for bullet lists</li>
                <li><code>1. item</code> for numbered lists</li>
              </ul>
            </div>
          </div>

          <div className="mb-4">
            <label htmlFor="article_excerpt" className="block text-sm font-medium text-gray-700 mb-1">
              Excerpt
            </label>
            <div className="flex space-x-2">
              <textarea
                id="article_excerpt"
                value={articleExcerpt}
                onChange={(e) => setArticleExcerpt(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              />
              <button
                type="button"
                onClick={generateExcerpt}
                className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              >
                Generate
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              A short summary of the article. Leave blank to auto-generate from content.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="article_category" className="block text-sm font-medium text-gray-700 mb-1">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                id="article_category"
                value={articleCategory}
                onChange={(e) => setArticleCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="article_tags" className="block text-sm font-medium text-gray-700 mb-1">
                Tags
              </label>
              <input
                type="text"
                id="article_tags"
                value={articleTags}
                onChange={(e) => setArticleTags(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Enter tags separated by commas"
              />
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <div className="flex space-x-4">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="status_draft"
                  name="article_status"
                  value="draft"
                  checked={articleStatus === 'draft'}
                  onChange={() => setArticleStatus('draft')}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                />
                <label htmlFor="status_draft" className="ml-2 block text-sm text-gray-700">
                  Draft
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="status_pending"
                  name="article_status"
                  value="pending_review"
                  checked={articleStatus === 'pending_review'}
                  onChange={() => setArticleStatus('pending_review')}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                />
                <label htmlFor="status_pending" className="ml-2 block text-sm text-gray-700">
                  Pending Review
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="status_published"
                  name="article_status"
                  value="published"
                  checked={articleStatus === 'published'}
                  onChange={() => setArticleStatus('published')}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                />
                <label htmlFor="status_published" className="ml-2 block text-sm text-gray-700">
                  Published
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="status_rejected"
                  name="article_status"
                  value="rejected"
                  checked={articleStatus === 'rejected'}
                  onChange={() => setArticleStatus('rejected')}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                />
                <label htmlFor="status_rejected" className="ml-2 block text-sm text-gray-700">
                  Rejected
                </label>
              </div>
            </div>
          </div>

          {/* Article Images Section */}
          <div className="mb-6 border-t border-gray-200 pt-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">Article Images</h2>
            <p className="text-gray-600 mb-4">
              Upload and manage images for this article. You can then insert them into your content using markdown.
            </p>

            {/* Storage Setup Check */}
            <div className="mb-4">
              <StorageSetup />
            </div>

            {params.id && <ArticleImageManager articleId={params.id as string} />}
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSaving}
              className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
