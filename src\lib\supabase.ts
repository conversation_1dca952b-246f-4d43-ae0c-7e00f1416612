import { createClient as createSupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string;

// Export a pre-initialized client for direct use
export const supabase = createSupabaseClient(supabaseUrl, supabaseAnonKey);

// Export a function to create a new client when needed
export function createClient() {
  return createSupabaseClient(supabaseUrl, supabaseAnonKey);
}
