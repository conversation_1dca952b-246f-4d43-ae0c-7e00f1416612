'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getReportedContent, updateReportStatus } from '@/lib/forums';
import { format, formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import {
  FaExclamationTriangle, FaCheck, FaTimes, FaEye,
  FaFilter, FaArrowLeft, FaTrash, FaEdit
} from 'react-icons/fa';

export default function ModerationQueuePage() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [reportedContent, setReportedContent] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalReports, setTotalReports] = useState(0);
  const [filters, setFilters] = useState({
    status: 'pending',
    contentType: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [modalAction, setModalAction] = useState(null);
  const [moderatorNotes, setModeratorNotes] = useState('');
  const [selectedReports, setSelectedReports] = useState([]);
  const [bulkActionModalOpen, setBulkActionModalOpen] = useState(false);
  const [bulkAction, setBulkAction] = useState(null);
  const itemsPerPage = 10;

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        // Redirect to unauthorized page if not admin/moderator
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Load reported content
      loadReportedContent();
    }

    checkAuth();
  }, [router, user, loading, page, filters]);

  async function loadReportedContent() {
    try {
      setIsLoading(true);

      const { data, count } = await getReportedContent({
        limit: itemsPerPage,
        offset: (page - 1) * itemsPerPage,
        status: filters.status,
        contentType: filters.contentType
      });

      setReportedContent(data || []);
      setTotalReports(count || 0);
      setTotalPages(Math.ceil((count || 0) / itemsPerPage));
    } catch (err) {
      console.error('Error loading reported content:', err);
      setError('Failed to load moderation queue');
    } finally {
      setIsLoading(false);
    }
  }

  function handleFilterChange(e) {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  }

  function applyFilters() {
    setPage(1); // Reset to first page when applying filters
    loadReportedContent();
  }

  function resetFilters() {
    setFilters({
      status: 'pending',
      contentType: 'all'
    });
    setPage(1);
    loadReportedContent();
  }

  function openResolveModal(report) {
    setModalData(report);
    setModalAction('resolve');
    setModeratorNotes('');
  }

  function openDismissModal(report) {
    setModalData(report);
    setModalAction('dismiss');
    setModeratorNotes('');
  }

  function closeModal() {
    setModalData(null);
    setModalAction(null);
    setModeratorNotes('');
  }

  async function handleResolveReport() {
    if (!modalData || !modalAction) return;

    try {
      setIsLoading(true);

      await updateReportStatus({
        reportId: modalData.id,
        status: modalAction === 'resolve' ? 'resolved' : 'dismissed',
        moderatorId: user.id,
        notes: moderatorNotes
      });

      setSuccess(`Report ${modalAction === 'resolve' ? 'resolved' : 'dismissed'} successfully`);
      closeModal();
      loadReportedContent();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Error updating report status:', err);
      setError(err.message || `Failed to ${modalAction} report`);
    } finally {
      setIsLoading(false);
    }
  }

  async function handleBulkAction() {
    if (!bulkAction || selectedReports.length === 0) return;

    try {
      setIsLoading(true);

      // Process each selected report
      const promises = selectedReports.map(reportId => {
        return updateReportStatus({
          reportId,
          status: bulkAction === 'resolve' ? 'resolved' : 'dismissed',
          moderatorId: user.id,
          notes: moderatorNotes
        });
      });

      await Promise.all(promises);

      setSuccess(`${selectedReports.length} reports ${bulkAction === 'resolve' ? 'resolved' : 'dismissed'} successfully`);
      setBulkActionModalOpen(false);
      setSelectedReports([]);
      setBulkAction(null);
      setModeratorNotes('');
      loadReportedContent();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Error processing bulk action:', err);
      setError(err.message || `Failed to process bulk action`);
    } finally {
      setIsLoading(false);
    }
  }

  // Helper function to get content link
  const getContentLink = (report) => {
    if (!report || !report.content) return '#';

    if (report.content_type === 'post') {
      return `/forums/${report.content.topic?.category?.slug || 'category'}/${report.content.topic?.slug || 'topic'}#post-${report.content_id}`;
    } else if (report.content_type === 'topic') {
      return `/forums/${report.content.category?.slug || 'category'}/${report.content.slug || 'topic'}`;
    }

    return '#';
  };

  // Helper function to get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading moderation queue...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <button
            onClick={() => router.push('/admin')}
            className="mr-4 text-nature-green hover:underline flex items-center"
          >
            <FaArrowLeft className="mr-1" />
            Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold">Moderation Queue</h1>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-lg border border-green-100">
          <p className="font-medium">Success</p>
          <p>{success}</p>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Filters</h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-nature-green hover:underline flex items-center"
          >
            <FaFilter className="mr-1" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="pending">Pending</option>
                <option value="resolved">Resolved</option>
                <option value="dismissed">Dismissed</option>
                <option value="all">All</option>
              </select>
            </div>

            <div>
              <label htmlFor="contentType" className="block text-sm font-medium text-gray-700 mb-1">
                Content Type
              </label>
              <select
                id="contentType"
                name="contentType"
                value={filters.contentType}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="all">All Types</option>
                <option value="post">Posts</option>
                <option value="topic">Topics</option>
              </select>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-2">
          <button
            onClick={resetFilters}
            className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>
          <button
            onClick={applyFilters}
            className="px-3 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>

      {/* Stats and Bulk Actions */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div>
              <span className="text-gray-500">Total Reports:</span>
              <span className="ml-2 font-semibold">{totalReports}</span>
            </div>
            <div>
              <span className="text-gray-500">Page:</span>
              <span className="ml-2 font-semibold">{page} of {totalPages}</span>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedReports.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">{selectedReports.length} selected</span>
              <div className="relative inline-block">
                <select
                  className="appearance-none bg-nature-green text-white px-4 py-2 pr-8 rounded-md cursor-pointer focus:outline-none focus:ring-2 focus:ring-nature-green focus:ring-opacity-50"
                  onChange={(e) => {
                    if (e.target.value) {
                      setBulkAction(e.target.value);
                      setBulkActionModalOpen(true);
                    }
                  }}
                  value=""
                >
                  <option value="" disabled>Bulk Actions</option>
                  <option value="resolve">Resolve Selected</option>
                  <option value="dismiss">Dismiss Selected</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
              <button
                onClick={() => setSelectedReports([])}
                className="text-sm text-gray-600 hover:text-gray-800 underline"
              >
                Clear
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Reported Content */}
      {reportedContent.length > 0 ? (
        <div className="space-y-6">
          {reportedContent.map(report => (
            <div key={report.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-4 bg-gray-50 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {report.status === 'pending' && (
                      <div className="mr-2">
                        <input
                          type="checkbox"
                          id={`report-${report.id}`}
                          checked={selectedReports.includes(report.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedReports([...selectedReports, report.id]);
                            } else {
                              setSelectedReports(selectedReports.filter(id => id !== report.id));
                            }
                          }}
                          className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                        />
                      </div>
                    )}
                    <FaExclamationTriangle className="text-yellow-500 mr-2" />
                    <div>
                      <h3 className="font-medium">
                        Report #{report.id.substring(0, 8)} -
                        <span className="capitalize ml-1">{report.content_type}</span>
                      </h3>
                      <p className="text-sm text-gray-500">
                        Reported {formatDistanceToNow(new Date(report.created_at), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(report.status)}`}>
                    {report.status}
                  </span>
                </div>
              </div>

              <div className="p-4">
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Reported By</h4>
                  <div className="flex items-center">
                    {report.reporter?.avatar_url ? (
                      <img
                        src={report.reporter.avatar_url}
                        alt={report.reporter.username}
                        className="w-8 h-8 rounded-full mr-2"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                        <span className="text-gray-500 text-xs">{report.reporter?.username?.charAt(0) || '?'}</span>
                      </div>
                    )}
                    <span>{report.reporter?.username || 'Unknown'}</span>
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Reason</h4>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <p className="text-sm">{report.reason}</p>
                  </div>
                </div>

                {report.content && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Reported Content</h4>
                    <div className="bg-gray-50 p-3 rounded-md">
                      {report.content_type === 'post' ? (
                        <>
                          <p className="text-xs text-gray-500 mb-1">
                            Post by {report.content.author?.username || 'Unknown'} in topic "{report.content.topic?.title || 'Unknown'}"
                          </p>
                          <p className="text-sm line-clamp-3">{report.content.content}</p>
                        </>
                      ) : (
                        <>
                          <p className="text-xs text-gray-500 mb-1">
                            Topic by {report.content.author?.username || 'Unknown'} in {report.content.category?.name || 'Unknown'}
                          </p>
                          <p className="text-sm font-medium">{report.content.title}</p>
                        </>
                      )}
                    </div>
                  </div>
                )}

                {report.status === 'pending' ? (
                  <div className="flex justify-end space-x-2">
                    <Link
                      href={getContentLink(report)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors flex items-center"
                      target="_blank"
                    >
                      <FaEye className="mr-1" />
                      View
                    </Link>
                    <button
                      onClick={() => openDismissModal(report)}
                      className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center"
                    >
                      <FaTimes className="mr-1" />
                      Dismiss
                    </button>
                    <button
                      onClick={() => openResolveModal(report)}
                      className="px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors flex items-center"
                    >
                      <FaCheck className="mr-1" />
                      Resolve
                    </button>
                  </div>
                ) : (
                  <div className="flex justify-end space-x-2">
                    <Link
                      href={getContentLink(report)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors flex items-center"
                      target="_blank"
                    >
                      <FaEye className="mr-1" />
                      View Content
                    </Link>
                    {report.moderator_notes && (
                      <div className="text-sm text-gray-500">
                        <span className="font-medium">Moderator Notes:</span> {report.moderator_notes}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <FaExclamationTriangle className="text-yellow-500 text-4xl mx-auto mb-4" />
          <h3 className="text-xl font-medium mb-2">No Reports Found</h3>
          <p className="text-gray-500">
            {filters.status === 'pending'
              ? 'There are no pending reports in the moderation queue.'
              : `No ${filters.status} reports found.`}
          </p>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="flex items-center space-x-2">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setPage(pageNum)}
                  className={`px-3 py-1 rounded-md ${
                    page === pageNum
                      ? 'bg-nature-green text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-3 py-1 rounded-md ${
                page === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Next
            </button>
          </nav>
        </div>
      )}

      {/* Resolve/Dismiss Modal */}
      {modalData && modalAction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-bold mb-4 capitalize">
              {modalAction} Report
            </h3>

            <p className="mb-4">
              Are you sure you want to {modalAction} this report?
              {modalAction === 'resolve' && (
                <span className="block mt-2 text-sm text-gray-500">
                  Resolving indicates that you've taken action on the reported content.
                </span>
              )}
              {modalAction === 'dismiss' && (
                <span className="block mt-2 text-sm text-gray-500">
                  Dismissing indicates that no action is needed for this report.
                </span>
              )}
            </p>

            <div className="mb-4">
              <label htmlFor="moderatorNotes" className="block text-sm font-medium text-gray-700 mb-1">
                Moderator Notes (optional)
              </label>
              <textarea
                id="moderatorNotes"
                value={moderatorNotes}
                onChange={(e) => setModeratorNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Add notes about how this report was handled..."
              />
            </div>

            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center"
              >
                <FaTimes className="mr-2" />
                Cancel
              </button>
              <button
                onClick={handleResolveReport}
                disabled={isLoading}
                className={`px-4 py-2 text-white rounded-md transition-colors flex items-center ${
                  modalAction === 'resolve'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-gray-600 hover:bg-gray-700'
                }`}
              >
                {modalAction === 'resolve' ? <FaCheck className="mr-2" /> : <FaTimes className="mr-2" />}
                {isLoading ? 'Processing...' : `Confirm ${modalAction}`}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Render the Bulk Action Modal */}
      <BulkActionModal
        bulkActionModalOpen={bulkActionModalOpen}
        bulkAction={bulkAction}
        selectedReports={selectedReports}
        moderatorNotes={moderatorNotes}
        setModeratorNotes={setModeratorNotes}
        setBulkActionModalOpen={setBulkActionModalOpen}
        setBulkAction={setBulkAction}
        isLoading={isLoading}
        handleBulkAction={handleBulkAction}
      />
    </div>
  );
}

// Separate component for the bulk action modal
function BulkActionModal({
  bulkActionModalOpen,
  bulkAction,
  selectedReports,
  moderatorNotes,
  setModeratorNotes,
  setBulkActionModalOpen,
  setBulkAction,
  isLoading,
  handleBulkAction
}) {
  if (!bulkActionModalOpen || !bulkAction) return null;

  return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <h3 className="text-xl font-bold mb-4 capitalize">{bulkAction} {selectedReports.length} Reports</h3>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-4">
                You are about to {bulkAction} {selectedReports.length} reports. This action cannot be undone.
              </p>

              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      Bulk actions will apply the same notes to all selected reports.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="bulkModeratorNotes" className="block text-sm font-medium text-gray-700 mb-1">
                Moderator Notes
              </label>
              <textarea
                id="bulkModeratorNotes"
                value={moderatorNotes}
                onChange={(e) => setModeratorNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                rows={4}
                placeholder="Add notes about your decision..."
              ></textarea>
            </div>

            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setBulkActionModalOpen(false);
                  setBulkAction(null);
                  setModeratorNotes('');
                }}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                onClick={handleBulkAction}
                className={`px-4 py-2 rounded-md text-white transition-colors ${bulkAction === 'resolve' ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-600 hover:bg-gray-700'}`}
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  <span className="capitalize">{bulkAction} All</span>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
}
