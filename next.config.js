/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configure for Netlify
  output: 'standalone', // Use standalone output for Netlify

  // Define routes that should be treated as dynamic (not statically generated)
  // This helps Netlify understand which routes need server-side rendering
  dynamicRoutes: [
    '/admin/**',
    '/admin',
    '/auth/**',
    '/auth',
    '/profile/**',
    '/profile',
    '/forums/**',
    '/forums',
    '/activity',
    '/notifications',
    '/leaderboards',
    '/offline',
    '/help/**',
    '/help',
  ],
  reactStrictMode: true,
  // Configure experimental features
  experimental: {
    // Enable build cache
    optimizePackageImports: ['@supabase/supabase-js'],
    serverActions: {
      allowedOrigins: ['localhost:3000', '127.0.0.1:3000']
    },
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  // Disable static generation
  staticPageGenerationTimeout: 1000,
  // Disable static exports
  distDir: '.next',
  // Disable static generation
  trailingSlash: true,
  // Disable static generation
  images: {
    unoptimized: true,
    domains: ['avatars.githubusercontent.com', 'lh3.googleusercontent.com', 'gravatar.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

  // Configure Netlify Edge Functions
  env: {
    NETLIFY: process.env.NETLIFY || '',
  },

  // Configure for Netlify deployment
  generateBuildId: async () => {
    // This helps with caching and versioning
    return `build-${new Date().getTime()}`;
  },
};

module.exports = nextConfig;
