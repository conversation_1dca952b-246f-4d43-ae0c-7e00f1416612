'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type RolePermission = {
  id: string;
  role: 'user' | 'moderator' | 'admin';
  resource: string;
  action: string;
  allowed: boolean;
};

type ResourceGroup = {
  name: string;
  permissions: RolePermission[];
};

export default function AdminPermissionsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [permissions, setPermissions] = useState<RolePermission[]>([]);
  const [resourceGroups, setResourceGroups] = useState<ResourceGroup[]>([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedRole, setSelectedRole] = useState<'user' | 'moderator' | 'admin'>('user');

  useEffect(() => {
    async function checkAuth() {
      if (authLoading) return;
      
      if (!user) {
        router.push('/auth/signin');
        return;
      }
      
      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (error || !profile || profile.role !== 'admin') {
        router.push('/');
        return;
      }
      
      setIsAdmin(true);
      setIsLoading(false);
      loadPermissions();
    }
    
    checkAuth();
  }, [user, authLoading, router]);

  useEffect(() => {
    if (permissions.length > 0) {
      organizePermissionsByResource();
    }
  }, [permissions, selectedRole]);

  // Generate mock permissions data
  async function loadPermissions() {
    try {
      setIsLoading(true);
      
      // Mock permissions data
      const mockPermissions: RolePermission[] = [
        // User permissions
        { id: '1', role: 'user', resource: 'articles', action: 'read', allowed: true },
        { id: '2', role: 'user', resource: 'articles', action: 'create', allowed: true },
        { id: '3', role: 'user', resource: 'articles', action: 'update_own', allowed: true },
        { id: '4', role: 'user', resource: 'articles', action: 'delete_own', allowed: false },
        { id: '5', role: 'user', resource: 'comments', action: 'create', allowed: true },
        { id: '6', role: 'user', resource: 'comments', action: 'update_own', allowed: true },
        { id: '7', role: 'user', resource: 'comments', action: 'delete_own', allowed: true },
        { id: '8', role: 'user', resource: 'media', action: 'upload', allowed: true },
        { id: '9', role: 'user', resource: 'media', action: 'delete_own', allowed: true },
        { id: '10', role: 'user', resource: 'profile', action: 'update_own', allowed: true },
        
        // Moderator permissions
        { id: '11', role: 'moderator', resource: 'articles', action: 'read', allowed: true },
        { id: '12', role: 'moderator', resource: 'articles', action: 'create', allowed: true },
        { id: '13', role: 'moderator', resource: 'articles', action: 'update_any', allowed: true },
        { id: '14', role: 'moderator', resource: 'articles', action: 'delete_own', allowed: true },
        { id: '15', role: 'moderator', resource: 'articles', action: 'delete_any', allowed: false },
        { id: '16', role: 'moderator', resource: 'articles', action: 'approve', allowed: true },
        { id: '17', role: 'moderator', resource: 'comments', action: 'create', allowed: true },
        { id: '18', role: 'moderator', resource: 'comments', action: 'update_any', allowed: true },
        { id: '19', role: 'moderator', resource: 'comments', action: 'delete_any', allowed: true },
        { id: '20', role: 'moderator', resource: 'media', action: 'upload', allowed: true },
        { id: '21', role: 'moderator', resource: 'media', action: 'delete_any', allowed: true },
        { id: '22', role: 'moderator', resource: 'media', action: 'approve', allowed: true },
        { id: '23', role: 'moderator', resource: 'profile', action: 'update_own', allowed: true },
        { id: '24', role: 'moderator', resource: 'users', action: 'view', allowed: true },
        
        // Admin permissions
        { id: '25', role: 'admin', resource: 'articles', action: 'read', allowed: true },
        { id: '26', role: 'admin', resource: 'articles', action: 'create', allowed: true },
        { id: '27', role: 'admin', resource: 'articles', action: 'update_any', allowed: true },
        { id: '28', role: 'admin', resource: 'articles', action: 'delete_any', allowed: true },
        { id: '29', role: 'admin', resource: 'articles', action: 'approve', allowed: true },
        { id: '30', role: 'admin', resource: 'comments', action: 'create', allowed: true },
        { id: '31', role: 'admin', resource: 'comments', action: 'update_any', allowed: true },
        { id: '32', role: 'admin', resource: 'comments', action: 'delete_any', allowed: true },
        { id: '33', role: 'admin', resource: 'media', action: 'upload', allowed: true },
        { id: '34', role: 'admin', resource: 'media', action: 'delete_any', allowed: true },
        { id: '35', role: 'admin', resource: 'media', action: 'approve', allowed: true },
        { id: '36', role: 'admin', resource: 'profile', action: 'update_own', allowed: true },
        { id: '37', role: 'admin', resource: 'profile', action: 'update_any', allowed: true },
        { id: '38', role: 'admin', resource: 'users', action: 'view', allowed: true },
        { id: '39', role: 'admin', resource: 'users', action: 'create', allowed: true },
        { id: '40', role: 'admin', resource: 'users', action: 'update', allowed: true },
        { id: '41', role: 'admin', resource: 'users', action: 'delete', allowed: true },
        { id: '42', role: 'admin', resource: 'settings', action: 'view', allowed: true },
        { id: '43', role: 'admin', resource: 'settings', action: 'update', allowed: true },
        { id: '44', role: 'admin', resource: 'logs', action: 'view', allowed: true },
        { id: '45', role: 'admin', resource: 'backup', action: 'create', allowed: true },
        { id: '46', role: 'admin', resource: 'backup', action: 'restore', allowed: true },
      ];
      
      setPermissions(mockPermissions);
    } catch (err) {
      console.error('Error loading permissions:', err);
      setError('An unexpected error occurred while loading permissions');
    } finally {
      setIsLoading(false);
    }
  }

  function organizePermissionsByResource() {
    const filteredPermissions = permissions.filter(p => p.role === selectedRole);
    
    // Group permissions by resource
    const resourceMap = new Map<string, RolePermission[]>();
    
    filteredPermissions.forEach(permission => {
      if (!resourceMap.has(permission.resource)) {
        resourceMap.set(permission.resource, []);
      }
      resourceMap.get(permission.resource)!.push(permission);
    });
    
    // Convert map to array of resource groups
    const groups: ResourceGroup[] = [];
    resourceMap.forEach((perms, resource) => {
      groups.push({
        name: resource,
        permissions: perms
      });
    });
    
    // Sort groups alphabetically
    groups.sort((a, b) => a.name.localeCompare(b.name));
    
    setResourceGroups(groups);
  }

  const handlePermissionChange = (permissionId: string, allowed: boolean) => {
    setPermissions(prevPermissions => 
      prevPermissions.map(permission => 
        permission.id === permissionId ? { ...permission, allowed } : permission
      )
    );
  };

  const handleSavePermissions = async () => {
    try {
      setIsSaving(true);
      setError('');
      setSuccess('');
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess(`Permissions for ${selectedRole} role updated successfully`);
    } catch (err) {
      console.error('Error saving permissions:', err);
      setError('An unexpected error occurred while saving permissions');
    } finally {
      setIsSaving(false);
    }
  };

  const formatActionName = (action: string) => {
    return action
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatResourceName = (resource: string) => {
    return resource.charAt(0).toUpperCase() + resource.slice(1);
  };

  if (!isAdmin || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">User Permissions</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          {success}
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Role Permissions</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setSelectedRole('user')}
              className={`px-3 py-1 rounded-md text-sm ${selectedRole === 'user' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              User
            </button>
            <button
              onClick={() => setSelectedRole('moderator')}
              className={`px-3 py-1 rounded-md text-sm ${selectedRole === 'moderator' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Moderator
            </button>
            <button
              onClick={() => setSelectedRole('admin')}
              className={`px-3 py-1 rounded-md text-sm ${selectedRole === 'admin' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Admin
            </button>
          </div>
        </div>

        <div className="mb-6">
          <p className="text-gray-600">
            {selectedRole === 'user' && 'Regular users can contribute content but have limited moderation abilities.'}
            {selectedRole === 'moderator' && 'Moderators can review and approve content, but cannot access system settings.'}
            {selectedRole === 'admin' && 'Administrators have full access to all features and settings.'}
          </p>
        </div>

        <div className="space-y-6">
          {resourceGroups.map(group => (
            <div key={group.name} className="border border-gray-200 rounded-md overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <h3 className="font-medium text-gray-700">{formatResourceName(group.name)}</h3>
              </div>
              <div className="divide-y divide-gray-200">
                {group.permissions.map(permission => (
                  <div key={permission.id} className="px-4 py-3 flex justify-between items-center">
                    <span className="text-gray-700">{formatActionName(permission.action)}</span>
                    <div className="flex items-center">
                      <label className="inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={permission.allowed}
                          onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}
                          disabled={selectedRole === 'admin' && ['settings', 'users', 'backup', 'logs'].includes(permission.resource)}
                          className="sr-only peer"
                        />
                        <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-nature-green"></div>
                      </label>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 flex justify-end">
          <button
            onClick={handleSavePermissions}
            disabled={isSaving || selectedRole === 'admin'}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isSaving ? 'Saving...' : 'Save Permissions'}
          </button>
        </div>
        
        {selectedRole === 'admin' && (
          <div className="mt-4 p-4 bg-yellow-50 text-yellow-700 rounded-md border border-yellow-200">
            <p className="text-sm">
              <strong>Note:</strong> Admin permissions cannot be modified for security reasons. Admins always have full access to all system features.
            </p>
          </div>
        )}
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Permission Definitions</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Actions</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Read:</span>
                <span className="text-gray-600">View content</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Create:</span>
                <span className="text-gray-600">Create new content</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Update Own:</span>
                <span className="text-gray-600">Edit content created by the user</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Update Any:</span>
                <span className="text-gray-600">Edit any content regardless of creator</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Delete Own:</span>
                <span className="text-gray-600">Delete content created by the user</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Delete Any:</span>
                <span className="text-gray-600">Delete any content regardless of creator</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Approve:</span>
                <span className="text-gray-600">Review and approve content</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Resources</h3>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Articles:</span>
                <span className="text-gray-600">Wiki articles and knowledge base content</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Comments:</span>
                <span className="text-gray-600">User comments on articles</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Media:</span>
                <span className="text-gray-600">Images, videos, and other uploaded files</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Profile:</span>
                <span className="text-gray-600">User profile information</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Users:</span>
                <span className="text-gray-600">User accounts and management</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Settings:</span>
                <span className="text-gray-600">System configuration settings</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Logs:</span>
                <span className="text-gray-600">System logs and activity records</span>
              </li>
              <li className="flex items-start">
                <span className="font-medium text-gray-700 mr-2">Backup:</span>
                <span className="text-gray-600">Database backup and restore operations</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
