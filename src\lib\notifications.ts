import { createClient } from '@/lib/supabase';
import { Notification, NotificationPreferences } from '@/lib/notification-types';

// Get user notifications
export async function getUserNotifications({
  userId,
  limit = 10,
  offset = 0,
  unreadOnly = false
}: {
  userId: string;
  limit?: number;
  offset?: number;
  unreadOnly?: boolean;
}) {
  const supabase = createClient();

  let query = supabase
    .from('notifications')
    .select(`
      *,
      sender:sender_id (
        username,
        full_name,
        avatar_url
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (unreadOnly) {
    query = query.eq('is_read', false);
  }

  const { data, error, count } = await query;

  if (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }

  return { data: data as Notification[], count };
}

// Get unread notification count
export async function getUnreadNotificationCount(userId: string) {
  const supabase = createClient();

  const { count, error } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('is_read', false);

  if (error) {
    console.error('Error fetching unread notification count:', error);
    throw error;
  }

  return count || 0;
}

// Mark notification as read
export async function markNotificationAsRead(notificationId: string) {
  const supabase = createClient();

  const { error } = await supabase
    .from('notifications')
    .update({ is_read: true })
    .eq('id', notificationId);

  if (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }

  return true;
}

// Mark all notifications as read
export async function markAllNotificationsAsRead(userId: string) {
  const supabase = createClient();

  const { error } = await supabase
    .from('notifications')
    .update({ is_read: true })
    .eq('user_id', userId)
    .eq('is_read', false);

  if (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }

  return true;
}

// Delete notification
export async function deleteNotification(notificationId: string) {
  const supabase = createClient();

  const { error } = await supabase
    .from('notifications')
    .delete()
    .eq('id', notificationId);

  if (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }

  return true;
}

// Get notification preferences
export async function getNotificationPreferences(userId: string) {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('notification_preferences')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 is "No rows returned"
    console.error('Error fetching notification preferences:', error);
    throw error;
  }

  return data as NotificationPreferences | null;
}

// Update notification preferences
export async function updateNotificationPreferences(
  userId: string,
  preferences: Partial<NotificationPreferences>
) {
  const supabase = createClient();

  // Remove user_id from preferences to avoid conflicts
  const { user_id, created_at, updated_at, ...prefsToUpdate } = preferences as any;

  const { data, error } = await supabase
    .from('notification_preferences')
    .upsert({
      user_id: userId,
      ...prefsToUpdate,
      updated_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    console.error('Error updating notification preferences:', error);
    throw error;
  }

  return data as NotificationPreferences;
}

// Create a notification (for client-side use)
export async function createNotification({
  userId,
  senderId,
  notificationType,
  contentType,
  contentId,
  message,
  link,
  metadata
}: {
  userId: string;
  senderId?: string;
  notificationType: string;
  contentType: string;
  contentId: string;
  message: string;
  link?: string;
  metadata?: any;
}) {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('notifications')
    .insert({
      user_id: userId,
      sender_id: senderId,
      notification_type: notificationType,
      content_type: contentType,
      content_id: contentId,
      message,
      link,
      metadata
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating notification:', error);
    throw error;
  }

  return data as Notification;
}

// Create a moderation notification
export async function createModerationNotification({
  userId,
  contentType,
  contentId,
  action,
  reason,
  moderatorId
}: {
  userId: string;
  contentType: 'post' | 'topic' | 'user';
  contentId: string;
  action: string;
  reason: string;
  moderatorId: string;
}) {
  // Format the message based on the action
  let message = '';
  let link = '';

  switch (action) {
    case 'edit_post':
      message = `A moderator has edited your post. Reason: ${reason}`;
      link = `/forums/post/${contentId}`;
      break;
    case 'soft_delete_post':
    case 'hard_delete_post':
      message = `A moderator has removed your post. Reason: ${reason}`;
      break;
    case 'lock_topic':
      message = `A moderator has locked your topic. Reason: ${reason}`;
      link = `/forums/topic/${contentId}`;
      break;
    case 'move_topic':
      message = `A moderator has moved your topic to another category. Reason: ${reason}`;
      link = `/forums/topic/${contentId}`;
      break;
    case 'ban_user':
      message = `Your account has been suspended. Reason: ${reason}`;
      break;
    default:
      message = `A moderator has taken action on your content. Reason: ${reason}`;
  }

  return createNotification({
    userId,
    senderId: moderatorId,
    notificationType: 'moderation_action',
    contentType,
    contentId,
    message,
    link,
    metadata: {
      action,
      reason
    }
  });
}

// Create a report resolved notification
export async function createReportResolvedNotification({
  reporterId,
  moderatorId,
  reportId,
  contentType,
  contentId,
  resolution
}: {
  reporterId: string;
  moderatorId: string;
  reportId: string;
  contentType: 'post' | 'topic';
  contentId: string;
  resolution: 'resolved' | 'dismissed';
}) {
  const message = `Your report has been ${resolution}.`;
  const link = contentType === 'post'
    ? `/forums/post/${contentId}`
    : `/forums/topic/${contentId}`;

  return createNotification({
    userId: reporterId,
    senderId: moderatorId,
    notificationType: 'report_resolved',
    contentType: 'report',
    contentId: reportId,
    message,
    link,
    metadata: {
      resolution,
      contentType,
      contentId
    }
  });
}
