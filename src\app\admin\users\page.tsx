'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getUsers, updateUserRole } from '../actions';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type User = {
  id: string;
  username: string;
  full_name: string;
  email: string;
  role: 'user' | 'moderator' | 'admin';
  created_at: string;
};

type PaginationData = {
  page: number;
  limit: number;
  total: number;
  pages: number;
};

export default function AdminUsersPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [authChecked, setAuthChecked] = React.useState(false);
  const [unauthorized, setUnauthorized] = React.useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [roleFilter, setRoleFilter] = useState<'user' | 'moderator' | 'admin' | undefined>(undefined);
  const [updatingUserId, setUpdatingUserId] = useState<string | null>(null);

  React.useEffect(() => {
    async function checkAuth() {
      if (authLoading) return;

      if (!user) {
        setUnauthorized(true);
        setAuthChecked(true);
        setTimeout(() => router.push('/auth/signin'), 1500);
        return;
      }

      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (error || !profile || profile.role !== 'admin') {
        setUnauthorized(true);
        setAuthChecked(true);
        setTimeout(() => router.push('/auth/signin'), 1500);
        return;
      }

      setAuthChecked(true);
    }

    checkAuth();
  }, [router, user, authLoading]);

  useEffect(() => {
    loadUsers();
  }, [pagination.page, roleFilter]);

  async function loadUsers() {
    try {
      setLoading(true);

      // Use Supabase client directly instead of server action
      const supabase = createClientComponentClient();
      const offset = (pagination.page - 1) * pagination.limit;

      // First, get the profiles
      let query = supabase
        .from('profiles')
        .select('id, username, full_name, role, created_at', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(offset, offset + pagination.limit - 1);

      if (roleFilter) {
        query = query.eq('role', roleFilter);
      }

      const { data, error, count } = await query;

      if (error) {
        setError('Failed to load users: ' + error.message);
        return;
      }

      // In a real app, we would have a proper way to get emails
      // For now, we'll just use the profile data without emails
      const formattedData = data.map(item => {
        return {
          id: item.id,
          username: item.username || '',
          full_name: item.full_name || '',
          email: `${item.username || 'user'}@example.com`, // Placeholder email
          role: item.role || 'user',
          created_at: item.created_at
        };
      });

      setUsers(formattedData);
      setPagination({
        page: pagination.page,
        limit: pagination.limit,
        total: count || 0,
        pages: count ? Math.ceil(count / pagination.limit) : 0
      });
    } catch (err) {
      console.error('Error loading users:', err);
      setError('An unexpected error occurred while loading users');
    } finally {
      setLoading(false);
    }
  }

  const handleRoleChange = async (userId: string, newRole: 'user' | 'moderator' | 'admin') => {
    try {
      setUpdatingUserId(userId);
      setError('');
      setSuccess('');

      // Use Supabase client directly
      const supabase = createClientComponentClient();

      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole })
        .eq('id', userId);

      if (error) {
        setError(`Failed to update user role: ${error.message}`);
        return;
      }

      // Update the user in the local state
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId ? { ...user, role: newRole } : user
        )
      );

      setSuccess(`User role updated successfully to ${newRole}`);
    } catch (err) {
      console.error('Error updating user role:', err);
      setError('An unexpected error occurred while updating user role');
    } finally {
      setUpdatingUserId(null);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination(prev => ({ ...prev, page: newPage }));
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-6xl mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <h1 className="text-2xl sm:text-3xl font-bold">User Management</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-3 sm:px-4 py-1.5 sm:py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm sm:text-base"
        >
          Back to Dashboard
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          {success}
        </div>
      )}

      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <h2 className="text-lg sm:text-xl font-semibold">User List</h2>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setRoleFilter(undefined)}
              className={`px-2 sm:px-3 py-1 rounded-md text-xs sm:text-sm ${!roleFilter ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              All
            </button>
            <button
              onClick={() => setRoleFilter('user')}
              className={`px-2 sm:px-3 py-1 rounded-md text-xs sm:text-sm ${roleFilter === 'user' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Users
            </button>
            <button
              onClick={() => setRoleFilter('moderator')}
              className={`px-2 sm:px-3 py-1 rounded-md text-xs sm:text-sm ${roleFilter === 'moderator' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Moderators
            </button>
            <button
              onClick={() => setRoleFilter('admin')}
              className={`px-2 sm:px-3 py-1 rounded-md text-xs sm:text-sm ${roleFilter === 'admin' ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              Admins
            </button>
          </div>
        </div>

        {loading && <p className="text-gray-500">Loading users...</p>}

        {!loading && users.length === 0 && (
          <p className="text-gray-500">No users found.</p>
        )}

        {!loading && users.length > 0 && (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-4 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Joined
                    </th>
                    <th scope="col" className="px-4 sm:px-6 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map(user => (
                    <tr key={user.id}>
                      <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10 bg-gray-200 rounded-full flex items-center justify-center">
                            <span className="text-gray-500 font-medium">
                              {user.username.substring(0, 2).toUpperCase()}
                            </span>
                          </div>
                          <div className="ml-3 sm:ml-4">
                            <div className="text-sm font-medium text-gray-900">{user.full_name}</div>
                            <div className="text-xs sm:text-sm text-gray-500">@{user.username}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                        <div className="text-xs sm:text-sm text-gray-500">{user.email}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.role === 'admin' ? 'bg-purple-100 text-purple-800' : user.role === 'moderator' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
                          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </span>
                      </td>
                      <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                        <div className="text-xs sm:text-sm text-gray-500">{formatDate(user.created_at)}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end items-center space-x-2">
                          <select
                            value={user.role}
                            onChange={(e) => handleRoleChange(user.id, e.target.value as 'user' | 'moderator' | 'admin')}
                            disabled={updatingUserId === user.id}
                            className="text-xs sm:text-sm border border-gray-300 rounded-md p-1"
                          >
                            <option value="user">User</option>
                            <option value="moderator">Moderator</option>
                            <option value="admin">Admin</option>
                          </select>
                          <button
                            onClick={() => router.push(`/admin/users/${user.id}`)}
                            className="text-nature-green hover:text-nature-green-dark text-xs sm:text-sm"
                          >
                            View
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {users.map(user => (
                <div key={user.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-center mb-3">
                    <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-gray-500 font-medium">
                        {user.username.substring(0, 2).toUpperCase()}
                      </span>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{user.full_name}</div>
                      <div className="text-xs text-gray-500">@{user.username}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                    <div>
                      <span className="text-gray-500 block">Email:</span>
                      <span className="font-medium">{user.email}</span>
                    </div>
                    <div>
                      <span className="text-gray-500 block">Joined:</span>
                      <span className="font-medium">{formatDate(user.created_at)}</span>
                    </div>
                    <div>
                      <span className="text-gray-500 block">Role:</span>
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.role === 'admin' ? 'bg-purple-100 text-purple-800' : user.role === 'moderator' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                    <select
                      value={user.role}
                      onChange={(e) => handleRoleChange(user.id, e.target.value as 'user' | 'moderator' | 'admin')}
                      disabled={updatingUserId === user.id}
                      className="text-xs border border-gray-300 rounded-md p-1"
                    >
                      <option value="user">User</option>
                      <option value="moderator">Moderator</option>
                      <option value="admin">Admin</option>
                    </select>
                    <button
                      onClick={() => router.push(`/admin/users/${user.id}`)}
                      className="px-3 py-1 bg-nature-green text-white text-xs rounded-md"
                    >
                      View Profile
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        {pagination.pages > 1 && (
          <div className="mt-4 sm:mt-6 flex flex-col sm:flex-row justify-between items-center gap-3 sm:gap-0">
            <div className="text-xs sm:text-sm text-gray-500 text-center sm:text-left">
              Showing {(pagination.page - 1) * pagination.limit + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} users
            </div>
            <div className="flex flex-wrap justify-center gap-1 sm:gap-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className="px-2 sm:px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs sm:text-sm hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                // Show pages around current page
                let pageNum;
                if (pagination.pages <= 5) {
                  pageNum = i + 1;
                } else if (pagination.page <= 3) {
                  pageNum = i + 1;
                } else if (pagination.page >= pagination.pages - 2) {
                  pageNum = pagination.pages - 4 + i;
                } else {
                  pageNum = pagination.page - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center rounded-md text-xs sm:text-sm ${pagination.page === pageNum ? 'bg-nature-green text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.pages}
                className="px-2 sm:px-3 py-1 bg-gray-200 text-gray-700 rounded-md text-xs sm:text-sm hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}