'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { UserProfile, ProfileVisibility, HerbalInterest, INTEREST_LABELS, PRESET_AVATARS, AVATAR_CATEGORIES } from '@/lib/profile-types';
import { getGravatarUrl } from '@/lib/gravatar';
import { updateProfile } from '../actions';
import { directUpdateProfile } from '../direct-update';

export default function EditProfilePage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<UserProfile>>({
    username: '',
    full_name: '',
    bio: '',
    location: '',
    website: '',
    avatar_url: '',
    avatar_type: 'preset',
    preset_avatar: PRESET_AVATARS[0],
    visibility: 'public',
    show_email: false,
    show_location: false,
    interests: [],
    expertise_level: 'beginner',
    healing_philosophy: '',
    favorite_herbs: [],
    social_links: {
      twitter: '',
      instagram: '',
      facebook: '',
      pinterest: '',
      youtube: ''
    },
    spirit_plant: ''
  });

  // Temporary state for favorite herbs input
  const [herbInput, setHerbInput] = useState('');

  useEffect(() => {
    async function loadProfile() {
      if (loading) return;

      if (!user) {
        router.push('/auth/signin?redirect=/profile/edit');
        return;
      }

      try {
        setIsLoading(true);
        const supabase = createClientComponentClient();

        // Get the user's profile
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
          throw error;
        }

        if (data) {
          // Initialize form with existing data
          setFormData({
            ...formData,
            ...data,
            // Ensure these fields exist even if they're not in the database yet
            social_links: data.social_links || {
              twitter: '',
              instagram: '',
              facebook: '',
              pinterest: '',
              youtube: ''
            },
            favorite_herbs: data.favorite_herbs || [],
            interests: data.interests || []
          });

          if (data.avatar_url) {
            setAvatarPreview(data.avatar_url);
          }
        } else {
          // Set email from auth if profile doesn't exist yet
          setFormData({
            ...formData,
            email: user.email
          });
        }
      } catch (err: any) {
        console.error('Error loading profile:', err);
        setError(err.message || 'Failed to load profile');
      } finally {
        setIsLoading(false);
      }
    }

    loadProfile();
  }, [user, loading, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSocialLinkChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    setFormData(prev => ({
      ...prev,
      social_links: {
        ...prev.social_links,
        [name]: value
      }
    }));
  };

  const handleInterestToggle = (interest: HerbalInterest) => {
    setFormData(prev => {
      const interests = prev.interests || [];

      if (interests.includes(interest)) {
        return {
          ...prev,
          interests: interests.filter(i => i !== interest)
        };
      } else {
        return {
          ...prev,
          interests: [...interests, interest]
        };
      }
    });
  };

  const handleAddHerb = () => {
    if (herbInput.trim() && !formData.favorite_herbs?.includes(herbInput.trim())) {
      setFormData(prev => ({
        ...prev,
        favorite_herbs: [...(prev.favorite_herbs || []), herbInput.trim()]
      }));
      setHerbInput('');
    }
  };

  const handleRemoveHerb = (herb: string) => {
    setFormData(prev => ({
      ...prev,
      favorite_herbs: prev.favorite_herbs?.filter(h => h !== herb) || []
    }));
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setAvatarFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      setFormData(prev => ({
        ...prev,
        avatar_type: 'uploaded'
      }));
    }
  };

  const handlePresetAvatarSelect = (preset: string) => {
    setFormData(prev => ({
      ...prev,
      avatar_type: 'preset',
      preset_avatar: preset
    }));

    // Clear uploaded avatar
    setAvatarFile(null);
    setAvatarPreview(`/avatars/${preset}.png`);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      setError('You must be logged in to update your profile');
      return;
    }

    try {
      setIsSaving(true);
      setError('');
      setSuccess('');

      const supabase = createClientComponentClient();

      // Upload avatar if changed
      let avatarUrl = formData.avatar_url;

      if (avatarFile) {
        const fileExt = avatarFile.name.split('.').pop();
        const filePath = `avatars/${user.id}-${Date.now()}.${fileExt}`;

        const { error: uploadError } = await supabase.storage
          .from('profiles')
          .upload(filePath, avatarFile);

        if (uploadError) {
          throw uploadError;
        }

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('profiles')
          .getPublicUrl(filePath);

        avatarUrl = publicUrl;
      } else if (formData.avatar_type === 'preset' && formData.preset_avatar) {
        // Use preset avatar
        avatarUrl = `/avatars/${formData.preset_avatar}.png`;
      } else if (formData.avatar_type === 'gravatar' && user.email) {
        // Use Gravatar
        avatarUrl = getGravatarUrl(user.email);
      }

      // Prepare profile data
      const profileData = {
        id: user.id,
        username: formData.username,
        full_name: formData.full_name,
        bio: formData.bio,
        location: formData.location,
        website: formData.website,
        avatar_url: avatarUrl,
        avatar_type: formData.avatar_type,
        preset_avatar: formData.preset_avatar,
        visibility: formData.visibility as ProfileVisibility,
        show_email: formData.show_email,
        show_location: formData.show_location,
        interests: formData.interests,
        expertise_level: formData.expertise_level,
        healing_philosophy: formData.healing_philosophy,
        favorite_herbs: formData.favorite_herbs,
        social_links: formData.social_links,
        spirit_plant: formData.spirit_plant
      };

      // Log the profile data being sent
      console.log('Sending profile data:', {
        id: profileData.id,
        username: profileData.username,
        // other fields omitted for brevity
      });

      // Try the service role API approach first
      try {
        const supabase = createClientComponentClient();
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          throw new Error('No active session found');
        }

        // Get the access token
        const accessToken = session.access_token;

        // Call the service API endpoint
        const response = await fetch('/api/profile/service-update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          },
          body: JSON.stringify(profileData)
        });

        const result = await response.json();
        console.log('Profile service update result:', result);

        if (!response.ok || result.error) {
          // If service API fails, throw error to try the fallback
          throw new Error(result.error || 'Service API failed, trying fallback');
        }

        // If we get here, the service API worked
        console.log('Profile updated successfully via service API');
      } catch (serviceError) {
        // Fallback to direct update if service API fails
        console.warn('Service API failed, trying direct update:', serviceError);

        // Try direct update as fallback
        const directResult = await directUpdateProfile(profileData);
        console.log('Direct update result:', directResult);

        if (directResult.error) {
          // If both approaches fail, throw the error
          throw new Error(directResult.error);
        }

        console.log('Profile updated successfully via direct update');
      }

      setSuccess('Profile updated successfully!');

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/profile');
      }, 1500);
    } catch (err: any) {
      console.error('Error updating profile:', err);
      // Provide more detailed error information
      if (err.code) {
        setError(`Error (${err.code}): ${err.message || 'Unknown error'}`);
      } else if (err.details) {
        setError(`Error details: ${err.details}`);
      } else if (err.hint) {
        setError(`Hint: ${err.hint}`);
      } else if (err.message) {
        setError(err.message);
      } else {
        setError(JSON.stringify(err) || 'Failed to update profile');
      }
    } finally {
      setIsSaving(false);
    }
  };

  if (loading || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Edit Your Profile</h1>
        <button
          onClick={() => router.push('/profile')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Cancel
        </button>
      </div>

      {error && (
        <div className="bg-red-50 p-4 rounded-md text-red-700 mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 p-4 rounded-md text-green-700 mb-6">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Basic Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  Username <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  required
                />
              </div>

              <div>
                <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  id="full_name"
                  name="full_name"
                  value={formData.full_name || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                />
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="City, Country"
                />
              </div>

              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                  Website
                </label>
                <input
                  type="url"
                  id="website"
                  name="website"
                  value={formData.website || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="https://example.com"
                />
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                Bio
              </label>
              <textarea
                id="bio"
                name="bio"
                value={formData.bio || ''}
                onChange={handleChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Tell us about yourself and your interest in natural healing..."
              />
            </div>
          </div>

          {/* Avatar */}
          <div className="md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Profile Picture</h2>

            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-shrink-0">
                <div className="h-32 w-32 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {avatarPreview ? (
                    <img
                      src={avatarPreview}
                      alt="Avatar Preview"
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <span className="text-4xl font-bold text-gray-400">
                      {(formData.full_name || formData.username || 'U').charAt(0).toUpperCase()}
                    </span>
                  )}
                </div>
              </div>

              <div className="flex-grow">
                <div className="mb-4">
                  <div className="flex flex-wrap space-x-4">
                    <div className="flex items-center mb-2">
                      <input
                        type="radio"
                        id="avatar_type_preset"
                        name="avatar_type"
                        value="preset"
                        checked={formData.avatar_type === 'preset'}
                        onChange={() => setFormData(prev => ({ ...prev, avatar_type: 'preset' }))}
                        className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                      />
                      <label htmlFor="avatar_type_preset" className="ml-2 block text-sm text-gray-700">
                        Use Preset Avatar
                      </label>
                    </div>
                    <div className="flex items-center mb-2">
                      <input
                        type="radio"
                        id="avatar_type_uploaded"
                        name="avatar_type"
                        value="uploaded"
                        checked={formData.avatar_type === 'uploaded'}
                        onChange={() => setFormData(prev => ({ ...prev, avatar_type: 'uploaded' }))}
                        className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                      />
                      <label htmlFor="avatar_type_uploaded" className="ml-2 block text-sm text-gray-700">
                        Upload Your Own
                      </label>
                    </div>
                    <div className="flex items-center mb-2">
                      <input
                        type="radio"
                        id="avatar_type_gravatar"
                        name="avatar_type"
                        value="gravatar"
                        checked={formData.avatar_type === 'gravatar'}
                        onChange={() => {
                          if (user?.email) {
                            const gravatarUrl = getGravatarUrl(user.email);
                            setAvatarPreview(gravatarUrl);
                            setFormData(prev => ({
                              ...prev,
                              avatar_type: 'gravatar',
                              avatar_url: gravatarUrl
                            }));
                          } else {
                            setFormData(prev => ({ ...prev, avatar_type: 'gravatar' }));
                          }
                        }}
                        className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300"
                      />
                      <label htmlFor="avatar_type_gravatar" className="ml-2 block text-sm text-gray-700">
                        Use Gravatar
                      </label>
                    </div>
                  </div>
                  {formData.avatar_type === 'gravatar' && (
                    <p className="text-xs text-gray-500 mt-1 mb-2">
                      Using your email's Gravatar. <a href="https://gravatar.com" target="_blank" rel="noopener noreferrer" className="text-nature-green hover:underline">Change it at Gravatar.com</a>
                    </p>
                  )}
                </div>

                {formData.avatar_type === 'preset' ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Choose a Preset Avatar
                    </label>
                    <div className="space-y-4 max-h-80 overflow-y-auto pr-2">
                      {AVATAR_CATEGORIES.map(category => (
                        <div key={category.name} className="mb-4">
                          <h4 className="text-sm font-medium text-gray-600 mb-2">{category.name}</h4>
                          <div className="grid grid-cols-6 gap-2">
                            {category.avatars.map(preset => (
                              <button
                                key={preset}
                                type="button"
                                onClick={() => handlePresetAvatarSelect(preset)}
                                className={`h-12 w-12 rounded-full overflow-hidden border-2 ${
                                  formData.preset_avatar === preset ? 'border-nature-green' : 'border-transparent'
                                }`}
                              >
                                <img
                                  src={`/avatars/${preset}.png`}
                                  alt={`Avatar ${preset}`}
                                  className="h-full w-full object-cover"
                                />
                              </button>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    <label htmlFor="avatar" className="block text-sm font-medium text-gray-700 mb-2">
                      Upload Avatar
                    </label>
                    <input
                      type="file"
                      id="avatar"
                      name="avatar"
                      onChange={handleAvatarChange}
                      accept="image/*"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Recommended: Square image, at least 200x200 pixels. Max 2MB.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Herbal Interests */}
          <div className="md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Herbal Interests</h2>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Your Interests
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {Object.entries(INTEREST_LABELS).map(([key, label]) => (
                  <div key={key} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`interest_${key}`}
                      checked={formData.interests?.includes(key as HerbalInterest) || false}
                      onChange={() => handleInterestToggle(key as HerbalInterest)}
                      className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                    />
                    <label htmlFor={`interest_${key}`} className="ml-2 block text-sm text-gray-700">
                      {label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="expertise_level" className="block text-sm font-medium text-gray-700 mb-1">
                Expertise Level
              </label>
              <select
                id="expertise_level"
                name="expertise_level"
                value={formData.expertise_level || 'beginner'}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
                <option value="expert">Expert</option>
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="healing_philosophy" className="block text-sm font-medium text-gray-700 mb-1">
                Your Healing Philosophy
              </label>
              <textarea
                id="healing_philosophy"
                name="healing_philosophy"
                value={formData.healing_philosophy || ''}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Share your approach to natural healing..."
              />
            </div>

            <div className="mb-4">
              <label htmlFor="favorite_herbs" className="block text-sm font-medium text-gray-700 mb-1">
                Favorite Herbs
              </label>
              <div className="flex">
                <input
                  type="text"
                  id="favorite_herbs"
                  value={herbInput}
                  onChange={(e) => setHerbInput(e.target.value)}
                  className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="Add a favorite herb..."
                />
                <button
                  type="button"
                  onClick={handleAddHerb}
                  className="px-4 py-2 bg-nature-green text-white rounded-r-md hover:bg-green-700 transition-colors"
                >
                  Add
                </button>
              </div>

              {formData.favorite_herbs && formData.favorite_herbs.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-2">
                  {formData.favorite_herbs.map(herb => (
                    <span
                      key={herb}
                      className="px-3 py-1 bg-green-50 text-green-700 border border-green-200 rounded-full text-sm flex items-center"
                    >
                      {herb}
                      <button
                        type="button"
                        onClick={() => handleRemoveHerb(herb)}
                        className="ml-2 text-green-700 hover:text-green-900"
                      >
                        &times;
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="spirit_plant" className="block text-sm font-medium text-gray-700 mb-1">
                Your Spirit Plant
              </label>
              <input
                type="text"
                id="spirit_plant"
                name="spirit_plant"
                value={formData.spirit_plant || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="What plant represents your essence?"
              />
              <p className="text-xs text-gray-500 mt-1">
                Just for fun! Share the plant that you feel most connected to.
              </p>
            </div>
          </div>

          {/* Social Links */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Social Media</h2>

            <div className="space-y-3">
              <div>
                <label htmlFor="twitter" className="block text-sm font-medium text-gray-700 mb-1">
                  Twitter
                </label>
                <input
                  type="url"
                  id="twitter"
                  name="twitter"
                  value={formData.social_links?.twitter || ''}
                  onChange={handleSocialLinkChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="https://twitter.com/yourusername"
                />
              </div>

              <div>
                <label htmlFor="instagram" className="block text-sm font-medium text-gray-700 mb-1">
                  Instagram
                </label>
                <input
                  type="url"
                  id="instagram"
                  name="instagram"
                  value={formData.social_links?.instagram || ''}
                  onChange={handleSocialLinkChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="https://instagram.com/yourusername"
                />
              </div>

              <div>
                <label htmlFor="facebook" className="block text-sm font-medium text-gray-700 mb-1">
                  Facebook
                </label>
                <input
                  type="url"
                  id="facebook"
                  name="facebook"
                  value={formData.social_links?.facebook || ''}
                  onChange={handleSocialLinkChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="https://facebook.com/yourusername"
                />
              </div>

              <div>
                <label htmlFor="pinterest" className="block text-sm font-medium text-gray-700 mb-1">
                  Pinterest
                </label>
                <input
                  type="url"
                  id="pinterest"
                  name="pinterest"
                  value={formData.social_links?.pinterest || ''}
                  onChange={handleSocialLinkChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="https://pinterest.com/yourusername"
                />
              </div>

              <div>
                <label htmlFor="youtube" className="block text-sm font-medium text-gray-700 mb-1">
                  YouTube
                </label>
                <input
                  type="url"
                  id="youtube"
                  name="youtube"
                  value={formData.social_links?.youtube || ''}
                  onChange={handleSocialLinkChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                  placeholder="https://youtube.com/c/yourchannel"
                />
              </div>
            </div>
          </div>

          {/* Privacy Settings */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Privacy Settings</h2>

            <div className="mb-4">
              <label htmlFor="visibility" className="block text-sm font-medium text-gray-700 mb-1">
                Profile Visibility
              </label>
              <select
                id="visibility"
                name="visibility"
                value={formData.visibility || 'public'}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
              >
                <option value="public">Public - Anyone can view</option>
                <option value="members">Members Only - Only registered users can view</option>
                <option value="private">Private - Only you can view</option>
              </select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="show_email"
                  name="show_email"
                  checked={formData.show_email || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                />
                <label htmlFor="show_email" className="ml-2 block text-sm text-gray-700">
                  Show my email address on my profile
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="show_location"
                  name="show_location"
                  checked={formData.show_location || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
                />
                <label htmlFor="show_location" className="ml-2 block text-sm text-gray-700">
                  Show my location on my profile
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <button
            type="submit"
            disabled={isSaving}
            className="px-6 py-3 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isSaving ? 'Saving...' : 'Save Profile'}
          </button>
        </div>
      </form>
    </div>
  );
}
