'use client';

import React, { useState, useEffect, use } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useAuth } from '@/components/AuthProvider';
import Link from 'next/link';
import Image from 'next/image';
import { formatDate } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import SocialShare from '@/components/SocialShare';
import DirectImageUploader from '@/components/DirectImageUploader';
import { FaEye, FaCalendarAlt, FaUserEdit, FaFolder } from 'react-icons/fa';

export default function ArticlePage({ params }: { params: Promise<{ slug: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const { slug } = unwrappedParams;

  const { user } = useAuth();
  const router = useRouter();
  const [article, setArticle] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<any[]>([]);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        setError(null);

        const supabase = createClientComponentClient();
        const { data, error } = await supabase
          .from('articles')
          .select(`
            *,
            profiles!author_id (username, full_name, avatar_url),
            categories!category_id (name, slug, icon)
          `)
          .eq('slug', slug)
          .eq('status', 'published')
          .single();

        if (error) {
          console.error('Error fetching article:', error);
          setError('Article not found');
          return;
        }

        if (!data) {
          setError('Article not found');
          return;
        }

        // Fetch article images
        const { data: articleImages, error: imagesError } = await supabase
          .from('article_media')
          .select(`
            media_id,
            display_order,
            media (id, title, description, file_url, file_type)
          `)
          .eq('article_id', data.id)
          .order('display_order', { ascending: true });

        if (!imagesError && articleImages && articleImages.length > 0) {
          // Add the first image as the featured image
          data.featured_image = articleImages[0].media.file_url;
          data.featured_image_alt = articleImages[0].media.description || articleImages[0].media.title;
          // Add all images to the article data
          data.images = articleImages;
        } else {
          // If no images found in article_media, try to get any image from the media table
          const { data: mediaItems, error: mediaError } = await supabase
            .from('media')
            .select('file_url, title, description')
            .eq('status', 'published')
            .limit(1);

          if (!mediaError && mediaItems && mediaItems.length > 0) {
            data.featured_image = mediaItems[0].file_url;
            data.featured_image_alt = mediaItems[0].description || mediaItems[0].title || data.title;
          }
        }

        setArticle(data);

        // Increment view count
        await supabase
          .from('articles')
          .update({ view_count: (data.view_count || 0) + 1 })
          .eq('id', data.id);

        // Fetch related articles
        if (data.category_id) {
          const { data: relatedData } = await supabase
            .from('articles')
            .select(`
              id,
              title,
              slug,
              excerpt,
              profiles!author_id (username, full_name)
            `)
            .eq('category_id', data.category_id)
            .eq('status', 'published')
            .neq('id', data.id)
            .order('view_count', { ascending: false })
            .limit(3);

          if (relatedData) {
            setRelatedArticles(relatedData);
          }
        }
      } catch (err: any) {
        console.error('Error:', err);
        setError(err.message || 'Failed to fetch article');
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [slug]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
          <span className="ml-3 text-lg">Loading article...</span>
        </div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Article Not Found</h1>
          <p className="text-gray-600 mb-6">
            The article you're looking for doesn't exist or has been removed.
          </p>
          <Link
            href="/wiki"
            className="inline-block bg-nature-green text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors"
          >
            Return to Wiki
          </Link>
        </div>
      </div>
    );
  }

  // Get the full URL for sharing
  const fullUrl = typeof window !== 'undefined' ? window.location.href : '';

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        {/* Breadcrumb */}
        <div className="text-sm text-gray-500 mb-6 bg-gray-50 p-3 rounded-md shadow-sm">
          <Link href="/" className="hover:text-nature-green">Home</Link> &gt;{" "}
          <Link href="/wiki" className="hover:text-nature-green">Wiki</Link> &gt;{" "}
          {article.categories ? (
            <Link href={`/categories/${article.categories.slug}`} className="hover:text-nature-green">
              {article.categories.name}
            </Link>
          ) : (
            <span className="text-gray-500">Uncategorized</span>
          )}
          &gt; <span className="text-gray-700 font-medium">{article.title}</span>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content - 3/4 width on desktop */}
          <div className="lg:col-span-3">
            {/* Article Header */}
            <div className="mb-8 bg-white p-4 sm:p-6 rounded-xl shadow-md">
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">{article.title}</h1>

              <div className="flex flex-wrap items-center justify-between mb-6">
                <div className="flex items-center mb-2 sm:mb-0">
                  <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden mr-3 border-2 border-nature-green">
                    {article.profiles?.avatar_url ? (
                      <Image
                        src={article.profiles.avatar_url}
                        alt={article.profiles.username || 'Author'}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-nature-green text-white">
                        {article.profiles?.username?.charAt(0).toUpperCase() || 'A'}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {article.profiles?.full_name || article.profiles?.username || 'Anonymous'}
                    </div>
                    <div className="text-xs text-gray-500 flex items-center">
                      <FaCalendarAlt className="mr-1" size={12} />
                      {formatDate(article.created_at)}
                      {article.updated_at !== article.created_at && (
                        <span className="ml-2 flex items-center">
                          <FaUserEdit className="mr-1" size={12} />
                          Updated {formatDate(article.updated_at)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center text-gray-500">
                    <FaEye className="mr-1" />
                    <span>{article.view_count || 0}</span>
                  </div>
                  {article.categories && (
                    <div className="flex items-center text-gray-500">
                      <FaFolder className="mr-1" />
                      <Link href={`/categories/${article.categories.slug}`} className="hover:text-nature-green">
                        {article.categories.name}
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              {/* Featured Image */}
              <div className="mb-6 rounded-lg overflow-hidden shadow-md">
                {article.featured_image ? (
                  <Image
                    src={article.featured_image}
                    alt={article.featured_image_alt || article.title}
                    width={1200}
                    height={630}
                    className="w-full h-auto object-cover"
                  />
                ) : (
                  <div className="bg-gray-100 flex items-center justify-center h-64">
                    <div className="text-center p-6">
                      <div className="text-gray-400 text-6xl mb-4">🌿</div>
                      <p className="text-gray-500">
                        {article.categories?.name ? (
                          <span>A {article.categories.name} article</span>
                        ) : (
                          <span>NatureHeals.info</span>
                        )}
                      </p>
                      {(user?.id === article.author_id || user?.role === 'admin') && (
                        <button
                          onClick={() => {
                            const uploadSection = document.querySelector('.direct-image-uploader');
                            if (uploadSection) {
                              uploadSection.scrollIntoView({ behavior: 'smooth' });
                            }
                          }}
                          className="mt-4 px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
                        >
                          Add an image
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {article.excerpt && (
                <div className="bg-gray-50 border-l-4 border-nature-green p-4 rounded-r-md text-gray-700 italic">
                  {article.excerpt}
                </div>
              )}
            </div>

            {/* Article Content */}
            <div className="prose prose-base md:prose-lg max-w-none mb-8 article-content bg-white p-4 sm:p-6 md:p-8 rounded-xl shadow-md">
              {article.content && (
                <div className="article-content">
                  {article.content.split('\n\n').map((paragraph, index) => (
                    <p key={index} className="mb-4">
                      {paragraph.split('\n').map((line, lineIndex) => (
                        <React.Fragment key={lineIndex}>
                          {line}
                          {lineIndex < paragraph.split('\n').length - 1 && <br />}
                        </React.Fragment>
                      ))}
                    </p>
                  ))}
                </div>
              )}

              {/* Direct Image Uploader - Only visible to admins and article author */}
              {user && (user.role === 'admin' || user.id === article.author_id) && (
                <DirectImageUploader
                  articleId={article.id}
                  onSuccess={() => window.location.reload()}
                />
              )}
            </div>

            {/* Article Footer */}
            <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md mb-8">
              <div className="flex flex-col sm:flex-row justify-between items-center">
                {/* Tags would go here if implemented */}
                <div className="mb-4 sm:mb-0 flex space-x-2">
                  {(user?.id === article.author_id || user?.role === 'admin' || user?.role === 'moderator') && (
                    <>
                      <Link
                        href={`/admin/content/edit/${article.id}`}
                        className="inline-flex items-center px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
                      >
                        <FaUserEdit className="mr-2" /> Edit Article
                      </Link>
                      {!article.featured_image && (
                        <button
                          onClick={() => {
                            const contentSection = document.querySelector('.article-content');
                            if (contentSection) {
                              contentSection.scrollIntoView({ behavior: 'smooth' });
                            }
                          }}
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          Add Image
                        </button>
                      )}
                    </>
                  )}
                </div>

                {/* Social Share Component (Mobile) */}
                <div className="sm:hidden w-full">
                  <SocialShare url={fullUrl} title={article.title} description={article.excerpt} />
                </div>
              </div>
            </div>

            {/* Related Articles */}
            {relatedArticles.length > 0 && (
              <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 border-b pb-2">Related Articles</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {relatedArticles.map((related) => (
                    <div key={related.id} className="bg-gray-50 rounded-lg overflow-hidden hover:shadow-md transition-shadow border border-gray-100">
                      <div className="p-4">
                        <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                          <Link href={`/wiki/${related.slug}`} className="hover:text-nature-green">
                            {related.title}
                          </Link>
                        </h3>
                        {related.excerpt && (
                          <p className="text-gray-600 text-sm mb-4 line-clamp-3">{related.excerpt}</p>
                        )}
                        <div className="text-xs text-gray-500">
                          By {related.profiles?.full_name || related.profiles?.username || 'Anonymous'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar - 1/4 width on desktop */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              {/* Social Share Component (Desktop) */}
              <div className="hidden sm:block bg-white p-4 sm:p-6 rounded-xl shadow-md mb-6">
                <SocialShare url={fullUrl} title={article.title} description={article.excerpt} />
              </div>

              {/* Table of Contents would go here */}
              <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md mb-6">
                <h3 className="text-lg font-semibold mb-3 border-b pb-2">About this Article</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center">
                    <FaCalendarAlt className="mr-2 text-gray-500" />
                    <span>Published: {formatDate(article.created_at)}</span>
                  </li>
                  {article.updated_at !== article.created_at && (
                    <li className="flex items-center">
                      <FaUserEdit className="mr-2 text-gray-500" />
                      <span>Last updated: {formatDate(article.updated_at)}</span>
                    </li>
                  )}
                  <li className="flex items-center">
                    <FaEye className="mr-2 text-gray-500" />
                    <span>{article.view_count || 0} views</span>
                  </li>
                  {article.categories && (
                    <li className="flex items-center">
                      <FaFolder className="mr-2 text-gray-500" />
                      <Link href={`/categories/${article.categories.slug}`} className="hover:text-nature-green">
                        {article.categories.name}
                      </Link>
                    </li>
                  )}
                </ul>
              </div>

              {/* Author Info */}
              <div className="bg-white p-4 sm:p-6 rounded-xl shadow-md">
                <h3 className="text-lg font-semibold mb-3 border-b pb-2">Author</h3>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden mr-3 border-2 border-nature-green">
                    {article.profiles?.avatar_url ? (
                      <Image
                        src={article.profiles.avatar_url}
                        alt={article.profiles.username || 'Author'}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-nature-green text-white">
                        {article.profiles?.username?.charAt(0).toUpperCase() || 'A'}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="font-medium">
                      {article.profiles?.full_name || article.profiles?.username || 'Anonymous'}
                    </div>
                    <div className="text-xs text-gray-500">
                      Contributor
                    </div>
                  </div>
                </div>
                {/* Add more author info or link to profile here */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
