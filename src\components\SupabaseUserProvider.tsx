'use client';

import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { User } from '@supabase/supabase-js';

type SupabaseUserContextType = {
  user: User | null;
  loading: boolean;
};

export const SupabaseUserContext = createContext<SupabaseUserContextType>({
  user: null,
  loading: true
});

export function useSupabaseUser() {
  return useContext(SupabaseUserContext);
}

type Props = {
  children: ReactNode;
  initialSession?: { user: User | null };
};

export default function SupabaseUserProvider({ children, initialSession }: Props) {
  const [user, setUser] = useState<User | null>(initialSession?.user ?? null);
  const [loading, setLoading] = useState(!initialSession);

  useEffect(() => {
    const supabase = createClientComponentClient();

    // If no initialSession, fetch on client
    if (!initialSession) {
      supabase.auth.getUser().then(({ data, error }) => {
        setUser(data?.user ?? null);
        setLoading(false);
      });
    } else {
      setLoading(false);
    }

    // Listen for auth state changes
    const { data: listener } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);
      setUser(session?.user ?? null);
    });

    return () => {
      listener?.subscription.unsubscribe();
    };
  }, [initialSession]);

  return (
    <SupabaseUserContext.Provider value={{ user, loading }}>
      {children}
    </SupabaseUserContext.Provider>
  );
}