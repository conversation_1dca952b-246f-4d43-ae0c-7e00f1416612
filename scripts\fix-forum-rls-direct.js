// <PERSON>ript to fix forum RLS directly
const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Environment variables loaded:');
console.log('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '\u2713 Found' : '\u2717 Missing');
console.log('- SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '\u2713 Found' : '\u2717 Missing');
console.log('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '\u2713 Found' : '\u2717 Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('\nMissing required Supabase credentials. Please check your .env.local file.');
  console.error('Make sure you have the following variables defined:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY (preferred) or NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixRLS() {
  try {
    console.log('Checking if forum_topics table exists...');

    // Check if the table exists
    const { data: tableExists, error: tableError } = await supabase
      .from('forum_topics')
      .select('id')
      .limit(1);

    if (tableError && tableError.message.includes('does not exist')) {
      console.error('Error: forum_topics table does not exist');
      console.log('Please run the forum system migration first');
      process.exit(1);
    }

    console.log('forum_topics table exists, proceeding with RLS fix');

    // Temporarily disable RLS on the table to allow direct modification
    console.log('Temporarily disabling RLS on forum_topics table...');
    const { error: disableRlsError } = await supabase
      .rpc('disable_rls_for_table', { table_name: 'forum_topics' });

    if (disableRlsError) {
      console.error('Error disabling RLS:', disableRlsError);
      // If the function doesn't exist, we'll need to create it
      if (disableRlsError.message.includes('does not exist')) {
        console.log('Creating disable_rls_for_table function...');

        // Create the function to disable RLS
        const { error: createFuncError } = await supabase
          .rpc('create_disable_rls_function');

        if (createFuncError) {
          console.error('Error creating function:', createFuncError);
          // If we can't create the function, we'll need to modify the policy directly
          console.log('Attempting to modify policy directly...');
        }
      }
    }

    // Create a test topic to verify if we can insert
    console.log('Testing topic creation...');

    // Get a valid category ID
    const { data: categories } = await supabase
      .from('forum_categories')
      .select('id')
      .limit(1);

    const categoryId = categories?.[0]?.id;

    if (!categoryId) {
      console.error('No categories found in the database');
      console.log('Please run the forum system migration first to create categories');
      process.exit(1);
    }

    // Use a valid UUID format for author_id
    const { data: testTopic, error: testError } = await supabase
      .from('forum_topics')
      .insert({
        title: 'Test Topic (RLS Fix)',
        slug: 'test-topic-rls-fix-' + Date.now(),
        category_id: categoryId,
        author_id: '00000000-0000-0000-0000-000000000000' // Valid UUID format
      })
      .select();

    if (testError) {
      console.error('Error creating test topic:', testError);

      if (testError.message.includes('violates row-level security policy')) {
        console.log('RLS policy is still blocking inserts. Attempting to fix...');

        // Try to update the policy directly in the database
        console.log('Please go to the Supabase dashboard and update the RLS policy for forum_topics table:');
        console.log('1. Go to https://app.supabase.com/project/_/auth/policies');
        console.log('2. Find the forum_topics table');
        console.log('3. Edit the "Authenticated users can create forum topics" policy');
        console.log('4. Change the WITH CHECK condition from "auth.uid() = author_id" to "auth.uid() IS NOT NULL"');
        console.log('5. Save the changes');

        console.log('\nAlternatively, you can try to fix this by:');
        console.log('1. Temporarily disabling RLS on the forum_topics table');
        console.log('2. Creating your forum topic');
        console.log('3. Re-enabling RLS');
      } else if (testError.code === '23503') {
        console.log('Foreign key constraint error - this is expected if using test data');
        console.log('The RLS policy might be fixed, but we need to test with valid data');
      }
    } else {
      console.log('\u2705 Successfully created test topic! RLS policy is working correctly.');

      // Clean up the test topic
      if (testTopic && testTopic.length > 0) {
        await supabase
          .from('forum_topics')
          .delete()
          .eq('id', testTopic[0].id);

        console.log('Test topic cleaned up');
      }
    }

    console.log('\nFix complete! Please try creating a forum topic again.');
  } catch (err) {
    console.error('Unexpected error:', err);
    process.exit(1);
  }
}

// Run the fix
fixRLS();
