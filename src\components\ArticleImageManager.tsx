'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Image from 'next/image';
import { FaTrash, FaPlus, FaImage, FaClipboard, FaCheck } from 'react-icons/fa';
import ImageUploader from './ImageUploader';

interface ArticleImage {
  media_id: string;
  article_id: string;
  display_order: number;
  media: {
    id: string;
    title: string;
    description: string;
    file_url: string;
    file_type: string;
  };
}

interface ArticleImageManagerProps {
  articleId: string;
}

const ArticleImageManager: React.FC<ArticleImageManagerProps> = ({ articleId }) => {
  const [images, setImages] = useState<ArticleImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUploader, setShowUploader] = useState(false);
  const [copiedId, setCopiedId] = useState<string | null>(null);

  useEffect(() => {
    fetchArticleImages();
  }, [articleId]);

  const fetchArticleImages = async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createClientComponentClient();
      const { data, error } = await supabase
        .from('article_media')
        .select(`
          media_id,
          article_id,
          display_order,
          media (
            id,
            title,
            description,
            file_url,
            file_type
          )
        `)
        .eq('article_id', articleId)
        .order('display_order', { ascending: true });

      if (error) {
        throw error;
      }

      setImages(data || []);
    } catch (err: any) {
      console.error('Error fetching article images:', err);
      setError(err.message || 'Failed to load images');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUploaded = async (imageData: { id: string; url: string; title: string }) => {
    try {
      const supabase = createClientComponentClient();

      // Create association between article and media
      const { data, error } = await supabase
        .from('article_media')
        .insert({
          article_id: articleId,
          media_id: imageData.id,
          display_order: images.length // Add to the end
        })
        .select(`
          media_id,
          article_id,
          display_order,
          media (
            id,
            title,
            description,
            file_url,
            file_type
          )
        `)
        .single();

      if (error) {
        // Check for RLS policy errors
        if (error.message.includes('row-level security') ||
            error.message.includes('new row violates')) {
          throw new Error(
            'Permission denied: The article_media table needs RLS policies. ' +
            'Please add this policy in Supabase: ' +
            '(auth.uid() IN (SELECT id FROM profiles WHERE role = \'admin\' OR role = \'moderator\'))'
          );
        }
        throw error;
      }

      // Update the images list
      setImages([...images, data]);
      setShowUploader(false);
    } catch (err: any) {
      console.error('Error associating image with article:', err);
      setError(err.message || 'Failed to add image to article');
    }
  };

  const handleRemoveImage = async (mediaId: string) => {
    if (!confirm('Are you sure you want to remove this image from the article?')) {
      return;
    }

    try {
      const supabase = createClientComponentClient();

      // Remove the association between article and media
      const { error } = await supabase
        .from('article_media')
        .delete()
        .eq('article_id', articleId)
        .eq('media_id', mediaId);

      if (error) {
        // Check for RLS policy errors
        if (error.message.includes('row-level security') ||
            error.message.includes('new row violates')) {
          throw new Error(
            'Permission denied: The article_media table needs RLS policies for deletes. ' +
            'Please add this policy in Supabase: ' +
            '(auth.uid() IN (SELECT id FROM profiles WHERE role = \'admin\' OR role = \'moderator\'))'
          );
        }
        throw error;
      }

      // Update the images list
      setImages(images.filter(img => img.media_id !== mediaId));
    } catch (err: any) {
      console.error('Error removing image from article:', err);
      setError(err.message || 'Failed to remove image');
    }
  };

  const copyImageMarkdown = (image: ArticleImage) => {
    const markdown = `![${image.media.description || image.media.title}](${image.media.file_url})`;
    navigator.clipboard.writeText(markdown)
      .then(() => {
        setCopiedId(image.media_id);
        setTimeout(() => setCopiedId(null), 2000);
      })
      .catch(err => {
        console.error('Failed to copy markdown:', err);
        setError('Failed to copy to clipboard');
      });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green"></div>
        <span className="ml-2">Loading images...</span>
      </div>
    );
  }

  return (
    <div className="article-image-manager">
      {error && (
        <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
          {error}
        </div>
      )}

      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Article Images</h3>
        {!showUploader && (
          <button
            type="button"
            onClick={() => setShowUploader(true)}
            className="px-3 py-1.5 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors flex items-center text-sm"
          >
            <FaPlus className="mr-1" /> Add Image
          </button>
        )}
      </div>

      {showUploader ? (
        <div className="mb-6">
          <ImageUploader onImageUploaded={handleImageUploaded} />
          <div className="mt-2 text-right">
            <button
              type="button"
              onClick={() => setShowUploader(false)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Cancel
            </button>
          </div>
        </div>
      ) : images.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-6 text-center">
          <FaImage className="h-10 w-10 mx-auto text-gray-400 mb-2" />
          <p className="text-gray-600 mb-4">No images have been added to this article yet</p>
          <button
            type="button"
            onClick={() => setShowUploader(true)}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors inline-flex items-center"
          >
            <FaPlus className="mr-2" /> Add First Image
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {images.map(image => (
            <div key={image.media_id} className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
              <div className="aspect-w-16 aspect-h-9 bg-gray-100">
                <Image
                  src={image.media.file_url}
                  alt={image.media.description || image.media.title}
                  width={400}
                  height={225}
                  className="object-contain w-full h-full"
                />
              </div>
              <div className="p-3">
                <h4 className="font-medium text-gray-900 mb-1 truncate">{image.media.title}</h4>
                <p className="text-xs text-gray-500 mb-3 line-clamp-2">{image.media.description || 'No description'}</p>
                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={() => copyImageMarkdown(image)}
                    className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors flex items-center"
                    title="Copy markdown to insert in content"
                  >
                    {copiedId === image.media_id ? (
                      <>
                        <FaCheck className="mr-1" /> Copied!
                      </>
                    ) : (
                      <>
                        <FaClipboard className="mr-1" /> Copy Markdown
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleRemoveImage(image.media_id)}
                    className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors flex items-center"
                    title="Remove image from article"
                  >
                    <FaTrash className="mr-1" /> Remove
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-4 text-sm text-gray-500">
        <p>To insert an image in your article content, click "Copy Markdown" and paste it where you want the image to appear.</p>
      </div>
    </div>
  );
};

export default ArticleImageManager;
