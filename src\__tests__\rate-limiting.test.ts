import { formatRateLimitMessage } from '@/lib/rate-limiting';

// Mock the checkRateLimit function since it requires database access
jest.mock('@/lib/rate-limiting', () => ({
  ...jest.requireActual('@/lib/rate-limiting'),
  checkRateLimit: jest.fn()
}));

describe('Rate Limiting', () => {
  describe('formatRateLimitMessage', () => {
    test('should format message for forum_post_create action', () => {
      const resetTime = new Date(Date.now() + 30 * 1000); // 30 seconds from now
      const message = formatRateLimitMessage('forum_post_create', resetTime);
      
      expect(message).toContain('You can only create');
      expect(message).toContain('posts per');
      expect(message).toContain('Please try again in');
    });
    
    test('should format message for report_content action', () => {
      const resetTime = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now
      const message = formatRateLimitMessage('report_content', resetTime);
      
      expect(message).toContain('You can only submit');
      expect(message).toContain('reports per');
      expect(message).toContain('Please try again in');
    });
    
    test('should handle missing reset time', () => {
      const message = formatRateLimitMessage('search');
      
      expect(message).toContain('You can only perform');
      expect(message).toContain('searches per minute');
      expect(message).toContain('Please try again later');
      expect(message).not.toContain('Please try again in');
    });
  });
});
