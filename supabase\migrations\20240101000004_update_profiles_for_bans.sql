-- Update profiles table to add ban-related fields
DO $$
BEGIN
    -- Check if is_banned column exists
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'is_banned') THEN
        ALTER TABLE public.profiles ADD COLUMN is_banned BOOLEAN DEFAULT FALSE;
    END IF;

    -- Check if ban_expires_at column exists
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'ban_expires_at') THEN
        ALTER TABLE public.profiles ADD COLUMN ban_expires_at TIMESTAMPTZ;
    END IF;
END $$;
