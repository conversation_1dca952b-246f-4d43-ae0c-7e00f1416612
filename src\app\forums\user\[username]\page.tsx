'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { FaArrowLeft, FaUser, FaComments } from 'react-icons/fa';
import UserLevelDisplay from '@/components/UserLevelDisplay';
import VerificationBadge from '@/components/VerificationBadge';

interface ForumPost {
  id: string;
  content: string;
  created_at: string;
  topic_id: string;
  author_id: string;
  topic: {
    title: string;
    slug: string;
    category: {
      name: string;
      slug: string;
    }
  }
}

export default function UserForumPostsPage({ params }: { params: { username: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [profile, setProfile] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const postsPerPage = 10;

  // Unwrap params with React.use() to avoid warnings
  const unwrappedParams = use(params);
  const username = unwrappedParams.username;

  useEffect(() => {
    async function loadUserAndPosts() {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClientComponentClient();

        // First, get the user profile by username
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('username', username)
          .maybeSingle();

        if (profileError) {
          if (profileError.code === 'PGRST116') {
            // This is the error when multiple rows are returned
            setError('Multiple users found with this username. Please contact support.');
          } else {
            setError(profileError.message || 'Error loading user profile');
          }
          setIsLoading(false);
          return;
        }

        if (!profileData) {
          setError('User not found');
          setIsLoading(false);
          return;
        }

        setProfile(profileData);

        // Then, get the user's forum posts
        const { data: postsData, error: postsError, count } = await supabase
          .from('forum_posts')
          .select(`
            id,
            content,
            created_at,
            topic_id,
            author_id,
            topic:topic_id (
              title,
              slug,
              category:category_id (
                name,
                slug
              )
            )
          `, { count: 'exact' })
          .eq('author_id', profileData.id)
          .order('created_at', { ascending: false })
          .range((page - 1) * postsPerPage, page * postsPerPage - 1);

        if (postsError) {
          setError(postsError.message || 'Error loading forum posts');
          setIsLoading(false);
          return;
        }

        setPosts(postsData as ForumPost[]);
        setHasMore(count !== null && count > page * postsPerPage);
      } catch (err: any) {
        console.error('Error in loadUserAndPosts:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    }

    if (username) {
      loadUserAndPosts();
    }
  }, [username, page]);

  const loadMorePosts = () => {
    setPage(prevPage => prevPage + 1);
  };

  const truncateContent = (content: string, maxLength = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 p-4 rounded-md text-red-700 mb-4">
          {error}
        </div>
        <button
          onClick={() => router.push('/forums')}
          className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Forums
        </button>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">User Not Found</h1>
          <p className="mb-6">The user you're looking for doesn't exist or has deleted their account.</p>
          <button
            onClick={() => router.push('/forums')}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Return to Forums
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href="/forums" className="inline-flex items-center text-nature-green hover:underline">
          <FaArrowLeft className="mr-2" /> Back to Forums
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-4">
              {profile.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt={profile.full_name || profile.username}
                  className="h-full w-full object-cover"
                />
              ) : (
                <FaUser className="text-gray-400 text-2xl" />
              )}
            </div>
            <div>
              <div className="flex items-center">
                <h1 className="text-2xl font-bold">{profile.full_name || profile.username}</h1>
                {profile.is_verified_expert && <VerificationBadge className="ml-2" />}
              </div>
              <div className="flex items-center text-gray-600">
                <span>@{profile.username}</span>
                <UserLevelDisplay level={profile.level || 1} className="ml-2" />
              </div>
            </div>
          </div>

          <div className="flex items-center text-gray-600 mb-4">
            <FaComments className="mr-2" />
            <span>{posts.length} forum posts</span>
          </div>

          <Link href={`/profile/${profile.username}`} className="text-nature-green hover:underline">
            View Full Profile
          </Link>
        </div>
      </div>

      <h2 className="text-2xl font-bold mb-6">Forum Posts by {profile.username}</h2>

      {posts.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-8 text-center text-gray-500">
          <FaComments className="mx-auto text-4xl mb-4 text-gray-300" />
          <p>This user hasn't made any forum posts yet.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {posts.map((post) => (
            <div key={post.id} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
              <div className="p-5">
                <div className="mb-2">
                  <Link
                    href={`/forums/${post.topic.category.slug}/${post.topic.slug}`}
                    className="text-lg font-semibold text-nature-green hover:underline"
                  >
                    {post.topic.title}
                  </Link>
                  <div className="text-sm text-gray-500">
                    in <Link href={`/forums/${post.topic.category.slug}`} className="text-nature-green hover:underline">
                      {post.topic.category.name}
                    </Link> • {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
                  </div>
                </div>

                <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: truncateContent(post.content) }} />

                <div className="mt-3">
                  <Link
                    href={`/forums/${post.topic.category.slug}/${post.topic.slug}#post-${post.id}`}
                    className="text-nature-green hover:underline text-sm"
                  >
                    Read full post →
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {hasMore && (
        <div className="mt-6 text-center">
          <button
            onClick={loadMorePosts}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
            disabled={isLoading}
          >
            {isLoading ? 'Loading...' : 'Load More Posts'}
          </button>
        </div>
      )}
    </div>
  );
}
