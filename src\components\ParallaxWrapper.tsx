'use client';

import { useParallax } from '@/components/useParallax';

const ParallaxWrapper = ({ children }: { children: React.ReactNode }) => {
  const { x: parallaxX, y: parallaxY } = useParallax(30);

  return (
    <div
      style={{
        transform: `translate3d(${parallaxX}px, ${parallaxY}px, 0)`,
        transition: "transform 0.2s cubic-bezier(0.4,0,0.2,1)",
      }}
    >
      {children}
    </div>
  );
};

export default ParallaxWrapper;