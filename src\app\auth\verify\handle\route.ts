import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const token = requestUrl.searchParams.get('token');
  const type = requestUrl.searchParams.get('type');
  const error = requestUrl.searchParams.get('error');
  const errorCode = requestUrl.searchParams.get('error_code');
  const errorDescription = requestUrl.searchParams.get('error_description');
  
  // Handle errors
  if (error || errorCode || errorDescription) {
    return NextResponse.redirect(
      new URL(`/auth/verify/error?error=${encodeURIComponent(errorDescription || error || 'Unknown error')}&code=${encodeURIComponent(errorCode || '')}`, 
      requestUrl.origin)
    );
  }
  
  // If there's no token, redirect to the error page
  if (!token) {
    return NextResponse.redirect(
      new URL('/auth/verify/error?error=No verification token provided', requestUrl.origin)
    );
  }

  try {
    // Create a Supabase client
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify the token
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: type === 'signup' ? 'signup' : 'email',
    });
    
    if (error) {
      return NextResponse.redirect(
        new URL(`/auth/verify/error?error=${encodeURIComponent(error.message)}`, requestUrl.origin)
      );
    }
    
    // Redirect to success page
    return NextResponse.redirect(new URL('/auth/verify/success', requestUrl.origin));
  } catch (err: any) {
    console.error('Error in auth verification:', err);
    return NextResponse.redirect(
      new URL(`/auth/verify/error?error=${encodeURIComponent(err.message || 'An unexpected error occurred')}`, requestUrl.origin)
    );
  }
}
