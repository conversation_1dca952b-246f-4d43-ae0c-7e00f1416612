'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { getForumCategories, createTopic } from '@/lib/forums';
import { ForumCategory } from '@/lib/forum-types';
import Link from 'next/link';

// Main component that uses the wrapper
export default function CreateTopicPage() {
  return (
    <Suspense fallback={<div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Create New Topic</h1>
        <div className="bg-white rounded-xl shadow-md p-6 text-center">
          <p className="text-gray-500">Loading...</p>
        </div>
      </div>
    </div>}>
      <CreateTopicContent />
    </Suspense>
  );
}

// Content component that uses useSearchParams
function CreateTopicContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading } = useAuth();
  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [categoryId, setCategoryId] = useState('');

  useEffect(() => {
    async function checkAuthAndLoadCategories() {
      if (loading) return;

      if (!user) {
        router.push('/auth/signin?redirect=/forums/create-topic');
        return;
      }

      setIsLoading(true);

      try {
        const data = await getForumCategories();
        setCategories(data);

        // Set initial category from URL parameter if available
        const initialCategory = searchParams?.get('category');
        if (initialCategory) {
          setCategoryId(initialCategory);
        } else if (data.length > 0) {
          setCategoryId(data[0].id);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    }

    checkAuthAndLoadCategories();
  }, [user, loading, router, searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      router.push('/auth/signin?redirect=/forums/create-topic');
      return;
    }

    // Debug user information
    console.log('Current user:', {
      id: user.id,
      email: user.email,
      role: user.role,
      username: user.username
    });

    if (!title.trim() || !content.trim() || !categoryId) {
      setError('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      console.log('Submitting topic with user ID:', user.id);

      // Use the server-side API endpoint instead of the client-side function
      const response = await fetch('/api/forums/create-topic', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          content,
          categoryId,
          authorId: user.id
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create topic');
      }

      const { topic } = await response.json();

      // Get the category slug
      const category = categories.find(c => c.id === categoryId);

      // Redirect to the new topic
      router.push(`/forums/${category?.slug}/${topic.slug}`);
    } catch (err: any) {
      setError(err.message || 'Failed to create topic');
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Create New Topic</h1>
          <div className="bg-white rounded-xl shadow-md p-6 text-center">
            <p className="text-gray-500">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center mb-6">
          <Link href="/forums" className="text-nature-green hover:underline mr-2">
            Forums
          </Link>
          <span className="text-gray-500 mx-2">&gt;</span>
          <span className="text-gray-700">Create New Topic</span>
        </div>

        <h1 className="text-2xl font-bold mb-6">Create New Topic</h1>

        {error && (
          <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <div className="bg-white rounded-xl shadow-md p-6">
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category*
              </label>
              <select
                id="category"
                value={categoryId}
                onChange={(e) => setCategoryId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              >
                <option value="" disabled>Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Topic Title*
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                placeholder="Enter a descriptive title for your topic"
                required
              />
            </div>

            <div className="mb-6">
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                Content*
              </label>
              <textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                rows={10}
                placeholder="Write your post here..."
                required
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Link
                href="/forums"
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating...' : 'Create Topic'}
              </button>
            </div>
          </form>
        </div>

        <div className="mt-6 bg-blue-50 rounded-xl p-4">
          <h2 className="text-sm font-semibold text-blue-800 mb-2">Posting Guidelines</h2>
          <ul className="text-sm text-blue-700 list-disc list-inside space-y-1">
            <li>Be respectful and kind to other community members</li>
            <li>Stay on topic and post in the appropriate category</li>
            <li>Provide as much detail as possible in your post</li>
            <li>Use a clear, descriptive title</li>
            <li>Avoid making medical claims or giving medical advice</li>
            <li>Do not promote or sell products or services</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
