-- Add admin functions to bypass <PERSON><PERSON> for category operations

-- Function to update a category as admin (bypassing <PERSON>LS)
CREATE OR REPLACE FUNCTION public.admin_update_category(
  p_id UUID,
  p_name TEXT,
  p_slug TEXT,
  p_description TEXT DEFAULT NULL,
  p_parent_id UUID DEFAULT NULL,
  p_icon TEXT DEFAULT NULL
) RETURNS SETOF categories
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_user_role TEXT;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if the user exists and is an admin
  SELECT role INTO v_user_role
  FROM public.profiles
  WHERE id = v_user_id;
  
  IF v_user_role IS NULL OR v_user_role != 'admin' THEN
    RAISE EXCEPTION 'Unauthorized: Only admins can use this function';
  END IF;
  
  -- Update the category
  RETURN QUERY
  UPDATE public.categories
  SET
    name = p_name,
    slug = p_slug,
    description = p_description,
    parent_id = p_parent_id,
    icon = p_icon,
    updated_at = NOW()
  WHERE id = p_id
  RETURNING *;
END;
$$;

-- Function to create a category as admin (bypassing RLS)
CREATE OR REPLACE FUNCTION public.admin_create_category(
  p_name TEXT,
  p_slug TEXT,
  p_description TEXT DEFAULT NULL,
  p_parent_id UUID DEFAULT NULL,
  p_icon TEXT DEFAULT NULL
) RETURNS SETOF categories
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_user_role TEXT;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if the user exists and is an admin
  SELECT role INTO v_user_role
  FROM public.profiles
  WHERE id = v_user_id;
  
  IF v_user_role IS NULL OR v_user_role != 'admin' THEN
    RAISE EXCEPTION 'Unauthorized: Only admins can use this function';
  END IF;
  
  -- Insert the category
  RETURN QUERY
  INSERT INTO public.categories (
    name,
    slug,
    description,
    parent_id,
    icon,
    created_at,
    updated_at
  )
  VALUES (
    p_name,
    p_slug,
    p_description,
    p_parent_id,
    p_icon,
    NOW(),
    NOW()
  )
  RETURNING *;
END;
$$;

-- Function to delete a category as admin (bypassing RLS)
CREATE OR REPLACE FUNCTION public.admin_delete_category(
  p_id UUID
) RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_user_role TEXT;
  v_result BOOLEAN;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if the user exists and is an admin
  SELECT role INTO v_user_role
  FROM public.profiles
  WHERE id = v_user_id;
  
  IF v_user_role IS NULL OR v_user_role != 'admin' THEN
    RAISE EXCEPTION 'Unauthorized: Only admins can use this function';
  END IF;
  
  -- Check if category has child categories
  IF EXISTS (SELECT 1 FROM public.categories WHERE parent_id = p_id) THEN
    RAISE EXCEPTION 'Cannot delete a category that has child categories';
  END IF;
  
  -- Check if category has articles
  IF EXISTS (SELECT 1 FROM public.articles WHERE category_id = p_id) THEN
    RAISE EXCEPTION 'Cannot delete a category that has articles';
  END IF;
  
  -- Delete the category
  DELETE FROM public.categories WHERE id = p_id;
  
  -- Check if the delete was successful
  GET DIAGNOSTICS v_result = ROW_COUNT;
  
  RETURN v_result > 0;
END;
$$;

-- Function to execute SQL as admin (for emergency fixes)
CREATE OR REPLACE FUNCTION public.execute_sql(
  sql TEXT
) RETURNS VOID
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_user_role TEXT;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if the user exists and is an admin
  SELECT role INTO v_user_role
  FROM public.profiles
  WHERE id = v_user_id;
  
  IF v_user_role IS NULL OR v_user_role != 'admin' THEN
    RAISE EXCEPTION 'Unauthorized: Only admins can use this function';
  END IF;
  
  -- Execute the SQL
  EXECUTE sql;
END;
$$;

-- Function to check RLS status for a table
CREATE OR REPLACE FUNCTION public.check_rls_status(
  table_name TEXT
) RETURNS JSONB
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  v_user_id UUID;
  v_user_role TEXT;
  v_result JSONB;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if the user exists and is an admin
  SELECT role INTO v_user_role
  FROM public.profiles
  WHERE id = v_user_id;
  
  IF v_user_role IS NULL OR v_user_role != 'admin' THEN
    RAISE EXCEPTION 'Unauthorized: Only admins can use this function';
  END IF;
  
  -- Check RLS status
  SELECT jsonb_build_object(
    'table_name', t.tablename,
    'rls_enabled', t.rowsecurity,
    'policies', COALESCE(jsonb_agg(jsonb_build_object(
      'policy_name', p.policyname,
      'policy_cmd', p.cmd,
      'policy_roles', p.roles,
      'policy_qual', p.qual,
      'policy_with_check', p.with_check
    )) FILTER (WHERE p.policyname IS NOT NULL), '[]'::jsonb)
  ) INTO v_result
  FROM pg_tables t
  LEFT JOIN pg_policies p ON t.tablename = p.tablename AND t.schemaname = p.schemaname
  WHERE t.schemaname = 'public' AND t.tablename = table_name
  GROUP BY t.tablename, t.rowsecurity;
  
  RETURN v_result;
END;
$$;
