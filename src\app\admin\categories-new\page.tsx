'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import Link from 'next/link';

type Category = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  parent_id: string | null;
  icon?: string;
};

export default function AdminCategoriesPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    description: '',
    parentId: '',
    icon: ''
  });

  useEffect(() => {
    // Check if user is authenticated
    if (authLoading) return;

    if (!user) {
      router.push('/auth/signin?redirect=/admin/categories-new');
      return;
    }

    fetchCategories();
  }, [user, authLoading, router]);

  async function fetchCategories() {
    try {
      setLoading(true);
      setError('');

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/categories?t=${timestamp}`);

      if (!response.ok) {
        if (response.status === 401) {
          router.push('/auth/signin?redirect=/admin/categories-new');
          return;
        }

        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to load categories');
      }

      const data = await response.json();
      setCategories(data);
    } catch (err: any) {
      console.error('Failed to load categories:', err);
      setError(err.message || 'An unexpected error occurred while loading categories');
    } finally {
      setLoading(false);
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreateClick = () => {
    setFormData({
      id: '',
      name: '',
      description: '',
      parentId: '',
      icon: ''
    });
    setFormMode('create');
    setShowForm(true);
    setSelectedCategory(null);
  };

  const handleEditClick = (category: Category) => {
    setFormData({
      id: category.id,
      name: category.name,
      description: category.description || '',
      parentId: category.parent_id || '',
      icon: category.icon || ''
    });
    setFormMode('edit');
    setShowForm(true);
    setSelectedCategory(category);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!formData.name) {
      setError('Category name is required');
      return;
    }

    try {
      // Determine the action based on form mode
      const action = formMode === 'create' ? 'create' : 'update';
      
      // Prepare the request payload
      const payload = {
        action,
        id: formMode === 'edit' ? formData.id : undefined,
        name: formData.name,
        description: formData.description || null,
        parentId: formData.parentId || null,
        icon: formData.icon || null
      };
      
      // Send the request to our category manager API
      const response = await fetch('/api/admin/category-manager', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} category`);
      }
      
      const data = await response.json();
      
      // Show success message
      setSuccess(`Category "${formData.name}" ${formMode === 'create' ? 'created' : 'updated'} successfully`);
      
      // Reset form and refresh categories
      setShowForm(false);
      fetchCategories();
    } catch (err: any) {
      console.error(`Error ${formMode === 'create' ? 'creating' : 'updating'} category:`, err);
      setError(err.message || 'An unexpected error occurred');
    }
  };

  const handleDelete = async (categoryId: string, categoryName: string) => {
    if (!window.confirm(`Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setError('');
      setSuccess('');
      
      // Send delete request to our category manager API
      const response = await fetch('/api/admin/category-manager', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'delete',
          id: categoryId
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete category');
      }
      
      // Show success message
      setSuccess(`Category "${categoryName}" deleted successfully`);
      
      // Refresh categories
      fetchCategories();
      
      // If the form was showing the deleted category, hide it
      if (selectedCategory?.id === categoryId) {
        setShowForm(false);
      }
    } catch (err: any) {
      console.error('Error deleting category:', err);
      setError(err.message || 'An unexpected error occurred');
    }
  };

  // Function to render icon based on name
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'leaf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      case 'sparkles':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
          </svg>
        );
    }
  };

  if (loading && categories.length === 0) {
    return (
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Categories Management</h1>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <p className="text-gray-500">Loading categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Categories Management</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleCreateClick}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Create New Category
          </button>
          <Link
            href="/admin"
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Back to Admin
          </Link>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">All Categories</h2>
            
            {categories.length === 0 ? (
              <p className="text-gray-500">No categories found. Create your first category!</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Slug
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Parent
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {categories.map((category) => {
                      const parentCategory = categories.find(c => c.id === category.parent_id);
                      
                      return (
                        <tr key={category.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {category.icon && (
                                <div className="flex-shrink-0 h-8 w-8 mr-2 text-green-600">
                                  {renderIcon(category.icon)}
                                </div>
                              )}
                              <div className="text-sm font-medium text-gray-900">
                                {category.name}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {category.slug}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {parentCategory ? parentCategory.name : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => handleEditClick(category)}
                              className="text-indigo-600 hover:text-indigo-900 mr-3"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDelete(category.id, category.name)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {showForm && (
          <div className="lg:col-span-1">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-xl font-semibold mb-4">
                {formMode === 'create' ? 'Create New Category' : 'Edit Category'}
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Category Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter category name"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter category description"
                  ></textarea>
                </div>
                
                <div>
                  <label htmlFor="parentId" className="block text-sm font-medium text-gray-700 mb-1">
                    Parent Category (optional)
                  </label>
                  <select
                    id="parentId"
                    name="parentId"
                    value={formData.parentId}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="">None (Top-level category)</option>
                    {categories
                      .filter(category => category.id !== formData.id) // Prevent setting itself as parent
                      .map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-1">
                    Category Icon (optional)
                  </label>
                  <select
                    id="icon"
                    name="icon"
                    value={formData.icon}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="">Select an icon</option>
                    <option value="leaf">Leaf</option>
                    <option value="sparkles">Sparkles</option>
                    <option value="cake">Cake</option>
                    <option value="beaker">Beaker</option>
                    <option value="fire">Fire</option>
                    <option value="academic-cap">Academic Cap</option>
                    <option value="sun">Sun</option>
                    <option value="moon">Moon</option>
                    <option value="droplet">Droplet</option>
                    <option value="globe">Globe</option>
                    <option value="map">Map</option>
                    <option value="home">Home</option>
                    <option value="collection">Collection</option>
                    <option value="shopping-cart">Shopping Cart</option>
                    <option value="color-swatch">Color Swatch</option>
                    <option value="heart">Heart</option>
                    <option value="lightning-bolt">Lightning Bolt</option>
                  </select>
                  
                  {formData.icon && (
                    <div className="mt-2 p-2 bg-gray-50 rounded-md inline-block">
                      <div className="text-xs text-gray-500 mb-1">Icon Preview:</div>
                      <div className="bg-green-100 p-2 rounded-full w-10 h-10 flex items-center justify-center text-green-600">
                        {renderIcon(formData.icon)}
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    {formMode === 'create' ? 'Create Category' : 'Update Category'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
