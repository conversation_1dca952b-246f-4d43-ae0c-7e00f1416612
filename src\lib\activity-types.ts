export type ActivityType = 
  | 'forum_topic_create'
  | 'forum_post_create'
  | 'forum_reply_create'
  | 'forum_post_reaction'
  | 'forum_solution_marked'
  | 'article_create'
  | 'article_edit'
  | 'article_comment'
  | 'level_up'
  | 'badge_earned'
  | 'verification_approved';

export type ContentType = 
  | 'forum_topic'
  | 'forum_post'
  | 'forum_post_reaction'
  | 'article'
  | 'comment'
  | 'profile'
  | 'expert_verification';

export interface UserActivity {
  id: string;
  user_id: string;
  activity_type: ActivityType;
  content_type: ContentType;
  content_id: string;
  metadata?: any;
  created_at: string;
  user?: {
    username: string;
    full_name?: string;
    avatar_url?: string;
  };
}

export const ACTIVITY_ICONS = {
  forum_topic_create: '📝',
  forum_post_create: '💬',
  forum_reply_create: '↩️',
  forum_post_reaction: '👍',
  forum_solution_marked: '✅',
  article_create: '📄',
  article_edit: '✏️',
  article_comment: '💭',
  level_up: '⬆️',
  badge_earned: '🏆',
  verification_approved: '🏅'
};

export const ACTIVITY_MESSAGES = {
  forum_topic_create: 'created a new topic',
  forum_post_create: 'posted in a topic',
  forum_reply_create: 'replied to a post',
  forum_post_reaction: 'reacted to a post',
  forum_solution_marked: 'had their post marked as a solution',
  article_create: 'created a new article',
  article_edit: 'edited an article',
  article_comment: 'commented on an article',
  level_up: 'leveled up',
  badge_earned: 'earned a badge',
  verification_approved: 'became a verified expert'
};
