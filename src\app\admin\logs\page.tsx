'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type LogEntry = {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  source: string;
  message: string;
  user_id?: string;
  user_email?: string;
  details?: any;
};

export default function AdminLogs() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filter, setFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    async function checkAuth() {
      if (loading) return;
      
      if (!user) {
        router.push('/auth/signin');
        return;
      }
      
      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (error || !profile || profile.role !== 'admin') {
        router.push('/');
        return;
      }
      
      setIsAdmin(true);
      
      // Generate mock logs since we don't have a real logs table yet
      const mockLogs: LogEntry[] = [
        {
          id: '1',
          timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
          level: 'info',
          source: 'auth',
          message: 'User logged in',
          user_id: user.id,
          user_email: user.email
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 30 * 60000).toISOString(),
          level: 'warning',
          source: 'content',
          message: 'Failed to upload image: file size too large',
          user_id: user.id,
          user_email: user.email,
          details: { fileSize: '12MB', maxSize: '5MB' }
        },
        {
          id: '3',
          timestamp: new Date(Date.now() - 2 * 3600000).toISOString(),
          level: 'error',
          source: 'database',
          message: 'Database connection error',
          details: { error: 'Connection timeout' }
        },
        {
          id: '4',
          timestamp: new Date(Date.now() - 6 * 3600000).toISOString(),
          level: 'info',
          source: 'system',
          message: 'System backup completed successfully'
        },
        {
          id: '5',
          timestamp: new Date(Date.now() - 12 * 3600000).toISOString(),
          level: 'info',
          source: 'auth',
          message: 'New user registered',
          user_email: '<EMAIL>'
        },
        {
          id: '6',
          timestamp: new Date(Date.now() - 24 * 3600000).toISOString(),
          level: 'warning',
          source: 'security',
          message: 'Multiple failed login attempts',
          user_email: '<EMAIL>',
          details: { attempts: 5, ip: '***********' }
        },
        {
          id: '7',
          timestamp: new Date(Date.now() - 2 * 86400000).toISOString(),
          level: 'error',
          source: 'api',
          message: 'API rate limit exceeded',
          user_id: '123456',
          user_email: '<EMAIL>',
          details: { endpoint: '/api/articles', limit: '100/hour' }
        }
      ];
      
      setLogs(mockLogs);
      setTotalPages(Math.ceil(mockLogs.length / 5));
      setIsLoading(false);
    }
    
    checkAuth();
  }, [user, loading, router]);

  const filteredLogs = logs.filter(log => {
    if (filter === 'all') return true;
    return log.level === filter;
  });

  const paginatedLogs = filteredLogs.slice((page - 1) * 5, page * 5);

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilter(e.target.value);
    setPage(1);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (!isAdmin || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">System Logs</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <label htmlFor="filter" className="mr-2 text-sm font-medium text-gray-700">
              Filter by level:
            </label>
            <select
              id="filter"
              value={filter}
              onChange={handleFilterChange}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
            >
              <option value="all">All Levels</option>
              <option value="info">Info</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
            </select>
          </div>
          
          <button
            onClick={() => {
              // In a real app, this would download the logs
              alert('Download logs functionality would be implemented here');
            }}
            className="px-3 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Download Logs
          </button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Level
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Message
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedLogs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(log.timestamp)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getLevelColor(log.level)}`}>
                      {log.level.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.source}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {log.message}
                    {log.details && (
                      <details className="mt-1">
                        <summary className="text-xs text-gray-500 cursor-pointer">Details</summary>
                        <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                          {JSON.stringify(log.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.user_email || '-'}
                  </td>
                </tr>
              ))}
              
              {paginatedLogs.length === 0 && (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    No logs found matching the current filter.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        
        {filteredLogs.length > 5 && (
          <div className="mt-4 flex justify-between items-center">
            <div className="text-sm text-gray-700">
              Showing {(page - 1) * 5 + 1} to {Math.min(page * 5, filteredLogs.length)} of {filteredLogs.length} logs
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(page - 1)}
                disabled={page === 1}
                className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(page + 1)}
                disabled={page === totalPages}
                className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
