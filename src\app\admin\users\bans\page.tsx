'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { getBannedUsers, unbanUser } from '@/lib/forums';
import { format, formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import {
  FaUser, FaUnlock, FaFilter, FaCalendarAlt, FaArrowLeft,
  FaPlus, FaDownload, FaCheck, FaTimes
} from 'react-icons/fa';

export default function UserBansPage() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);
  const [bannedUsers, setBannedUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalBans, setTotalBans] = useState(0);
  const [filters, setFilters] = useState({
    includeExpired: false
  });
  const [showFilters, setShowFilters] = useState(false);
  const [unbanReason, setUnbanReason] = useState('');
  const [userToUnban, setUserToUnban] = useState(null);
  const [showUnbanModal, setShowUnbanModal] = useState(false);
  const itemsPerPage = 20;

  const { user, loading } = useAuth();

  useEffect(() => {
    async function checkAuth() {
      if (loading) return; // Wait for auth to load

      if (!user) {
        // Redirect to sign in if no user
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin or moderator
      const supabase = createClientComponentClient();
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'moderator')) {
        // Redirect to unauthorized page if not admin/moderator
        setUnauthorized(true);
        router.push('/');
        return;
      }

      setAuthChecked(true);

      // Load banned users
      loadBannedUsers();
    }

    checkAuth();
  }, [router, user, loading, page, filters]);

  async function loadBannedUsers() {
    try {
      setIsLoading(true);
      
      const { data, count } = await getBannedUsers({
        limit: itemsPerPage,
        offset: (page - 1) * itemsPerPage,
        includeExpired: filters.includeExpired
      });
      
      setBannedUsers(data || []);
      setTotalBans(count || 0);
      setTotalPages(Math.ceil((count || 0) / itemsPerPage));
    } catch (err) {
      console.error('Error loading banned users:', err);
      setError('Failed to load banned users');
    } finally {
      setIsLoading(false);
    }
  }

  function handleFilterChange(e) {
    const { name, checked } = e.target;
    setFilters(prev => ({ ...prev, [name]: checked }));
  }

  function applyFilters() {
    setPage(1); // Reset to first page when applying filters
    loadBannedUsers();
  }

  function resetFilters() {
    setFilters({
      includeExpired: false
    });
    setPage(1);
    loadBannedUsers();
  }

  function exportToCSV() {
    // Create CSV content
    const headers = ['Username', 'Email', 'Ban Type', 'Reason', 'Expires', 'Banned By', 'Ban Date'];
    const csvRows = [headers.join(',')];
    
    bannedUsers.forEach(ban => {
      const row = [
        `"${(ban.user?.username || 'Unknown').replace(/"/g, '""')}"`,
        `"${(ban.user?.email || 'Unknown').replace(/"/g, '""')}"`,
        ban.ban_type,
        `"${(ban.reason || '').replace(/"/g, '""')}"`,
        ban.expires_at ? format(new Date(ban.expires_at), 'yyyy-MM-dd') : 'Never',
        `"${(ban.moderator?.username || 'Unknown').replace(/"/g, '""')}"`,
        format(new Date(ban.created_at), 'yyyy-MM-dd HH:mm:ss')
      ];
      csvRows.push(row.join(','));
    });
    
    const csvContent = csvRows.join('\n');
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `banned-users-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  function openUnbanModal(bannedUser) {
    setUserToUnban(bannedUser);
    setUnbanReason('');
    setShowUnbanModal(true);
  }

  function closeUnbanModal() {
    setShowUnbanModal(false);
    setUserToUnban(null);
    setUnbanReason('');
  }

  async function handleUnban() {
    if (!userToUnban || !unbanReason.trim()) {
      return;
    }
    
    try {
      setIsLoading(true);
      
      const result = await unbanUser({
        userId: userToUnban.user_id,
        moderatorId: user.id,
        reason: unbanReason
      });
      
      if (result.success) {
        setSuccess(`User ${userToUnban.user?.username || userToUnban.user_id} has been unbanned`);
        closeUnbanModal();
        loadBannedUsers();
        
        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(result.message || 'Failed to unban user');
      }
    } catch (err) {
      console.error('Error unbanning user:', err);
      setError(err.message || 'Failed to unban user');
    } finally {
      setIsLoading(false);
    }
  }

  // Helper function to format ban type
  const formatBanType = (banType) => {
    switch (banType) {
      case 'full':
        return 'Full Ban';
      case 'post_only':
        return 'Post Only';
      case 'read_only':
        return 'Read Only';
      default:
        return banType;
    }
  };

  if (unauthorized) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-600 font-bold text-xl mb-4">Unauthorized Access</div>
        <p className="text-gray-600 mb-4">You don't have permission to access the admin area.</p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Return to Homepage
        </button>
      </div>
    );
  }

  if (!authChecked || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
        <span className="ml-3 text-lg">Loading banned users...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <button
            onClick={() => router.push('/admin/users')}
            className="mr-4 text-nature-green hover:underline flex items-center"
          >
            <FaArrowLeft className="mr-1" />
            Back to Users
          </button>
          <h1 className="text-3xl font-bold">User Bans</h1>
        </div>
        <div className="flex space-x-2">
          <Link
            href="/admin/users/bans/create"
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
          >
            <FaPlus className="mr-2" />
            Ban User
          </Link>
          <button
            onClick={exportToCSV}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <FaDownload className="mr-2" />
            Export CSV
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-lg border border-green-100">
          <p className="font-medium">Success</p>
          <p>{success}</p>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Filters</h2>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-nature-green hover:underline flex items-center"
          >
            <FaFilter className="mr-1" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
        </div>

        {showFilters && (
          <div className="mb-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeExpired"
                name="includeExpired"
                checked={filters.includeExpired}
                onChange={handleFilterChange}
                className="h-4 w-4 text-nature-green focus:ring-nature-green border-gray-300 rounded"
              />
              <label htmlFor="includeExpired" className="ml-2 block text-sm text-gray-700">
                Include expired bans
              </label>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-2">
          <button
            onClick={resetFilters}
            className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reset
          </button>
          <button
            onClick={applyFilters}
            className="px-3 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <span className="text-gray-500">Total Bans:</span>
            <span className="ml-2 font-semibold">{totalBans}</span>
          </div>
          <div>
            <span className="text-gray-500">Page:</span>
            <span className="ml-2 font-semibold">{page} of {totalPages}</span>
          </div>
        </div>
      </div>

      {/* Banned Users Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ban Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Banned By
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ban Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expires
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {bannedUsers.length > 0 ? (
                bannedUsers.map((ban) => {
                  const isExpired = ban.expires_at && new Date(ban.expires_at) < new Date();
                  
                  return (
                    <tr key={ban.id} className={`hover:bg-gray-50 ${isExpired ? 'bg-gray-50 text-gray-400' : ''}`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {ban.user?.avatar_url ? (
                              <img
                                className="h-10 w-10 rounded-full"
                                src={ban.user.avatar_url}
                                alt={ban.user.username}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <FaUser className="text-gray-500" />
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {ban.user?.username || 'Unknown'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {ban.user?.email || 'No email'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          ban.ban_type === 'full' 
                            ? 'bg-red-100 text-red-800' 
                            : ban.ban_type === 'post_only'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-blue-100 text-blue-800'
                        }`}>
                          {formatBanType(ban.ban_type)}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {ban.reason || 'No reason provided'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {ban.moderator?.username || 'Unknown'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {format(new Date(ban.created_at), 'MMM d, yyyy')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {ban.expires_at ? (
                          <div className="text-sm text-gray-500">
                            {format(new Date(ban.expires_at), 'MMM d, yyyy')}
                            {isExpired && (
                              <span className="ml-2 text-xs text-red-500">Expired</span>
                            )}
                          </div>
                        ) : (
                          <div className="text-sm font-medium text-red-600">Never</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => openUnbanModal(ban)}
                          className="text-nature-green hover:text-green-700 flex items-center justify-end"
                        >
                          <FaUnlock className="mr-1" />
                          Unban
                        </button>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    No banned users found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="flex items-center space-x-2">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-3 py-1 rounded-md ${
                page === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>
            
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 2 + i;
              }
              
              return (
                <button
                  key={pageNum}
                  onClick={() => setPage(pageNum)}
                  className={`px-3 py-1 rounded-md ${
                    page === pageNum
                      ? 'bg-nature-green text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
            
            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-3 py-1 rounded-md ${
                page === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Next
            </button>
          </nav>
        </div>
      )}

      {/* Unban Modal */}
      {showUnbanModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-bold mb-4">Unban User</h3>
            
            <p className="mb-4">
              Are you sure you want to unban <strong>{userToUnban?.user?.username || 'this user'}</strong>?
            </p>
            
            <div className="mb-4">
              <label htmlFor="unbanReason" className="block text-sm font-medium text-gray-700 mb-1">
                Reason for unbanning *
              </label>
              <textarea
                id="unbanReason"
                value={unbanReason}
                onChange={(e) => setUnbanReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nature-green"
                required
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeUnbanModal}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center"
              >
                <FaTimes className="mr-2" />
                Cancel
              </button>
              <button
                onClick={handleUnban}
                disabled={!unbanReason.trim() || isLoading}
                className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors flex items-center"
              >
                <FaCheck className="mr-2" />
                {isLoading ? 'Processing...' : 'Confirm Unban'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
