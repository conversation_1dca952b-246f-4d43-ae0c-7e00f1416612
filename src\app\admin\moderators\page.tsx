'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/components/AuthProvider';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

type Moderator = {
  id: string;
  username: string;
  full_name: string;
  email: string;
  role: 'moderator' | 'admin';
  created_at: string;
  last_active?: string;
  activity_count?: number;
};

export default function AdminModeratorsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [moderators, setModerators] = useState<Moderator[]>([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [demotingUserId, setDemotingUserId] = useState<string | null>(null);

  useEffect(() => {
    async function checkAuth() {
      if (authLoading) return;

      if (!user) {
        router.push('/auth/signin');
        return;
      }

      // Check if user is admin
      const supabase = createClientComponentClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (error || !profile || profile.role !== 'admin') {
        router.push('/');
        return;
      }

      setIsAdmin(true);
      setIsLoading(false);
      loadModerators();
    }

    checkAuth();
  }, [user, authLoading, router]);

  async function loadModerators() {
    try {
      setIsLoading(true);

      const supabase = createClientComponentClient();

      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, full_name, role, created_at')
        .or('role.eq.moderator,role.eq.admin')
        .order('role', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) {
        setError('Failed to load moderators: ' + error.message);
        return;
      }

      // Format the data and add mock activity data
      const formattedData = data.map(item => {
        const lastActive = new Date();
        lastActive.setHours(lastActive.getHours() - Math.floor(Math.random() * 72));

        return {
          id: item.id,
          username: item.username || '',
          full_name: item.full_name || '',
          email: `${item.username || 'user'}@example.com`, // Placeholder email
          role: item.role as 'moderator' | 'admin',
          created_at: item.created_at,
          last_active: lastActive.toISOString(),
          activity_count: Math.floor(Math.random() * 100)
        };
      });

      setModerators(formattedData);
    } catch (err) {
      console.error('Error loading moderators:', err);
      setError('An unexpected error occurred while loading moderators');
    } finally {
      setIsLoading(false);
    }
  }

  const handleDemoteUser = async (userId: string) => {
    try {
      setDemotingUserId(userId);
      setError('');
      setSuccess('');

      const supabase = createClientComponentClient();

      const { error } = await supabase
        .from('profiles')
        .update({ role: 'user' })
        .eq('id', userId);

      if (error) {
        setError(`Failed to demote user: ${error.message}`);
        return;
      }

      // Remove the user from the list
      setModerators(prevModerators =>
        prevModerators.filter(mod => mod.id !== userId)
      );

      setSuccess('User has been demoted to regular user');
    } catch (err) {
      console.error('Error demoting user:', err);
      setError('An unexpected error occurred while demoting user');
    } finally {
      setDemotingUserId(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAdmin || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Moderator Management</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md border border-green-200">
          {success}
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Moderators & Admins</h2>
          <button
            onClick={() => router.push('/admin/users')}
            className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Manage All Users
          </button>
        </div>

        {moderators.length === 0 ? (
          <p className="text-gray-500">No moderators or admins found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Active
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Activity
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {moderators.map(moderator => (
                  <tr key={moderator.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-gray-500 font-medium">
                            {moderator.username.substring(0, 2).toUpperCase()}
                          </span>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{moderator.full_name}</div>
                          <div className="text-sm text-gray-500">@{moderator.username}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{moderator.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        moderator.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                      }`}>
                        {moderator.role.charAt(0).toUpperCase() + moderator.role.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{formatDate(moderator.created_at)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{moderator.last_active ? formatDateTime(moderator.last_active) : 'Never'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{moderator.activity_count} actions</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end items-center space-x-2">
                        <button
                          onClick={() => router.push(`/admin/users/${moderator.id}`)}
                          className="text-nature-green hover:text-nature-green-dark"
                        >
                          View
                        </button>
                        {moderator.role !== 'admin' && (
                          <button
                            onClick={() => handleDemoteUser(moderator.id)}
                            disabled={demotingUserId === moderator.id}
                            className="text-red-600 hover:text-red-800 disabled:text-gray-400"
                          >
                            {demotingUserId === moderator.id ? 'Demoting...' : 'Demote'}
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Add New Moderator</h2>
        <p className="text-gray-600 mb-4">
          To add a new moderator, go to the <Link href="/admin/users" className="text-nature-green hover:underline">User Management</Link> page and change a user&apos;s role to &quot;Moderator&quot;.
        </p>
        <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
          <h3 className="text-blue-800 font-medium mb-2">Moderator Permissions</h3>
          <ul className="list-disc list-inside text-blue-700 space-y-1">
            <li>Review and approve user-submitted content</li>
            <li>Edit and update wiki articles</li>
            <li>Manage comments and community discussions</li>
            <li>Flag inappropriate content for admin review</li>
            <li>Cannot access system settings or user management</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
