'use client';

import React, { useState, useEffect } from 'react';
import { getUserActivities, getGlobalActivityFeed, getActivityDetails } from '@/lib/activities';
import { UserActivity, ACTIVITY_ICONS, ACTIVITY_MESSAGES } from '@/lib/activity-types';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import UserLevelDisplay from '@/components/UserLevelDisplay';
import VerificationBadge from '@/components/VerificationBadge';

interface ActivityFeedProps {
  userId?: string;
  limit?: number;
  showLoadMore?: boolean;
  className?: string;
  emptyMessage?: string;
  title?: string;
}

export default function ActivityFeed({
  userId,
  limit = 10,
  showLoadMore = true,
  className = '',
  emptyMessage = 'No activity yet',
  title
}: ActivityFeedProps) {
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [activityDetails, setActivityDetails] = useState<{[key: string]: any}>({});
  
  useEffect(() => {
    loadActivities();
  }, [userId, page]);
  
  const loadActivities = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, count } = userId 
        ? await getUserActivities({
            userId,
            limit,
            offset: (page - 1) * limit
          })
        : await getGlobalActivityFeed({
            limit,
            offset: (page - 1) * limit
          });
      
      if (page === 1) {
        setActivities(data);
      } else {
        setActivities(prev => [...prev, ...data]);
      }
      
      setHasMore(count > page * limit);
      
      // Load details for each activity
      const details: {[key: string]: any} = {};
      for (const activity of data) {
        const detail = await getActivityDetails(activity);
        if (detail) {
          details[activity.id] = detail;
        }
      }
      
      setActivityDetails(prev => ({...prev, ...details}));
    } catch (err: any) {
      setError(err.message || 'Failed to load activities');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleLoadMore = () => {
    setPage(prev => prev + 1);
  };
  
  const getActivityLink = (activity: UserActivity) => {
    const details = activityDetails[activity.id];
    
    if (!details) return '#';
    
    switch (activity.content_type) {
      case 'forum_topic':
        return `/forums/${details.category?.slug}/${details.slug}`;
      
      case 'forum_post':
        return `/forums/${details.topic?.category?.slug}/${details.topic?.slug}#post-${activity.content_id}`;
      
      case 'article':
        return `/wiki/${details.slug}`;
      
      case 'comment':
        return `/wiki/${details.article?.slug}#comment-${activity.content_id}`;
      
      case 'profile':
        return `/profile/${activity.user?.username}`;
      
      default:
        return '#';
    }
  };
  
  const getActivityContent = (activity: UserActivity) => {
    const details = activityDetails[activity.id];
    const activityType = activity.activity_type as keyof typeof ACTIVITY_MESSAGES;
    const message = ACTIVITY_MESSAGES[activityType] || 'did something';
    
    if (!details) return message;
    
    switch (activity.content_type) {
      case 'forum_topic':
        return `${message}: "${details.title}"`;
      
      case 'forum_post':
        return `${message} in "${details.topic?.title}"`;
      
      case 'article':
        return `${message}: "${details.title}"`;
      
      case 'comment':
        return `${message} on "${details.article?.title}"`;
      
      case 'profile':
        if (activity.activity_type === 'level_up' && activity.metadata) {
          return `${message} to level ${activity.metadata.new_level}`;
        }
        return message;
      
      default:
        return message;
    }
  };
  
  return (
    <div className={`${className}`}>
      {title && (
        <h2 className="text-lg font-semibold mb-4">{title}</h2>
      )}
      
      {error && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-md mb-4">
          {error}
        </div>
      )}
      
      {isLoading && activities.length === 0 ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : activities.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <p className="text-gray-500 dark:text-gray-400">{emptyMessage}</p>
        </div>
      ) : (
        <div className="space-y-4">
          {activities.map((activity) => {
            const activityType = activity.activity_type as keyof typeof ACTIVITY_ICONS;
            const icon = ACTIVITY_ICONS[activityType] || '📝';
            
            return (
              <div key={activity.id} className="flex items-start p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center text-lg">
                    {icon}
                  </div>
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center">
                    <Link href={`/profile/${activity.user?.username}`} className="font-medium text-nature-green hover:underline flex items-center">
                      {activity.user?.full_name || activity.user?.username}
                      {activity.user?.is_verified_expert && (
                        <VerificationBadge isVerified={true} size="sm" className="ml-1" />
                      )}
                    </Link>
                    
                    <span className="mx-1 text-gray-500 dark:text-gray-400">
                      {getActivityContent(activity)}
                    </span>
                  </div>
                  
                  <Link href={getActivityLink(activity)} className="mt-1 text-gray-700 dark:text-gray-300 hover:text-nature-green dark:hover:text-nature-green block">
                    {activityDetails[activity.id]?.content && (
                      <p className="text-sm line-clamp-2">
                        {activityDetails[activity.id].content.length > 150
                          ? activityDetails[activity.id].content.substring(0, 150) + '...'
                          : activityDetails[activity.id].content}
                      </p>
                    )}
                  </Link>
                  
                  <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                  </div>
                </div>
              </div>
            );
          })}
          
          {showLoadMore && hasMore && (
            <div className="text-center pt-4">
              <button
                onClick={handleLoadMore}
                disabled={isLoading}
                className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
