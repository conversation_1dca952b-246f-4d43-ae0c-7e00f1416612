# Installing New Features

This guide will help you install and configure the new features added to NatureHeals.info.

## Prerequisites

- Existing NatureHeals.info installation
- Node.js 18+ and npm
- Supabase project with admin access

## Installation Steps

### 1. Update Dependencies

First, update your project dependencies:

```bash
npm install chart.js@^4.4.1 date-fns@^3.6.0 react-chartjs-2@^5.2.0 react-intersection-observer@^9.8.1 remark@^15.0.1 remark-html@^16.0.1 unified@^11.0.4
```

### 2. Apply Database Migrations

Run the migration script to add the necessary database tables and functions:

```bash
npm run apply-migrations
```

This will create the following tables:
- `bookmarks` - For user bookmarks
- `notifications` - For user notifications
- `user_activities` - For tracking user activity
- `user_preferences` - For user settings (including dark mode)
- `analytics_events` - For tracking site analytics

### 3. Update Tailwind Configuration

Make sure your `tailwind.config.js` is properly configured:

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
  // Your configuration
};
```

### 4. Add Components to Layout

Update your main layout to include:
- `NotificationsDropdown` - For displaying notifications

### 5. Test the Installation

1. Start your development server:
```bash
npm run dev
```

2. Check that the following features are working:
   - Bookmarking articles
   - Social sharing buttons
   - Notifications dropdown (if logged in)
   - User activity feed
   - Admin analytics dashboard (if admin)

## Troubleshooting



### Database Migrations Failed
- Check Supabase console for error messages
- Verify that your service role key has the necessary permissions
- Try running the SQL migrations manually in the Supabase SQL editor

## Support

If you encounter any issues, please open an issue on the GitHub repository or contact the development team.
