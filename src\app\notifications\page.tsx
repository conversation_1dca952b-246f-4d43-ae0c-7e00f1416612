'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow, format } from 'date-fns';
import Link from 'next/link';
import { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '@/lib/notifications';
import { Notification, NOTIFICATION_ICONS, NOTIFICATION_COLORS } from '@/lib/notification-types';

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalNotifications, setTotalNotifications] = useState(0);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  const notificationsPerPage = 20;

  useEffect(() => {
    // Redirect if not logged in
    if (!authLoading && !user) {
      router.push('/auth/signin?redirect=/notifications');
      return;
    }

    loadNotifications();
  }, [user, authLoading, router, currentPage, filter]);

  const loadNotifications = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const { data, count } = await getUserNotifications({
        userId: user.id,
        limit: notificationsPerPage,
        offset: (currentPage - 1) * notificationsPerPage,
        unreadOnly: filter === 'unread'
      });

      setNotifications(data);
      setTotalNotifications(count || 0);
    } catch (err: any) {
      setError(err.message || 'Failed to load notifications');
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user) return;

    try {
      await markAllNotificationsAsRead(user.id);

      // Update local state
      setNotifications(prev => prev.map(n => ({ ...n, is_read: true })));

      // Reload notifications if filtering by unread
      if (filter === 'unread') {
        loadNotifications();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to mark all notifications as read');
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    try {
      if (!notification.is_read) {
        await markNotificationAsRead(notification.id);

        // Update local state
        setNotifications(prev =>
          prev.map(n => n.id === notification.id ? { ...n, is_read: true } : n)
        );
      }

      // Navigate to link if provided
      if (notification.link) {
        router.push(notification.link);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to mark notification as read');
    }
  };

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const totalPages = Math.ceil(totalNotifications / notificationsPerPage);

  // Group notifications by date
  const groupNotificationsByDate = () => {
    const groups: { [key: string]: Notification[] } = {};

    notifications.forEach(notification => {
      const date = new Date(notification.created_at);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let groupKey;

      if (date.toDateString() === today.toDateString()) {
        groupKey = 'Today';
      } else if (date.toDateString() === yesterday.toDateString()) {
        groupKey = 'Yesterday';
      } else {
        groupKey = format(date, 'MMMM d, yyyy');
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }

      groups[groupKey].push(notification);
    });

    return groups;
  };

  const notificationGroups = groupNotificationsByDate();

  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Notifications</h1>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label htmlFor="filter" className="text-sm text-gray-600 dark:text-gray-400">
              Show:
            </label>
            <select
              id="filter"
              value={filter}
              onChange={(e) => setFilter(e.target.value as 'all' | 'unread')}
              className="text-sm border border-gray-300 dark:border-gray-700 rounded-md px-2 py-1 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-nature-green"
            >
              <option value="all">All</option>
              <option value="unread">Unread</option>
            </select>
          </div>

          {notifications.some(n => !n.is_read) && (
            <button
              onClick={handleMarkAllAsRead}
              className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
            >
              Mark all as read
            </button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[40vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nature-green"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-md">
          {error}
        </div>
      ) : notifications.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 dark:text-gray-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
          <h2 className="text-xl font-semibold mt-4">No notifications yet</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2 mb-6">
            You'll receive notifications when someone interacts with your content or mentions you.
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors"
          >
            Back to Home
          </Link>
        </div>
      ) : (
        <div className="space-y-8">
          {Object.entries(notificationGroups).map(([date, notifications]) => (
            <div key={date}>
              <h2 className="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">{date}</h2>
              <div className="space-y-4">
                {notifications.map((notification) => {
                  const notificationType = notification.notification_type as keyof typeof NOTIFICATION_ICONS;
                  const icon = NOTIFICATION_ICONS[notificationType] || '📣';
                  const colorClass = NOTIFICATION_COLORS[notificationType] || 'bg-gray-100 text-gray-800 border-gray-200';

                  return (
                    <div
                      key={notification.id}
                      className={`flex items-start p-4 rounded-lg border ${
                        notification.is_read
                          ? 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
                          : 'border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20'
                      }`}
                    >
                      <div className="mr-4">
                        <div className={`flex items-center justify-center w-10 h-10 rounded-full ${colorClass.replace('bg-', 'bg-').replace('text-', 'text-')}`}>
                          <span className="text-lg">{icon}</span>
                        </div>
                      </div>
                      <div className="flex-1">
                        <div
                          className="text-gray-800 dark:text-gray-200 cursor-pointer"
                          onClick={() => handleNotificationClick(notification)}
                        >
                          {notification.message}
                        </div>
                        {notification.sender && (
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            From: {notification.sender.full_name || notification.sender.username}
                          </div>
                        )}
                        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1" title={format(new Date(notification.created_at), 'PPpp')}>
                          {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                        </div>
                      </div>
                      {!notification.is_read && (
                        <div className="ml-2 flex-shrink-0">
                          <span className="inline-block w-3 h-3 bg-blue-500 rounded-full"></span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="inline-flex rounded-md shadow">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-1 rounded-l-md border border-gray-300 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`px-3 py-1 border border-gray-300 dark:border-gray-700 text-sm font-medium ${
                    currentPage === pageNum
                      ? 'bg-nature-green text-white'
                      : 'bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  } ${i === 0 ? '' : 'border-l-0'}`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-1 rounded-r-md border border-gray-300 dark:border-gray-700 border-l-0 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </nav>
        </div>
      )}

      <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Notification Settings</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          You can customize your notification preferences to control what types of notifications you receive.
        </p>
        <Link
          href="/settings/notifications"
          className="px-4 py-2 bg-nature-green text-white rounded-md hover:bg-nature-green-dark transition-colors inline-block"
        >
          Manage Notification Settings
        </Link>
      </div>
    </div>
  );
}
