'use client';

import React, { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { BADGES } from '@/lib/profile-types';
import { Tooltip } from '@/components/Tooltip';
import { FaPlus, FaTrash, FaSave, FaTimes } from 'react-icons/fa';

interface BadgeManagerProps {
  userId: string;
  userBadges: string[];
  onUpdate?: () => void;
}

export default function BadgeManager({
  userId,
  userBadges = [],
  onUpdate
}: BadgeManagerProps) {
  const [badges, setBadges] = useState<string[]>(userBadges);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Get all available badges
  const allBadges = Object.keys(BADGES);

  // Filter out badges that the user already has
  const availableBadges = allBadges.filter(badge => !badges.includes(badge));

  const handleAddBadge = (badge: string) => {
    setBadges([...badges, badge]);
  };

  const handleRemoveBadge = (badge: string) => {
    setBadges(badges.filter(b => b !== badge));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('Saving badges:', badges);
      console.log('User ID:', userId);

      const supabase = createClientComponentClient();

      // Update the user's badges
      const { data, error } = await supabase
        .from('profiles')
        .update({ badges })
        .eq('id', userId)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Update response:', data);
      setSuccess('Badges updated successfully');
      setIsEditing(false);

      // Force a page reload to reflect changes
      window.location.reload();
    } catch (err: any) {
      console.error('Error updating badges:', err);
      setError(err.message || 'Failed to update badges');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Reset badges to original state
    setBadges(userBadges);
    setIsEditing(false);
    setError(null);
    setSuccess(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">User Badges</h3>

        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors text-sm"
          >
            Manage Badges
          </button>
        ) : (
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="px-3 py-1 bg-nature-green text-white rounded hover:bg-green-700 transition-colors text-sm flex items-center font-medium"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <FaSave className="mr-1" /> Save
                </>
              )}
            </button>
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-sm flex items-center"
            >
              <FaTimes className="mr-1" /> Cancel
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md text-sm">
          {success}
        </div>
      )}

      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-500 mb-2">Current Badges</h4>
        {badges.length === 0 ? (
          <p className="text-gray-500 text-sm italic">No badges assigned yet</p>
        ) : (
          <div className="flex flex-wrap gap-2">
            {badges.map(badge => {
              const badgeInfo = BADGES[badge as keyof typeof BADGES];
              if (!badgeInfo) return null;

              return (
                <div
                  key={badge}
                  className="flex items-center px-3 py-1.5 bg-yellow-50 text-yellow-700 border border-yellow-200 rounded-full text-sm"
                >
                  <Tooltip content={badgeInfo.description || ''}>
                    <span className="flex items-center cursor-help">
                      <span className="mr-1">{badgeInfo.icon || '🏆'}</span> {badgeInfo.name}
                    </span>
                  </Tooltip>

                  {isEditing && (
                    <button
                      onClick={() => handleRemoveBadge(badge)}
                      className="ml-2 text-red-500 hover:text-red-700"
                      aria-label={`Remove ${badgeInfo.name} badge`}
                    >
                      <FaTrash size={12} />
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {isEditing && availableBadges.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-2">Available Badges</h4>
          <div className="flex flex-wrap gap-2">
            {availableBadges.map(badge => {
              const badgeInfo = BADGES[badge as keyof typeof BADGES];
              if (!badgeInfo) return null;

              return (
                <div
                  key={badge}
                  className="flex items-center px-3 py-1.5 bg-gray-50 text-gray-700 border border-gray-200 rounded-full text-sm"
                >
                  <Tooltip content={badgeInfo.description || ''}>
                    <span className="flex items-center cursor-help">
                      <span className="mr-1">{badgeInfo.icon || '🏆'}</span> {badgeInfo.name}
                    </span>
                  </Tooltip>

                  <button
                    onClick={() => handleAddBadge(badge)}
                    className="ml-2 text-green-500 hover:text-green-700"
                    aria-label={`Add ${badgeInfo.name} badge`}
                  >
                    <FaPlus size={12} />
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
