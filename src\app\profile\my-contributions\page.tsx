'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import { getArticlesByUser, deleteArticle } from '@/lib/articles';

type Article = {
  id: string;
  title: string;
  slug: string;
  created_at: string;
  status: string;
};

export default function MyContributionsPage() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const { user, loading: authLoading } = useAuth();

  useEffect(() => {
    async function fetchArticles() {
      if (authLoading) return; // Wait for auth to load

      if (!user) {
        router.push('/auth/signin');
        return;
      }

      setLoading(true);
      setError(null);
      try {
        // Fetch articles by user
        const userArticles = await getArticlesByUser(user.id);
        setArticles(userArticles);
      } catch (err: any) {
        setError('Failed to load your articles.');
      } finally {
        setLoading(false);
      }
    }
    fetchArticles();
  }, [router, user, authLoading]);

  const handleEdit = (slug: string) => {
    router.push(`/contribute/article/edit/${slug}`);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this article?')) return;
    try {
      await deleteArticle(id);
      setArticles(articles.filter(article => article.id !== id));
    } catch {
      alert('Failed to delete article.');
    }
  };

  if (loading) return <div>Loading your contributions...</div>;
  if (error) return <div className="text-red-600">{error}</div>;

  return (
    <div className="max-w-3xl mx-auto py-12">
      <h1 className="text-2xl font-bold mb-6">My Contributions</h1>
      {articles.length === 0 ? (
        <div>You have not submitted any articles yet.</div>
      ) : (
        <table className="min-w-full divide-y divide-gray-200 bg-white">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Title</th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Created</th>
              <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200 text-gray-900">
            {articles.map(article => (
              <tr key={article.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <a
                    href={`/wiki/${article.slug}`}
                    className="text-blue-700 hover:underline font-medium"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {article.title}
                  </a>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{new Date(article.created_at).toLocaleDateString()}</td>
                <td className="px-6 py-4 whitespace-nowrap capitalize">{article.status}</td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    className="text-green-700 hover:underline mr-4 font-semibold"
                    onClick={() => handleEdit(article.slug)}
                  >
                    Edit
                  </button>
                  <button
                    className="text-red-700 hover:underline font-semibold"
                    onClick={() => handleDelete(article.id)}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}