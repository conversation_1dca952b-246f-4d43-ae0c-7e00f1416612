import { NextRequest, NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// Helper function to handle CORS
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}

// GET /api/admin/categories/[id] - Get a specific category
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createServerComponentClient({ cookies: () => cookieStore });

    // Check authentication
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders() }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders() }
      );
    }

    // Get the category
    const { data, error } = await supabase
      .from('categories')
      .select('id, name, slug, description, parent_id')
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return NextResponse.json(
        { error: 'Failed to fetch category: ' + error.message },
        { status: 500, headers: corsHeaders() }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404, headers: corsHeaders() }
      );
    }

    // Add cache control headers to prevent caching
    const headers = {
      ...corsHeaders(),
      'Cache-Control': 'no-store, max-age=0, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    };

    return NextResponse.json(data, { headers });
  } catch (error) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders() }
    );
  }
}

// PUT /api/admin/categories/[id] - Update a category
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createServerComponentClient({ cookies: () => cookieStore });

    // Check authentication
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders() }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders() }
      );
    }

    // Get request body
    const body = await request.json();
    const { name, description, parentId, icon } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400, headers: corsHeaders() }
      );
    }

    // Generate a slug from the name
    const slug = name
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');

    // Check if the category exists first
    const { data: categoryCheck, error: checkError } = await supabase
      .from('categories')
      .select('*')
      .eq('id', params.id)
      .single();

    if (checkError || !categoryCheck) {
      console.error('Category not found for update:', checkError);
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404, headers: corsHeaders() }
      );
    }

    // Check if slug already exists for a different category
    if (slug !== categoryCheck.slug) {
      const { data: slugCheck, error: slugError } = await supabase
        .from('categories')
        .select('id')
        .eq('slug', slug)
        .neq('id', params.id);

      if (!slugError && slugCheck && slugCheck.length > 0) {
        return NextResponse.json(
          { error: 'A category with this name already exists' },
          { status: 400, headers: corsHeaders() }
        );
      }
    }

    // Prepare update data
    const updateData = {
      name,
      slug,
      description: description || null,
      parent_id: parentId || null,
      icon: icon || null,
      updated_at: new Date().toISOString()
    };

    console.log('Updating category with data:', JSON.stringify(updateData));

    // Use the admin_manage_category RPC function to update the category
    // This function is defined with SECURITY DEFINER and can bypass RLS.
    const { data: rpcData, error: rpcError } = await supabase.rpc('admin_manage_category', {
      p_action: 'update',
      p_id: params.id,
      p_name: name,
      p_description: description || null,
      p_parent_id: parentId || null,
      p_icon: icon || null
    });

    if (rpcError) {
      console.error('Error updating category via RPC:', rpcError);
      return NextResponse.json(
        { error: 'Failed to update category: ' + rpcError.message },
        { status: 500, headers: corsHeaders() }
      );
    }

    // Fetch the updated category
    const { data: updatedCategory, error: fetchError } = await supabase
      .from('categories')
      .select('id, name, slug, description, parent_id, icon')
      .eq('id', params.id)
      .single();

    if (fetchError) {
      console.error('Error fetching updated category:', fetchError);
      return NextResponse.json(
        { error: 'Category was updated but could not retrieve the updated data' },
        { status: 500, headers: corsHeaders() }
      );
    }

    console.log('Successfully updated category:', JSON.stringify(updatedCategory));

    // Add cache control headers to prevent caching
    const headers = {
      ...corsHeaders(),
      'Cache-Control': 'no-store, max-age=0, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    };

    // Return the updated category with cache control headers
    return NextResponse.json(updatedCategory, { headers });
  } catch (error) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders() }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders() });
}

// DELETE /api/admin/categories/[id] - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createServerComponentClient({ cookies: () => cookieStore });

    // Check authentication
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders() }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401, headers: corsHeaders() }
      );
    }

    // Check if category has child categories
    const { data: childCategories } = await supabase
      .from('categories')
      .select('id')
      .eq('parent_id', params.id);

    if (childCategories && childCategories.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete a category that has child categories' },
        { status: 400, headers: corsHeaders() }
      );
    }

    // Check if category has articles
    const { data: articles } = await supabase
      .from('articles')
      .select('id')
      .eq('category_id', params.id);

    if (articles && articles.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete a category that has articles' },
        { status: 400, headers: corsHeaders() }
      );
    }

    // Delete the category
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting category:', error);
      return NextResponse.json(
        { error: 'Failed to delete category: ' + error.message },
        { status: 500, headers: corsHeaders() }
      );
    }

    return NextResponse.json({ success: true }, { headers: corsHeaders() });
  } catch (error) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders() }
    );
  }
}
