import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Check RLS status for categories table
    const { data: rlsData, error: rlsError } = await supabase
      .rpc('check_rls_status', { table_name: 'categories' });

    if (rlsError) {
      console.error('Error checking RLS status:', rlsError);
      
      // Try a different approach - query the table directly
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('categories')
        .select('count(*)')
        .single();
        
      if (categoriesError) {
        console.error('Error querying categories table:', categoriesError);
        return NextResponse.json({ 
          error: 'Error checking RLS status', 
          details: {
            rls_error: rlsError,
            categories_error: categoriesError
          }
        }, { status: 500 });
      }
      
      return NextResponse.json({
        message: 'Could not check RLS status directly, but categories table is accessible',
        categories_count: categoriesData.count,
        rls_error: rlsError
      });
    }

    // If we got here, the RPC call worked
    return NextResponse.json({
      rls_status: rlsData,
      message: 'Successfully checked RLS status'
    });
  } catch (error) {
    console.error('Unexpected error in check-rls API:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error', 
      details: error
    }, { status: 500 });
  }
}
