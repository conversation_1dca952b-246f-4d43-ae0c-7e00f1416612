# Check if the categories table exists and has data
$env:PGPASSWORD = $env:SUPABASE_DB_PASSWORD
$supabaseUrl = $env:NEXT_PUBLIC_SUPABASE_URL

if (-not $supabaseUrl) {
    Write-Host "Error: NEXT_PUBLIC_SUPABASE_URL environment variable is not set." -ForegroundColor Red
    exit 1
}

# Extract project ID from the URL
$projectId = $supabaseUrl -replace "https://", "" -replace "\.supabase\.co.*", ""

Write-Host "Checking categories table for project: $projectId" -ForegroundColor Green

# Check if the table exists
$tableExists = psql -h db.$projectId.supabase.co -U postgres -d postgres -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'categories');"

if ($tableExists -match "t") {
    Write-Host "Categories table exists!" -ForegroundColor Green
    
    # Count the records
    $count = psql -h db.$projectId.supabase.co -U postgres -d postgres -t -c "SELECT COUNT(*) FROM categories;"
    Write-Host "Number of categories: $count" -ForegroundColor Green
    
    # Show sample data
    Write-Host "Sample categories:" -ForegroundColor Green
    psql -h db.$projectId.supabase.co -U postgres -d postgres -c "SELECT id, name, slug, description FROM categories LIMIT 5;"
} else {
    Write-Host "Categories table does not exist!" -ForegroundColor Red
    
    # Create the table if it doesn't exist
    Write-Host "Would you like to create the categories table? (y/n)" -ForegroundColor Yellow
    $createTable = Read-Host
    
    if ($createTable -eq "y") {
        psql -h db.$projectId.supabase.co -U postgres -d postgres -c "
        CREATE TABLE IF NOT EXISTS categories (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL,
            slug TEXT NOT NULL UNIQUE,
            description TEXT,
            parent_id UUID REFERENCES categories(id),
            icon TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Add some sample categories
        INSERT INTO categories (name, slug, description, icon)
        VALUES 
            ('Herbal Remedies', 'herbal-remedies', 'Natural plant-based medicines and treatments', 'leaf'),
            ('Nutritional Healing', 'nutritional-healing', 'Healing through food and dietary approaches', 'cake'),
            ('Traditional Medicine', 'traditional-medicine', 'Ancient healing practices from around the world', 'academic-cap');
        "
        
        Write-Host "Categories table created with sample data!" -ForegroundColor Green
    }
}
