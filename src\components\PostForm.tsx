tsx
// Assuming this is within your PostForm.tsx component

import React, { useState, useEffect } from 'react';
import categoriesData from '../../data/categories.json'; // Adjust path if necessary

interface Category {
  name: string;
  slug: string;
  description: string;
}

interface PostFormProps {
  // ... other props ...
  initialCategory?: string; // Optional: for editing existing posts
  onSubmit: (formData: any) => void; // Adjust formData type as needed
}

const PostForm: React.FC<PostFormProps> = ({ /* other props */ initialCategory, onSubmit }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>(initialCategory || '');
  // ... other form state ...

  useEffect(() => {
    // Fetch categories (assuming categories.json is in the same directory)
    setCategories(categoriesData as Category[]);
  }, []);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    // ... other form data ...
    const formData = {
      // ... other form data ...
      category: selectedCategory,
    };
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* ... other form fields ... */}

      <div className="mb-4">
        <label htmlFor="category" className="block text-gray-700 text-sm font-bold mb-2">
          Category
        </label>
        <select
          id="category"
          name="category"
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
        >
          <option value="">Select a category</option>
          {categories.map((category) => (
            <option key={category.slug} value={category.slug}>
              {category.name}
            </option>
          ))}
        </select>
      </div>

      {/* ... other form elements ... */}
    </form>
  );
};

export default PostForm;