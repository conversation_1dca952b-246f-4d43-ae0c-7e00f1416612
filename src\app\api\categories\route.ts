import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Helper function to handle CORS
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-client-info, apikey, X-Client-Info',
  };
}

// Fallback sample categories in case the database is unavailable
const SAMPLE_CATEGORIES = [
  {
    id: '1',
    name: 'Herbal Remedies',
    slug: 'herbal-remedies',
    description: 'Natural plant-based medicines and treatments',
    icon: 'leaf'
  },
  {
    id: '2',
    name: 'Nutritional Healing',
    slug: 'nutritional-healing',
    description: 'Healing through food and dietary approaches',
    icon: 'cake'
  },
  {
    id: '3',
    name: 'Traditional Medicine',
    slug: 'traditional-medicine',
    description: 'Ancient healing practices from around the world',
    icon: 'academic-cap'
  }
];

// GET /api/categories - Get all categories
export async function GET(request: NextRequest) {
  // Add cache control headers
  const headers = {
    ...corsHeaders(),
    'Cache-Control': 'public, max-age=300', // 5 minutes
  };

  try {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json(SAMPLE_CATEGORIES, { headers });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Direct query - simplest approach
    const { data, error } = await supabase
      .from('categories')
      .select('id, name, slug, description, parent_id, icon')
      .order('name');

    if (error || !data || data.length === 0) {
      // Return sample categories if there's an error or no data
      return NextResponse.json(SAMPLE_CATEGORIES, { headers });
    }

    // Return the actual data
    return NextResponse.json(data, { headers });
  } catch (error) {
    // Return sample categories for any unexpected errors
    return NextResponse.json(SAMPLE_CATEGORIES, { headers });
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders() });
}
