import crypto from 'crypto';

/**
 * Generate a Gravatar URL for a given email address
 * @param email The email address to generate a Gravatar URL for
 * @param size The size of the Gravatar image in pixels (default: 200)
 * @param defaultImage The default image to use if no Gravatar is found (default: identicon)
 * @param rating The maximum rating of the Gravatar image (default: g)
 * @returns The Gravatar URL
 */
export function getGravatarUrl(
  email: string,
  size: number = 200,
  defaultImage: 'mp' | 'identicon' | 'monsterid' | 'wavatar' | 'retro' | 'robohash' | 'blank' = 'identicon',
  rating: 'g' | 'pg' | 'r' | 'x' = 'g'
): string {
  if (!email) {
    return `https://www.gravatar.com/avatar/?s=${size}&d=${defaultImage}&r=${rating}`;
  }
  
  // Trim and lowercase the email
  const normalizedEmail = email.trim().toLowerCase();
  
  // Create an MD5 hash of the email
  const hash = crypto.createHash('md5').update(normalizedEmail).digest('hex');
  
  // Return the Gravatar URL
  return `https://www.gravatar.com/avatar/${hash}?s=${size}&d=${defaultImage}&r=${rating}`;
}
