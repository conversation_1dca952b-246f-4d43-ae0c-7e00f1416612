'use client';

import React, { useRef, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';

interface ReCaptchaProps {
  onChange: (token: string | null) => void;
  onExpired?: () => void;
  size?: 'normal' | 'compact';
  className?: string;
}

const RECAPTCHA_SITE_KEY = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'; // Test key

export default function ReCaptchaComponent({ 
  onChange, 
  onExpired, 
  size = 'normal',
  className = ''
}: ReCaptchaProps) {
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  useEffect(() => {
    // Reset on component mount
    return () => {
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
      }
    };
  }, []);

  const handleExpired = () => {
    if (onExpired) {
      onExpired();
    } else {
      onChange(null);
    }
  };

  return (
    <div className={`recaptcha-container ${className}`}>
      <ReCAPTCHA
        ref={recaptchaRef}
        sitekey={RECAPTCHA_SITE_KEY}
        onChange={onChange}
        onExpired={handleExpired}
        size={size}
      />
    </div>
  );
}
